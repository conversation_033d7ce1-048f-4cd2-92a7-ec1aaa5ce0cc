import React, { Component } from 'react'
import { Col, Row, Nav, Tabs, Tab } from 'react-bootstrap';
import Location from './Location';
import Gms from './Gms';
import Activity from './Activity';
import EhsRole from './EhsRole';
import EptwRole from './EptwRole';
import IncidentRole from './IncidentRole';

import PlantRole from './PlantRole';
import InspectionRole from './InspectionRole';
const Dropdown = () => {

    return (
        <>
            <div className="row">
                <div className="col-md-12 grid-margin stretch-card">
                    <div className="card">
                        <div className="card-body">
                            <h4 className="card-title">Dropdown Configuration</h4>
                            {/* <p className="card-description">Horizontal bootstrap tab</p> */}
                            <Tabs defaultActiveKey="location" id="uncontrolled-tab-example">
                                <Tab eventKey="location" title="Location" className="test-tab">
                                    <Location />
                                </Tab>
                                <Tab eventKey="gms" title="GMS">
                                    <Gms />
                                </Tab>
                                <Tab eventKey="activity" title="Work Activities">
                                    <Activity />
                                </Tab>

                                <Tab eventKey="ehs" title="Ehs Roles">
                                    <EhsRole />
                                </Tab>

                                <Tab eventKey="eptw" title="Eptw Roles">
                                    <EptwRole />
                                </Tab>

                                <Tab eventKey="incident" title="Incident Roles">
                                    <IncidentRole />
                                </Tab>

                                <Tab eventKey="Inspection" title="Inspection Roles">
                                    <InspectionRole />
                                </Tab>

                                <Tab eventKey="Plant" title="Plant Roles">
                                    <PlantRole />
                                </Tab>
                            </Tabs>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )

}

export default Dropdown
