import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    LIGHTING_URL,
    SURFACE_CONDITIONS_URL,
    WEATHER_CONDITION_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const WeatherConditionDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.weatherCondition ? incidentData.weatherCondition.id ?? '' : '');

    useEffect(() => {
        setSelected(incidentData.weatherCondition ? incidentData.weatherCondition.id ?? '' : '')
    }, [incidentData.weatherCondition])
    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(WEATHER_CONDITION_URL)
            .then(response => {
                setData(response.data);
            })
            .catch(error => {
                console.error('Error fetching risk categories', error);
            });
    }, []);


    return (
        <div className=''>


            <div className='form-group'>
                <label className=''> Weather Condition <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, weatherConditionId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.name}</option>
                    ))}
                </select>
            </div>



        </div>
    );
};

WeatherConditionDropdown.defaultProps = {
    readOnly: false
}
export default WeatherConditionDropdown;
