import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

const DynamicCBarChart = ({ data, title, total, legend }) => {

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', { month: 'short', year: '2-digit' });
    };

    const [modifiedData, setModifiedData] = useState([])

    useEffect(() => {

        if (data) {
            let cumulativeValue = 0;
            const cumulativeData = data.map(item => {
                cumulativeValue += item.value;
                return { ...item, cumulativeValue };
            });
            setModifiedData(cumulativeData)
        }

    }, [data])
    return (

        <>
            <div className=''>
                <h4 className='mb-3 text-center'>{title}* : {total}</h4>

            </div>

            <ResponsiveContainer width="100%" aspect={4.0 / 2.0}>
                <BarChart data={modifiedData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar name={legend} dataKey="cumulativeValue" fill="green" />
                </BarChart>
            </ResponsiveContainer>
        </>
    );
};

export default DynamicCBarChart;