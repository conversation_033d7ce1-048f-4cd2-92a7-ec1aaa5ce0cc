import moment from 'moment';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
export const tableOptions = (title = '') => {
  return {
    pageSize: 20,
    exportButton: true,
    exportAllData: true,
    exportCsv: (columns, data) => {
      console.log(data)
      const customTitle = title || ''; // Custom title for cell A1
      const filteredData = data.map(row => ({
        firstName: row.firstName,
        email: row.email,
        type: row.type === 'Internal' ? 'STT GDC' : row.company ? row.company : 'External',
      }));
      // Convert the data to CSV format
      const csvContent = `${customTitle}\n${columns.map(column => column.title).join(',')}\n${filteredData.map(row => Object.values(row).join(',')).join('\n')}`;

      // Create a Blob and initiate the download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = 'exported_data.csv';
      link.click();
    },
    actionsColumnIndex: -1,
    actionsCellStyle: {
      padding: '1.125rem 1.375rem',
    },

    headerStyle: {

      padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    },
    rowStyle: {
      // padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    }
  }
}
export const observationColumns = [
  {
    title: "Submitted Date",
    field: "created",
    defaultSort: 'desc',
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => moment(rowData.created).format('Do MMM YYYY hh:mm A')
  },
  {
    title: "ID",
    field: "maskId",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => {
      // Check if dueDate is past the current date
      const isOverdue = moment().isAfter(moment(rowData.dueDate, 'DD-MM-YYYY'));
      return (
        <span style={{ color: isOverdue ? 'red' : 'inherit' }}>
          {rowData.maskId}
        </span>
      );
    }
  },
  {
    title: "Category",
    field: "category",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Type of Observation",
    field: "type",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Description",
    field: "description",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Rectified on the Spot",
    field: "rectifiedStatus",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Reported By",
    field: "submitted.firstName",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Status",
    field: "status",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Action Taken / Action to be Taken",
    field: "actionTaken",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Due Date",
    field: "dueDate",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

];

export const dailyReportColumns = [
  {
    title: "Date",
    field: "date",
    defaultSort: 'desc',
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },

    render: rowData => {
      return `${moment(rowData.date, "DD/MM/YYYY").format("Do MMM YYYY")}`;
    }
  },
  {
    title: "Location",
    field: "location",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`
  },
  {
    title: "# of STT GDC Employees",
    field: "numberOfEmployees",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Daily Hours Worked of STT GDC Employee",
    field: "dailyHoursOfEmployee",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "# of Contractor Employees",
    field: "numberofContractors",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Daily Hours Worked of Contractor Employee",
    field: "dailyHoursOfContractors",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },


];

export const monthlyReportColumns = [
  {
    title: "Year and Month",
    field: "yearAndMonth",
    defaultSort: 'desc',
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
  },
  {
    title: "Location",
    field: "location",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`
  },
  {
    title: "Reported By",
    field: "user.firstName",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
  },
  {
    title: "# of STT GDC Employees",
    field: "numberOfEmployees",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Total Hours Worked of STT GDC Employee",
    field: "dailyHoursOfEmployee",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Total Working Days for STT GDC Employee",
    field: "workingDaysOfEmployee",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "# of Contractor Employees",
    field: "numberofContractors",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Daily Hours Worked of Contractor Employee",
    field: "dailyHoursOfContractors",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Total Working Days for Contractors",
    field: "workingDaysOfContractors",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "# of Safety Inductions",
    field: "noOfSafety",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "# of Toolbox Meetings/Safety Briefings/Safe Starts",
    field: "noOfToolbox",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "# of EHS Trainings",
    field: "noOfEhsTraining",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "# of EHS Inspections / Audits",
    field: "noOfInspection",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "# of Site Walk / Inspection",
    field: "noOfManagmentSiteWalk",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "# of Authority/NGO/Union Visits",
    field: "noOfAuthority",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },


];

export const userColumns = [

  {
    title: "Name",
    field: "firstName",
    cellStyle: {
      padding: '1.125rem 1.375rem',
      width: '30%',
      maxWidth: '30%'
    }
  },
  {
    title: "Email",
    field: "email",
    cellStyle: {
      padding: '1.125rem 1.375rem',
      width: '30%',
      maxWidth: '30%'
    }
  },
  {
    title: "Organization",
    field: "company",
    cellStyle: {
      padding: '1.125rem 1.375rem',
      width: '30%',
      maxWidth: '30%'
    },
    render: rowData => rowData.type === 'Internal' ? 'STT GDC' : (rowData.company ? rowData.company : 'External')
  },
  {
    title: "Country",
    field: "country",
    cellStyle: {
      padding: '1.125rem 1.375rem',
      width: '50%',
      maxWidth: '50%'
    },
    editComponent: props => (
      <FormControl>
        <InputLabel id="country-select-label">Country</InputLabel>
        <Select
          labelId="country-select-label"
          id="country-select"
          value={props.value}
          onChange={e => props.onChange(e.target.value)}
        >
          <MenuItem value="Singapore">Singapore</MenuItem>
          <MenuItem value="India">India</MenuItem>
          <MenuItem value="Thailand">Thailand</MenuItem>
          <MenuItem value="Korea">Korea</MenuItem>
          <MenuItem value="UK">UK</MenuItem>
          <MenuItem value="Philippines">Philippines</MenuItem>
        </Select>
      </FormControl>)
  }
];

export const eptwColumns = [
  {
    title: "ID",
    field: "maskId",
    defaultSort: 'desc',
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },
  {
    title: "Type of Permit",
    field: "permitType",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    }
  },

  {
    title: "Location",
    field: "location",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''} > ${rowData.locationFive?.name || ''} > ${rowData.locationSix?.name || ''}`
  },

  {
    title: "Closure Status",
    field: "closure",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => rowData.closure ? (rowData.closure.status ? rowData.closure.status : 'N/A') : 'N/A'
  },

  {
    title: "Closeout Date",
    field: "closure",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => rowData.closure && rowData.closure.closeoutDate
      ? moment(rowData.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A')
      : 'N/A'
  },

  {
    title: "Submitted By",
    field: "applicant.firstName",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => rowData.applicant ? rowData.applicant.firstName : ''
  },

  {
    title: "Status",
    field: "status",
    cellStyle: {
      padding: '1.125rem 1.375rem',
    },
    render: rowData => rowData.status ? rowData.status : 'N/A'
  },

];