import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON>, LabelList } from 'recharts';

const generateRandomData = () => {
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    return days.map(day => ({
        date: day.toString(),
        Count: Math.floor(Math.random() * 100), // You can modify the logic to combine your three values into one if needed
    }));
};

const BarSix = ({ type }) => {
    const [data, setData] = useState(generateRandomData());

    return (
        <ResponsiveContainer width="100%" height={500}>
            <BarChart data={data}>
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend verticalAlign="bottom" align="center" />

                <Bar dataKey="Count" fill="#5B8FF7" barSize={30}>
                    <LabelList dataKey="Count" position="insideTop" />
                </Bar>
            </BarChart>
        </ResponsiveContainer>
    );
};

export default BarSix;
