import React from 'react';
import Editable from "react-bootstrap-editable";
const LocationList = (props) => {
   


    return (
        <>
        {console.log(props.lists)}
            <div>
                {props.lists.map(i => {
                    return (
                        <div key={i.value} className="list d-flex justify-content-between align-items-center py-3 border-bottom">

                            <div className="content pencil-icon">
                               
                                <Editable initialValue={i.label} onSubmit={(value) => props.onLocationEdit(value, i.value)} className="d-flex" mode="inline" />
                                {/* <p className="list_text">Hello, last date for registering for the annual music event is closing in</p> */}
                            </div>
                            <div className="options">

                                { i.editable && <i className="mdi mdi-delete text-danger cursor-pointer" onClick={() => {props.handleDelete(i.value)}}></i>}

                            </div>
                        </div>
                    )
                })}

            </div>
        </>
    )
}
export default LocationList;