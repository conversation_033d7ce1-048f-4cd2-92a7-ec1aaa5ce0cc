/* InspectionReport.css - Scoped styles for InspectionReport component */

.inspection-report-container {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  margin: 0;
  padding: 2rem;
  color: #333;
}

.inspection-report-page-container {
  /* max-width: 900px; */
  margin: auto;
  background: #fff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border-radius: 12px;
  overflow: hidden;
  padding: 2rem;
}

/* Header Styles */
.inspection-report-header-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.inspection-report-header-title {
  font-weight: 600;
  font-size: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.inspection-report-header-title::before {
  content: "🗎";
  font-size: 20px;
  margin-right: 8px;
}

.inspection-report-status-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #28a745;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(254, 255, 254, 0.3);
}

.inspection-report-status-badge::before {
  content: "✓";
  margin-right: 6px;
  font-size: 16px;
}

.inspection-report-status-badge.completed {
  background-color: #28a745;
}

.inspection-report-status-badge.in-progress {
  background-color: #17a2b8;
}

.inspection-report-status-badge.pending {
  background-color: #ffc107;
  color: #212529;
}

.inspection-report-status-badge.overdue {
  background-color: #dc3545;
}

.inspection-report-timestamp {
  position: absolute;
  top: 35px;
  right: 0;
  font-size: 10px;
  color: #666;
  font-weight: normal;
  display: flex;
  align-items: center;
}

.inspection-report-timestamp::before {
  content: "🕒";
  margin-right: 4px;
  font-size: 12px;
}

.inspection-report-header-section {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.inspection-report-header-block {
  margin-bottom: 15px;
}

.inspection-report-header-block.full-width {
  grid-column: 1 / -1;
}

.inspection-report-header-label {
  font-size: 10px;
  color: #888;
  margin-bottom: 4px;
}

.inspection-report-header-value {
  font-weight: 600;
  font-size: 12px;
}

/* Areas Layout Styles */
.inspection-report-areas-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.inspection-report-areas-section {
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.inspection-report-areas-section:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.inspection-report-areas-header {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 2px solid #dee2e6;
  display: flex;
  align-items: center;
}

.inspection-report-areas-header.included {
  color: #0066cc;
  border-bottom-color: #0066cc;
}

.inspection-report-areas-header.not-included {
  color: #ff6600;
  border-bottom-color: #ff6600;
}

.inspection-report-areas-header::before {
  font-size: 18px;
  margin-right: 8px;
}

.inspection-report-areas-header.included::before {
  content: "✓";
}

.inspection-report-areas-header.not-included::before {
  content: "✕";
}

.inspection-report-areas-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.inspection-report-areas-list li {
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  font-weight: 500;
}

.inspection-report-areas-list li:hover {
  transform: translateX(2px);
}

.inspection-report-areas-list.included li {
  background-color: #e6f3ff;
  color: #004080;
  border-left: 4px solid #0066cc;
}

.inspection-report-areas-list.not-included li {
  background-color: #fff0e6;
  color: #cc4400;
  border-left: 4px solid #ff6600;
}

.inspection-report-areas-list li::before {
  margin-right: 8px;
  font-weight: bold;
}

.inspection-report-areas-list.included li::before {
  content: "●";
  color: #0066cc;
}

.inspection-report-areas-list.not-included li::before {
  content: "●";
  color: #ff6600;
}

.inspection-report-section-title {
  font-size: 16px;
  margin: 15px 0 6px;
  font-weight: bold;
}

.inspection-report-tabs {
  display: flex;
  margin: 20px 0 10px;
}

.inspection-report-tab {
  padding: 6px 15px;
  font-size: 13px;
  border-radius: 4px;
  margin-right: 6px;
  color: white;
}

.inspection-report-tab.green {
  background-color: #2ecc71;
}

.inspection-report-tab.blue {
  background-color: #27ae60;
}

.inspection-report-tab.orange {
  background-color: #f39c12;
}

.inspection-report-tab.red {
  background-color: #e74c3c;
}

.inspection-report-tab.gray {
  background-color: #95a5a6;
}

.inspection-report-checklist-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 8px;
  background-color: #fff;
  font-size: 11px;
}

.inspection-report-question-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.inspection-report-checklist-text {
  font-weight: 500;
  flex: 1;
  margin-right: 10px;
  line-height: 1.4;
}

.inspection-report-response {
  flex-shrink: 0;
  align-self: flex-start;
}

.inspection-report-remarks-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.inspection-report-btn {
  padding: 3px 8px;
  border-radius: 5px;
  font-size: 10px;
  border: 1px solid #ccc;
  margin-left: 5px;
  cursor: default;
}

.inspection-report-btn.yes {
  background-color: #e6f4ea;
  color: #28a745;
  border-color: #c3e6cb;
}

.inspection-report-btn.no {
  background-color: #fcebea;
  color: #dc3545;
  border-color: #f5c6cb;
}

.inspection-report-btn.na {
  background-color: #e2e3e5;
  color: #6c757d;
  border-color: #d6d8db;
}

.inspection-report-remark-label {
  font-size: 12px;
  color: #666;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.inspection-report-remark-label::before {
  content: "ⓘ";
  font-size: 14px;
  margin-right: 5px;
}

.inspection-report-remark-box {
  margin-top: 5px;
  padding: 10px;
  background-color: #fcfcfc;
  border: 1px solid #eee;
  border-radius: 5px;
  color: #555;
  font-style: italic;
}

.inspection-report-action-card {
  border: 1px solid #e0e0e0;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inspection-report-action-info {
  flex-grow: 1;
}

.inspection-report-action-id {
  font-size: 10px;
  color: #666;
  margin-bottom: 3px;
}

.inspection-report-action-title {
  font-weight: bold;
  font-size: 12px;
}

.inspection-report-action-meta {
  font-size: 10px;
  color: #444;
  margin-top: 5px;
}

.inspection-report-action-status {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 12px;
  display: inline-block;
  white-space: nowrap;
}

.inspection-report-action-status.completed {
  background-color: #d4edda;
  color: #155724;
}

.inspection-report-action-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

/* New Action Structure Styles */
.inspection-report-action-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
  background-color: #fff;
  overflow: hidden;
}

.inspection-report-action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.inspection-report-action-header-left {
  flex-grow: 1;
}

.inspection-report-action-id {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.inspection-report-action-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.inspection-report-action-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.inspection-report-action-badge.assigned {
  background-color: #cce5ff;
  color: #004085;
}

.inspection-report-action-badge.reassigned {
  background-color: #fff3cd;
  color: #856404;
}

.inspection-report-action-badge.pending {
  background-color: #f8d7da;
  color: #721c24;
}

.inspection-report-action-question {
  padding: 15px 20px;
  background-color: #f0f8ff;
  border-bottom: 1px solid #e0e0e0;
}

.inspection-report-action-question-group {
  font-size: 14px;
  font-weight: bold;
  color: #0066cc;
  margin-bottom: 5px;
}

.inspection-report-action-question-text {
  font-size: 13px;
  color: #333;
  font-style: italic;
}

.inspection-report-action-detail {
  padding: 20px;
}

.inspection-report-action-section {
  margin-bottom: 15px;
}

.inspection-report-action-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.inspection-report-action-content {
  font-size: 13px;
  color: #555;
  line-height: 1.5;
  margin-bottom: 10px;
}

.inspection-report-action-meta-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 15px;
}

.inspection-report-action-meta-col {
  display: flex;
  flex-direction: column;
}

.inspection-report-action-label {
  font-size: 12px;
  font-weight: bold;
  color: #666;
  margin-bottom: 4px;
}

.inspection-report-action-value {
  font-size: 13px;
  color: #333;
}

/* Uploads Section Styles */
.inspection-report-uploads-section {
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.inspection-report-uploads-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.inspection-report-uploads-title::before {
  content: "📎";
  margin-right: 8px;
  font-size: 16px;
}

.inspection-report-uploads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.inspection-report-upload-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.inspection-report-file-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #0066cc;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.inspection-report-file-link:hover {
  color: #004499;
  text-decoration: underline;
}

.inspection-report-unsupported-file {
  font-size: 11px;
  color: #666;
  text-align: center;
}

/* Loading Styles */
.inspection-report-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;
}

.inspection-report-loading-text {
  font-size: 16px;
  color: #666;
  margin-top: 10px;
}

.inspection-report-loading::before {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0066cc;
  border-radius: 50%;
  animation: inspection-report-spin 1s linear infinite;
}

@keyframes inspection-report-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Export Controls */
.inspection-report-export-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.inspection-report-export-btn {
  background: linear-gradient(135deg, #0066cc, #004499);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  min-width: 160px;
  justify-content: center;
}

.inspection-report-export-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #004499, #002266);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 102, 204, 0.3);
}

.inspection-report-export-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.inspection-report-export-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: inspection-report-spin 1s linear infinite;
}

/* Export Image Styles */
.inspection-report-export-image {
  max-width: 100%;
  max-height: 150px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* A4 Page Formatting */
@page {
  size: A4;
  margin: 20mm;
}

/* Additional responsive styles */
@media screen and (max-width: 768px) {
  .inspection-report-header-section {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .inspection-report-areas-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .inspection-report-page-container {
    padding: 1rem;
    margin: 1rem;
  }

  .inspection-report-action-meta-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .inspection-report-action-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .inspection-report-uploads-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .inspection-report-upload-item {
    min-height: 80px;
  }

  .inspection-report-export-controls {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .inspection-report-export-btn {
    width: 100%;
    min-width: auto;
  }
}

/* Print styles */
@media print {
  .inspection-report-container {
    margin: 0;
    padding: 15mm;
    font-size: 11px;
    width: 210mm;
    min-height: 297mm;
    box-sizing: border-box;
    background: white;
    line-height: 1.4;
  }

  .inspection-report-page-container {
    max-width: 170mm;
    margin: 0 auto;
    background: white;
    box-shadow: none;
  }

  .inspection-report-status-badge {
    box-shadow: none;
    font-size: 12px;
    padding: 6px 12px;
  }

  .inspection-report-timestamp {
    font-size: 10px;
  }

  .inspection-report-areas-section {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .inspection-report-action-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .inspection-report-checklist-item {
    break-inside: avoid;
  }

  .inspection-report-header-container {
    margin-bottom: 15px;
  }

  .inspection-report-section-title {
    margin: 15px 0 6px;
  }

  /* Scale down fonts for A4 */
  .inspection-report-header-title {
    font-size: 20px;
  }

  .inspection-report-section-title {
    font-size: 16px;
    margin: 20px 0 8px;
  }

  .inspection-report-header-label {
    font-size: 10px;
  }

  .inspection-report-header-value {
    font-size: 12px;
  }

  .inspection-report-checklist-item {
    font-size: 11px;
    padding: 10px;
    margin-bottom: 8px;
  }

  .inspection-report-btn {
    font-size: 10px;
    padding: 3px 8px;
  }

  .inspection-report-action-card {
    padding: 10px;
    margin-bottom: 8px;
  }

  .inspection-report-action-id {
    font-size: 10px;
  }

  .inspection-report-action-title {
    font-size: 12px;
  }

  .inspection-report-action-meta {
    font-size: 10px;
  }

  .inspection-report-areas-container {
    gap: 20px;
    margin: 15px 0;
    padding: 15px;
  }

  .inspection-report-areas-section {
    padding: 15px;
  }

  .inspection-report-areas-header {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .inspection-report-areas-list li {
    font-size: 11px;
    padding: 6px 10px;
    margin-bottom: 4px;
  }

  .inspection-report-action-container {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
    margin-bottom: 15px;
  }

  .inspection-report-action-header {
    padding: 10px 15px;
  }

  .inspection-report-action-detail {
    padding: 15px;
  }

  .inspection-report-action-id {
    font-size: 14px;
  }

  .inspection-report-action-badge {
    font-size: 10px;
    padding: 4px 8px;
  }

  .inspection-report-action-title {
    font-size: 12px;
  }

  .inspection-report-action-content {
    font-size: 11px;
  }

  .inspection-report-action-meta-row {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .inspection-report-uploads-section {
    break-inside: avoid;
    margin: 10px 0;
    padding: 10px;
  }

  .inspection-report-uploads-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }

  .inspection-report-upload-item {
    min-height: 60px;
    padding: 8px;
  }

  .inspection-report-file-link {
    font-size: 10px;
  }

  /* Hide export controls in print */
  .inspection-report-export-controls {
    display: none !important;
  }

  .inspection-report-export-image {
    max-height: 120px;
  }
}
