import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    LOCATION1_URL,
    LOCATION_TIER1_URL,
    TIER1_TIER2_URL,
    TIER2_TIER3_URL,
    TIER3_TIER4_URL,
    TIER4_TIER5_URL
} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const LocationDropdown = ({ incidentData, setIncidentData, readOnly }) => {

    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [locationFour, setLocationFour] = useState([]);
    const [locationFive, setLocationFive] = useState([]);
    const [locationSix, setLocationSix] = useState([]);

    const [selectedLocationOne, setSelectedLocationOne] = useState(incidentData.locationOne.id);
    const [selectedLocationTwo, setSelectedLocationTwo] = useState(incidentData.locationTwo.id);
    const [selectedLocationThree, setSelectedLocationThree] = useState(incidentData.locationThree.id);
    const [selectedLocationFour, setSelectedLocationFour] = useState(incidentData.locationFour.id);
    const [selectedLocationFive, setSelectedLocationFive] = useState(incidentData.locationFive?.id || '');
    const [selectedLocationSix, setSelectedLocationSix] = useState(incidentData.locationSix?.id || '');


    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(LOCATION1_URL)
            .then(response => {
                setLocationOne(response.data);
            })
            .catch(error => {
                console.error('Error fetching location-one options', error);
            });
    }, []);

    useEffect(() => {
        if (selectedLocationOne) {
            axiosInstance.get(LOCATION_TIER1_URL(selectedLocationOne))
                .then(response => {
                    setLocationTwo(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-two options', error);
                });
        }
    }, [selectedLocationOne]);

    useEffect(() => {
        if (selectedLocationTwo) {
            axiosInstance.get(TIER1_TIER2_URL(selectedLocationTwo))
                .then(response => {
                    setLocationThree(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-three options', error);
                });
        }
    }, [selectedLocationTwo]);

    useEffect(() => {
        if (selectedLocationThree) {
            axiosInstance.get(TIER2_TIER3_URL(selectedLocationThree))
                .then(response => {
                    setLocationFour(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-four options', error);
                });
        }
    }, [selectedLocationThree]);

    useEffect(() => {
        if (selectedLocationFour) {
            axiosInstance.get(TIER3_TIER4_URL(selectedLocationFour))
                .then(response => {
                    setLocationFive(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-five options', error);
                });
        }
    }, [selectedLocationFour]);

    useEffect(() => {
        if (selectedLocationFive) {
            axiosInstance.get(TIER4_TIER5_URL(selectedLocationFive))
                .then(response => {
                    setLocationSix(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-six options', error);
                });
        }
    }, [selectedLocationFive]);

    return (

        <div className='form-group'>
            <label>Site Details</label>
            <div className='d-flex'>


                <select className="form-select me-2" disabled={readOnly} value={selectedLocationOne} onChange={(e) => { setSelectedLocationOne(e.target.value); setIncidentData((prev) => ({ ...prev, locationOneId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationOne.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

                {/* Location Two Dropdown */}
                <select className="form-select me-2" disabled={readOnly} value={selectedLocationTwo} onChange={(e) => { setSelectedLocationTwo(e.target.value); setIncidentData((prev) => ({ ...prev, locationTwoId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationTwo.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

                {/* Location Three Dropdown */}
                <select className="form-select me-2" disabled={readOnly} value={selectedLocationThree} onChange={(e) => { setSelectedLocationThree(e.target.value); setIncidentData((prev) => ({ ...prev, locationThreeId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationThree.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

                {/* Location Four Dropdown */}
                <select className="form-select me-2" disabled={readOnly} value={selectedLocationFour} onChange={(e) => { setSelectedLocationFour(e.target.value); setIncidentData((prev) => ({ ...prev, locationFourId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationFour.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

                {/* Location Five Dropdown */}



                <select className="form-select me-2" disabled={readOnly} value={selectedLocationFive} onChange={(e) => { setSelectedLocationFive(e.target.value); setIncidentData((prev) => ({ ...prev, locationFiveId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationFive.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

                {/* Location Six Dropdown */}
                <select className="form-select me-2" disabled={readOnly} value={selectedLocationSix} onChange={(e) => { setSelectedLocationSix(e.target.value); setIncidentData((prev) => ({ ...prev, locationSixId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationSix.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>

            </div>
        </div>

    );
};

LocationDropdown.defaultProps = {
    readOnly: false
}

export default LocationDropdown;