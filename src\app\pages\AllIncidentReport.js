import React, { useEffect, useState, useMemo } from 'react'
import API from '../services/API';
import { Modal, Button, Form } from 'react-bootstrap';
import { useHistory } from "react-router-dom";
import { ALL_INCIDENT_URL, FILE_URL, STATIC_URL } from '../constants';
import GalleryPage from '../apps/Gallery';
import { useSelector } from "react-redux";
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { DropzoneArea } from 'material-ui-dropzone';
import IncidentTriggerModal from './IncidentTriggerModal';
import AllFilterLocation from './AllLocationFilter';
import IncidentInformationModal from './IncidentInformationModal';
import ActionTable from './ActionTable';
import moment from 'moment';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Button as Button1 } from 'primereact/button';
import extractImpactLevel from '../utils/impactLevel';
const AllIncidentReport = ({ incident, getIncidentData, setRendered }) => {
    
    const [type, setType] = useState('All')
    const [readOnly, setReadOnly] = useState(true)
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [showOverdue, setShowOverdue] = useState(false);
    const [current, setCurrent] = useState([])
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        maskId: { value: null, matchMode: FilterMatchMode.IN },
        IncidentCategory: { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        type: { value: null, matchMode: FilterMatchMode.IN },
        'user.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'reviewer.firstName': { value: null, matchMode: FilterMatchMode.IN },
        'incidentOwner.firstName': { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
        color: { value: null, matchMode: FilterMatchMode.EQUALS },
        created: { value: null, matchMode: FilterMatchMode.CUSTOM },
    });
    const user1 = useSelector((state) => state.login.user);
    const history = useHistory();
    const me = useSelector((state) => state.login.user)
    const data1 = useSelector((state) => state.login.user);
    const validationRoles = data1?.validationRoles || [];

    const [selectedFiles, setSelectedFiles] = useState([]);

    const isIncidentTrigger = useMemo(() => {
        console.log(me?.validationRoles)
        return me?.validationRoles?.some(item => item.name?.includes('Incident Owner') || item.name === 'Group EHS Team' || item.name === 'Country EHS Director') || false;
    }, [me]);

    const defaultMaterialTheme = createTheme();
    const tableOptions = {
        actionsColumnIndex: -1,
        actionsCellStyle: {
            padding: '1.125rem 1.375rem',
        },
        pageSize: 20,
        headerStyle: {

            padding: '1.125rem 1.375rem',
            fontSize: '0.812rem'
        },
        rowStyle: {
            // padding: '1.125rem 1.375rem',
            fontSize: '0.812rem'
        }
    }

    const [actionModal, setActionModal] = useState(false)
    const [actionData, setActionData] = useState([])
    const [maskId, setMaskId] = useState(null)
    const [totalAction, setTotalAction] = useState([])
    const [data, setData] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [triggerShowModal, setTriggerShowModal] = useState(false)
    const [showModal, setShowModal] = useState(false)
    const [showAttach, setShowAttach] = useState(false)
    const [user, setUser] = useState(false)
    const [currentIncident, setCurrentIncident] = useState('')
    const [owner, setOwner] = useState(false)
    const [reviewer, setReviewer] = useState(false)
    const [incidentId, setIncidentId] = useState('')
    const [selectedAttachments, setSelectedAttachments] = useState([])
    const handleActionCard = (id, actions, incidentId) => {

        console.log(actions)
        setCurrent(incidentId)
        setActionModal(true)
        // setActionData(data)
        setMaskId(id)
        setTotalAction(actions)
    }
    const [triggerInvestigationId, setTriggerInvestigationId] = useState(false)

    const handleTriggerInvestigation = (id) => {
        setTriggerInvestigationId(id)
        setTriggerShowModal(true)
    }

    const handleFileChange = (files) => {
        if (files.length === 0) return; // Ensure a file is selected
        setSelectedFiles(files);
    };

    const handleUpload = async () => {
        if (selectedFiles.length === 0) {
            alert("Please select files to upload.");
            return;
        }

        console.log("Uploading files:", selectedFiles);

        const formData = new FormData();
        selectedFiles.forEach(file => {
            formData.append("files", file); // Append all files
        });

        try {
            // Step 1: Upload files
            const fileResponse = await API.post(FILE_URL, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });

            if (fileResponse.status === 200) {
                const uploadedFileNames = fileResponse.data.files.map(file => file.originalname);
                console.log("Files uploaded successfully:", uploadedFileNames);

                // Step 2: Send PATCH request to update the incident record
                const patchResponse = await API.patch(`/report-incidents-additional-files/${selectedAttachments.id}`, {
                    additionalDocuments: uploadedFileNames, // Pass all uploaded file names
                });

                if (patchResponse.status === 204) {
                    console.log("Files successfully patched to incident.");
                    setSelectedFiles([]); // Clear selected files
                    window.location.reload(); // Reload page after successful upload
                } else {
                    console.error("Failed to patch files:", patchResponse.status);
                }
            }
        } catch (error) {
            console.error("File upload or patch failed:", error);
        }
    };


    useEffect(() => {


        getIncidentData()
    }, [showModal, triggerShowModal])

    useEffect(() => {
        // getIncidentData();

        const obs = incident.map(item => {
            return { name: item.user.firstName, value: item.user.firstName }
        })
        setUser(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs1 = incident.map(item => {
            return { name: item.incidentOwner?.firstName, value: item.incidentOwner?.firstName }
        })
        setOwner(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const obs2 = incident.map(item => {
            return { name: item.reviewer?.firstName, value: item.reviewer?.firstName }
        })
        setReviewer(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        // Transform status from "Tracked" to "Reviewed"
        const updatedIncident = incident.map(item => ({
            ...item,
            status: item.status === "Tracked" ? "Reviewed" : item.status
        }))

        setData(updatedIncident)
        setFilterData(updatedIncident)
    }, [incident])





    const viewIncident = async (id) => {
        setCurrentIncident(id);
        setShowModal(true)
    }




    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-between'>

            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };
    const impactBodyTemplate = (rowData) => {
        return `${rowData.actualImpact} ${rowData.potentialImpact && `(${extractImpactLevel(rowData.actualImpact)})`}`

    }
    function groupByDescription(data) {
        const groupedData = [];
        const descriptionMap = {};

        data?.forEach(item => {
            const { description, actionType, assignedToId, status } = item;
            if (!descriptionMap[description]) {
                descriptionMap[description] = {
                    description: description,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType], // Initialize array with current actionType
                    lastAssignedToId: assignedToId, // Initialize with current assignedToId
                    lastStatus: status, // Initialize with current status
                    data: []
                };
            } else {
                // Update lastActionType with current actionType
                descriptionMap[description].lastActionType = actionType;
                // Add current actionType to the array
                descriptionMap[description].actionTypes.push(actionType);
                // Update lastAssignedToId with current assignedToId
                descriptionMap[description].lastAssignedToId = assignedToId;
                // Update lastStatus with current status
                descriptionMap[description].lastStatus = status;
            }
            descriptionMap[description].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1]; // Get the last data object
            group.lastActionType = lastDataObject.actionType; // Update lastActionType
            group.lastAssignedToId = lastDataObject.assignedToId; // Update lastAssignedToId
            group.lastStatus = lastDataObject.status; // Update lastStatus
            groupedData.push(group);
        }


        return groupedData.filter(item => item.lastActionType !== 'verify_investigation_actions');
    }

    const actionBodyTemplate = (rowData) => {


        const totalActionData = groupByDescription(rowData.incidentData.totalActions)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';





        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleActionCard(rowData.maskId, totalActionData, rowData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;



    }


    const investBodyTemplate = (rowData) => {
        return rowData.investigationStatus ? (
            <span>{rowData.investigator?.firstName}</span> // Display text "Triggered" when investigationStatus is true
        ) : (
            <span>
                <i className="mdi mdi-account-clock icon-size cursor-pointer" onClick={() => handleTriggerInvestigation(rowData.id)}></i>
            </span>
            // <> - </>
        );
    }
    const maskIdBodyTemplate = (rowData) => {
        return (
            <div className='maskid' onClick={() => viewIncident(rowData.id)}>
                {rowData.maskId}
            </div>
        );
    }

    const categoryFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={[{ name: 'Safety', value: 'Safety' }, { name: 'Health', value: 'Health' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={[{ name: 'Reviewed', value: 'Reviewed' }, { name: 'Investigated', value: 'Investigated' }, { name: 'Under Investigation', value: 'Under Investigation' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const reportFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={user} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const ownerFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={owner} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const reviewerFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={reviewer} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const sortDate = (e) => {

        if (e.order === 1) {
            return e.data.sort((a, b) => {
                // Parse the dates using Moment.js
                const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);
                const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            });
        } else {

            return e.data.sort((a, b) => {
                // Parse the dates using Moment.js
                const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY',]);
                const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY',]);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            }).reverse()
        }
    }
    const openModal = (attachments) => {
        console.log(attachments)
        setSelectedAttachments(attachments);
        setShowAttach(true);
    };
    const attachmentBodyTemplate = (rowData) => {
        if (
            validationRoles.some(role => role.name === 'Country EHS Director') ||
            user1.id === rowData.incidentOwnerId ||
            user1.id === rowData.userId ||
            user1.id === rowData.reviewerId
        ) {
            return (
                <Button variant="link" onClick={() => openModal(rowData)}>
                    Add / View attachment(s)
                </Button>
            );
        }
        return null;
    };

    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    };
    // const handleFileChange = async (e) => {
    //     if (e.length === 0) return; // Ensure a file is selected

    //     console.log("Uploading file:", e[e.length - 1]);

    //     const formData = new FormData();
    //     formData.append("file", e[e.length - 1]);

    //     try {
    //         // Step 1: Upload file
    //         const fileResponse = await API.post(FILE_URL, formData, config);

    //         if (fileResponse.status === 200) {
    //             const uploadedFileName = fileResponse.data.files[0].originalname; // Get uploaded file name
    //             console.log("File uploaded successfully:", uploadedFileName);

    //             // Ensure additionalDocuments is an array
    //             const updatedFiles = [uploadedFileName];

    //             // Step 2: Send PATCH request with updated additionalDocuments array
    //             const patchResponse = await API.patch(`/report-incidents-additional-files/${selectedAttachments.id}`, {
    //                 additionalDocuments: updatedFiles,
    //             });

    //             if (patchResponse.status === 204) {
    //                 console.log("File successfully patched to incident:", patchResponse.data);
    //             } else {
    //                 console.error("Failed to patch file:", patchResponse.status);
    //             }
    //         }
    //     } catch (error) {
    //         console.error("File upload or patch failed:", error);
    //     }
    // };
    const removeFile = (index) => {
        const updatedFiles = [...selectedFiles];
        updatedFiles.splice(index, 1); // Remove the selected file
        setSelectedFiles(updatedFiles);
    };
    return (
        <>

            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="">
                            <div className="">


                                <div className="row">
                                    <div className="col-12">
                                        <div>


                                            <DataTable value={filterData} paginator rows={10} header={header} filters={filters} onFilter={(e) => { setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                rowsPerPageOptions={[10, 25, 50]}
                                                emptyMessage="No Data found." >

                                                <Column field='maskId' header="ID" sortable body={maskIdBodyTemplate} showFilterMatchModes={false}></Column>

                                                <Column field='incidentDate' header="Incident Date" sortable showFilterMatchModes={false} sortFunction={sortDate}></Column>

                                                <Column field='title' header="Incident Title" showFilterMatchModes={false}></Column>

                                                <Column field='IncidentCategory' header="Category" filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

                                                <Column field='actualImpact' body={impactBodyTemplate} header="Impact Classification" ></Column>

                                                <Column field='status' header="Current Status" filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column>
                                                <Column field='incidentOwner.firstName' header="Incident Owner" filter filterElement={ownerFilterTemplate} showFilterMatchModes={false}></Column>

                                                <Column field='user.firstName' header="Reported By" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                                                <Column field='reviewer.firstName' header="Reviewed By" filter filterElement={reviewerFilterTemplate} showFilterMatchModes={false}></Column>

                                                <Column header="Actions Taken" body={actionBodyTemplate} headerStyle={{ width: '15%' }}></Column>
                                                {isIncidentTrigger &&
                                                    <Column header="Investigation Status" body={investBodyTemplate} sortable headerStyle={{ width: '16%' }}></Column>
                                                }

                                                <Column header="Attachment" body={(rowData) => attachmentBodyTemplate(rowData)} headerStyle={{ width: '12%' }}></Column>

                                            </DataTable>


                                            {/* <DataTables thead={thead} options={options} /> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {(currentIncident && showModal) && <IncidentInformationModal readOnly={readOnly} type={type} setReadOnly={setReadOnly} setType={setType} id={currentIncident} showModal={showModal} setShowModal={setShowModal} />}
            {(triggerInvestigationId && triggerShowModal) && <IncidentInformationModal readOnly={true} type={'Trigger'} id={triggerInvestigationId} showModal={triggerShowModal} setShowModal={setTriggerShowModal} />}
            {/* <IncidentTriggerModal triggerShowModal={triggerShowModal} setTriggerShowModal={setTriggerShowModal} incidentId={triggerInvestigationId} /> */}
            <Modal
                show={actionModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>

                    <ActionTable totalActions={actionData} id={maskId} actions={totalAction} current={current} />
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setActionModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>


            <Modal show={showAttach} onHide={() => setShowAttach(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Add/view attachment(s)</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <DropzoneArea
                        acceptedFiles={[]}
                        dropzoneText={"Drag and Drop / Upload Evidence"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        showPreviewsInDropzone={false}

                    />

                    <div className="mt-3">
                        {selectedFiles.length > 0 && (
                            <h5>File Previews</h5>
                        )}

                        <div className="row">
                            {selectedFiles.map((file, index) => {
                                const fileExtension = file.name.split('.').pop().toLowerCase();
                                const fileURL = URL.createObjectURL(file);

                                return (
                                    <div className="col-md-4 mb-3" key={index}>
                                        {['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension) ? (
                                            <img
                                                src={fileURL}
                                                alt="Preview"
                                                className="img-thumbnail"
                                                style={{ maxWidth: '100%', height: 'auto' }}
                                            />
                                        ) : fileExtension === 'pdf' ? (
                                            <a href={fileURL} target="_blank" rel="noopener noreferrer">
                                                <i className="pi pi-file-pdf" style={{ fontSize: '24px', color: 'red' }}></i> {file.name}
                                            </a>
                                        ) : ['xls', 'xlsx'].includes(fileExtension) ? (
                                            <a href={fileURL} target="_blank" rel="noopener noreferrer">
                                                <i className="pi pi-file-excel" style={{ fontSize: '24px', color: 'green' }}></i> {file.name}
                                            </a>
                                        ) : (
                                            <a href={fileURL} target="_blank" rel="noopener noreferrer">
                                                <i className="pi pi-file" style={{ fontSize: '24px' }}></i> {file.name}
                                            </a>
                                        )}

                                        {/* Remove Button */}
                                        <Button
                                            variant="danger"
                                            size="sm"
                                            className="mt-2"
                                            onClick={() => removeFile(index)}
                                        >
                                            Remove
                                        </Button>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    <Button
                        variant="primary"
                        onClick={handleUpload}
                        className="mt-3"
                        disabled={selectedFiles.length === 0} // Disable if no file is selected
                    >
                        Upload
                    </Button>
                    <div className='row'>
                        <div className="row mb-3 mt-3">
                            {selectedAttachments.additionalDocuments &&
                                selectedAttachments.additionalDocuments.map((upload) => {
                                    const fileExtension = upload.split('.').pop().toLowerCase();

                                    // Remove numbers and special characters from the beginning of the filename
                                    const cleanFileName = upload.replace(/^\d+[_\-.]*/, '');

                                    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                        return (
                                            <div className="col-md-4 mb-3" key={upload}>
                                                <img
                                                    src={`${STATIC_URL}/${upload}`}
                                                    alt="Displayed"
                                                    className="display-image"
                                                    style={{ maxWidth: '100%' }}
                                                />
                                            </div>
                                        );
                                    } else if (fileExtension === 'pdf') {
                                        return (
                                            <div className="col-md-4 mb-3" key={upload}>
                                                <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer" style={{ wordBreak: 'break-all' }}>
                                                    {cleanFileName}
                                                </a>
                                            </div>
                                        );
                                    } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                        return (
                                            <div className="col-md-4 mb-3" key={upload}>
                                                <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer" style={{ wordBreak: 'break-all' }}>
                                                    {cleanFileName}
                                                </a>
                                            </div>
                                        );
                                    } else {
                                        return (
                                            <div className="col-md-4 mb-3" key={upload}>
                                                <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer" style={{ wordBreak: 'break-all' }}>
                                                    {cleanFileName}
                                                </a>
                                            </div>
                                        );
                                    }
                                })}
                        </div>
                    </div>







                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowAttach(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default AllIncidentReport
