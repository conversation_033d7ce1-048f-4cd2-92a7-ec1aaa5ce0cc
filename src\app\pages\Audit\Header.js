import React, { useState, useEffect } from 'react';
import moment from 'moment';

const Header = ({ readOnly, data, onValuesChange }) => {
    const [editableValues, setEditableValues] = useState({
        countryHeadOfBU: data.headerInformation?.countryHeadOfBU || '',
        projectDcLeader: data.headerInformation?.projectDcLeader || '',
        countryHeadOfEHS: data.headerInformation?.countryHeadOfEHS || '',
        auditTeamMembers: data.headerInformation?.auditTeamMembers || '',
        auditAttendees: data.headerInformation?.auditAttendees || '',
        assuranceLead: data.headerInformation?.assuranceLead || '', // Added Assurance / EHS Lead
    });

    const [isEditing, setIsEditing] = useState({
        countryHeadOfBU: false,
        projectDcLeader: false,
        countryHeadOfEHS: false,
        auditTeamMembers: false,
        auditAttendees: false,
        assuranceLead: false, // Added editing state for Assurance / EHS Lead
    });

    useEffect(() => {
        console.log(data, 'Updated data');
    }, [data]);

    const handleValueChange = (key, value) => {
        const updatedValues = { ...editableValues, [key]: value.trim() }; // Remove extra spaces
        setEditableValues(updatedValues);
        onValuesChange(updatedValues);
    };

    const handleFocus = (key) => {
        setIsEditing({ ...isEditing, [key]: true });
    };

    const handleBlur = (key, event) => {
        const value = event.target.textContent.trim();
        handleValueChange(key, value);
        setIsEditing({ ...isEditing, [key]: false });
    };

    const renderEditableField = (label, key) => (
        <div className="col-md-6">
            <p className="obs-title">{label}</p>
            {!readOnly && (
                <p
                    contentEditable="true"
                    className="obs-content"
                    onFocus={() => handleFocus(key)}
                    onBlur={(e) => handleBlur(key, e)}
                    suppressContentEditableWarning={true} // Prevents React warning
                >
                    {isEditing[key] || editableValues[key] ? editableValues[key] : 'Click to Edit'}
                </p>
            )}
            {readOnly && <p className="obs-content">{editableValues[key] || '-'}</p>}
        </div>
    );

    return (
        <div className="obs-section p-4">
            <h2>Audit</h2>
            <p className="obs-content mb-3">{data.maskId}</p>
            <div className="row mb-3">
                <div className="col-md-6">
                    <p className="obs-title">Audit Date</p>
                    <p className="obs-content">
                        {moment(data.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")} -
                        {moment(data.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}
                    </p>
                </div>
                <div className="col-md-6">
                    <p className="obs-title">Project / DC Operation</p>
                    <p className="obs-content">
                        {data.locationOne.name} > {data.locationTwo.name} > {data.locationThree.name} > {data.locationFour.name}
                    </p>
                </div>
                {renderEditableField("Country Head of BU", "countryHeadOfBU")}
                {renderEditableField("Project / DC Leader", "projectDcLeader")}

                {renderEditableField("Country Head of EHS", "countryHeadOfEHS")}
                {renderEditableField("Assurance / EHS Lead", "assuranceLead")} {/* Added Assurance / EHS Lead field */}
                {renderEditableField("Audit Team Member(s)", "auditTeamMembers")}
                {renderEditableField("Auditees", "auditAttendees")}
            </div>
        </div>
    );
};

export default Header;
