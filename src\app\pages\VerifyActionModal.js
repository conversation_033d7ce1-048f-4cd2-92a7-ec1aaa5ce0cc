import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, AUDIT_ACTION_URL, INSPECTION_ACTION_URL, GET_USERS_BY_ROLE, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import moment from 'moment';
import GalleryPage from "../apps/Gallery";
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Select from 'react-select';

const VerifyActionModal = ({ data, applicationType, showModal, setShowModal }) => {

    console.log('in')

    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])
    const [newDueDate, setNewDueDate] = useState(null);
    const [actionOwnerOption, setActionOwnerOption] = useState(null);
    const [showReturnModal, setShowReturnModal] = useState(false);
    const [inspector, setInspector] = useState([]);
    const comments = useRef();
    const [enteredAction, setEnteredAction] = useState('');

    const actionTaken = useRef();

    const handleFileChange = (file) => {
        setFiles(file)

    }
    const getInspector = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, {
            locationOneId: data.applicationDetails.locationOneId,
            locationTwoId: data.applicationDetails.locationTwoId,
            locationThreeId: data.applicationDetails.locationThreeId,
            locationFourId: data.applicationDetails.locationFourId,
            mode: 'inspection-action-plan-implementor'
        });
        if (response.status === 200) {
            setInspector(response.data);
        }
    };

    useEffect(() => {
        if (showReturnModal && applicationType === 'Inspection') {
            getInspector();
        }
    }, [showReturnModal]);

    useEffect(() => {
        if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {
            if (applicationType === 'Observation') {
                getObsUsers();

            }
        }

    }, [])

    const inspectorOptions = inspector.map((u) => ({
        label: `${u.firstName} `,
        value: u.id,
    }));

    const getObsUsers = async () => {
        const response = await API.post(OBSERVATION_REVIEWER_LIST_URL, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const handleSubmit = async (type) => {
        if (!comments.current.value) {
            cogoToast.error('Please enter your comments!')
            return;
        }

        let url = '';
        let actionType = '';

        switch (applicationType) {
            case 'Observation':
                url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
                actionType = type === 'approve' ? 'approve' : 'reject';
                break;

            case 'INCIDENT':
                url = REPORT_INCIDENT_ACTIONS_WITH_ID(data.id);
                actionType = type === 'approve' ? 'approve' : 'reject';
                break;

            case 'Inspection':
                url = INSPECTION_ACTION_URL(data.id);
                actionType = type === 'approve' ? 'approve' : 'reject';
                break;

            case 'AuditFinding':
                url = AUDIT_ACTION_URL(data.id);
                actionType = type === 'approve' ? 'approve' : 'reject';
                break;
        }

        const payload ={
            actionType: actionType,
            status: actionType,
            comments: comments.current.value,
            actionTaken: '',
            submittedById: data.submittedById,
            assignedToId: data.assignedToId,
            actionToBeTaken: data.actionToBeTaken,
            objectId: data.objectId,
            description: data.description,
            // dueDate: data.dueDate,
            // uploads: data.uploads,
            // createdDate: data.createdDate,
            sequenceNo: data.sequenceNo
        }
        if (applicationType === 'Inspection' && actionType === 'reject') {

            payload.dueDate = newDueDate    ? moment(newDueDate).format('YYYY-MM-DD') : null ;
            payload.actionToBeTaken = enteredAction;
            payload.assignedToId = actionOwnerOption.value;
        }
      console.log(payload)

        const response = await API.patch(url, payload)

        if (response.status === 204) {
            cogoToast.success('Submitted!')
            setShowModal(false)
        }
    }

    const matched = data.applicationDetails.postActions?.find(
        (p) => p.sequenceNo === data.sequenceNo
    );

    // Extract label from checklist using i and index
    let questionLabel = '';
    let main = ''
    if (
        matched &&
        matched.i !== undefined &&
        matched.index !== undefined &&
        data.applicationDetails.checklist?.value?.[matched.index]?.questions?.[matched.i]
    ) {
        questionLabel = data.applicationDetails.checklist.value[matched.index].questions[matched.i].label;
        main = data.applicationDetails.checklist.value[matched.index].label
    }

    const getCreatedDateBySequence = (data, sequence) => {
        const match = data.find(
            item => item.actionType === "ins_take_actions_control" && item.sequenceNo === sequence
        );

        return match ? moment(match.createdDate).format('Do MMM YYYY') : null;
    };
    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>

                        Verify Implementation -
                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>}
                        {applicationType === 'INCIDENT' && `Incident - ${data.applicationDetails.title}`}
                        {applicationType === 'AuditFinding' && 'AuditFinding'}

                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>
                        {console.log('inside ')}
                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                    <h5 className="card-title">ID :{data.applicationDetails.maskId}</h5>
                                    {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}

                                    {applicationType === 'INCIDENT' && <>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Incident Date</p>
                                                <p className="obs-content">
                                                    {moment(data.applicationDetails.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY')}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">Location</p>
                                                <p className="obs-content">
                                                    {[
                                                        data.applicationDetails.locationOne?.name,
                                                        data.applicationDetails.locationTwo?.name,
                                                        data.applicationDetails.locationThree?.name,
                                                        data.applicationDetails.locationFour?.name
                                                    ].filter(Boolean).join(" > ")}
                                                </p>

                                            </div>
                                        </div>
                                        {/* <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Incident Date</p>
                                                <p className="obs-content">
                                                ${data.applicationDetails.actualImpact} ${data.applicationDetails.potentialImpact && `(${data.applicationDetails.potentialImpact})`}`
                                                </p>
                                            </div>
                                        </div> */}
                                    </>}
                                    {applicationType === 'Inspection' && <>

                                        <h5 className="card-title"> {main}</h5>

                                        <h5 className="card-title"> Q : {questionLabel}</h5>

                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Checklist</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.checklist?.name}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">Description</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.checklist?.description}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Location</p>
                                                <p className="obs-content">
                                                    {[
                                                        data.applicationDetails.locationOne?.name,
                                                        data.applicationDetails.locationTwo?.name,
                                                        data.applicationDetails.locationThree?.name,
                                                        data.applicationDetails.locationFour?.name
                                                    ].filter(Boolean).join(" > ")}
                                                </p>

                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title"> Date</p>
                                                <p className="obs-content">
                                                    {moment(data.applicationDetails.created).format('Do MMM YYYY') || '' || ''}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Scheduler</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.assignedBy?.firstName}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title"> Inspector</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.assignedTo?.firstName || ''}
                                                </p>
                                            </div>
                                        </div>
                                    </>


                                    }
                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <input type="text" className="form-control" value={`${data.applicationDetails.locationOne && data.applicationDetails.locationOne.name} > ${data.applicationDetails.locationTwo && data.applicationDetails.locationTwo.name} > ${data.applicationDetails.locationThree && data.applicationDetails.locationThree.name} > ${data.applicationDetails.locationFour && data.applicationDetails.locationFour.name}`} readOnly />
                                    </div>}

                                    {
                                        applicationType === 'AuditFinding' && <>
                                            <div className="row mb-4 p-2" style={{ border: '1px solid #e7e6e6' }}>
                                                <div className="col-6">
                                                    <p>Project/DC: <b>{data.applicationDetails?.audit?.locationFour.name}</b></p>
                                                    <p>Start Date: <b>{moment(data.applicationDetails?.audit?.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>

                                                </div>
                                                <div className="col-6">
                                                    <p>Auditor Name: <b>{data.applicationDetails?.audit?.assignedTo?.firstName}</b></p>
                                                    <p>End Date: <b>{moment(data.applicationDetails?.audit?.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>
                                                </div>
                                                <div className="col-12">
                                                    <p>Findings: <b>{data.applicationDetails?.findings}</b></p>
                                                </div>

                                                <div className="col-6">
                                                    <p>Category: <b>{data.applicationDetails?.category}</b></p>
                                                    <p>Standards And References: <b>{data.applicationDetails?.standardsAndReferences}</b></p>
                                                    <p>Recommended Mitigation Measures: <b>{data.applicationDetails?.recommendations}</b></p>
                                                </div>
                                                <div className="col-6">
                                                    <p>Classification: <b>{data.applicationDetails?.classification}</b></p>
                                                    <p>Potential Consequences: <b>{data.applicationDetails?.potentialHazard}</b></p>
                                                    <p>Due Date: <b>{data.dueDate}</b></p>
                                                </div>

                                            </div>

                                            {/* <p>Findings: {data.applicationDetails?.findings}</p> */}
                                        </>}

                                    {
                                        (data.applicationDetails.postActions && data.applicationDetails.postActions.length > 0) && (
                                            <div className="mb-3">
                                                <label className="form-label">Uploads</label>
                                                <div className="border p-3 row">
                                                    {data.applicationDetails.postActions.map(j => {
                                                        console.log(j.sequenceNo);
                                                        console.log(data.sequenceNo);
                                                        if (String(j.sequenceNo).toLowerCase() === String(data.sequenceNo).toLowerCase()) {
                                                            console.log('in');
                                                            // Ensure that j.uploads is an array before mapping
                                                            return j.uploads?.map(i => {
                                                                const fileExtension = i.split('.').pop().toLowerCase();

                                                                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                                    console.log(i);
                                                                    return (
                                                                        <div className="col-md-3" key={i}>
                                                                            <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                                        </div>
                                                                    );
                                                                } else if (fileExtension === 'pdf') {
                                                                    return (
                                                                        <div className="col-md-3" key={i}>
                                                                            <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                                View PDF
                                                                            </a>
                                                                        </div>
                                                                    );
                                                                } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                                    return (
                                                                        <div className="col-md-3" key={i}>
                                                                            <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                                Download Excel File
                                                                            </a>
                                                                        </div>
                                                                    );
                                                                } else {
                                                                    return (
                                                                        <div className="col-md-3" key={i}>
                                                                            <p>Unsupported file type: {fileExtension}</p>
                                                                        </div>
                                                                    );
                                                                }
                                                            });
                                                        }
                                                        return null; // Return null if the condition is not met
                                                    })}
                                                </div>
                                            </div>
                                        )

                                    }

                                    {
                                        (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && (
                                            <div className="mb-3">
                                                <label className="form-label">Uploads</label>
                                                <div className="border p-3 row">
                                                    {data.applicationDetails.uploads.map(i => {
                                                        const fileExtension = i.split('.').pop().toLowerCase();

                                                        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                            // Handle image files
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                                </div>
                                                            );
                                                        } else if (fileExtension === 'pdf') {
                                                            // Handle PDF files (as URLs)
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                        View PDF
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                            // Handle Excel files
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                        Download Excel File
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else {
                                                            // Handle other file types or show a default message
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <p>Unsupported file type: {fileExtension}</p>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )
                                    }


                                    {(applicationType === 'INCIDENT' && data.applicationDetails.description) && <div className="mb-3">
                                        <label className="form-label">Description of Incident</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.applicationDetails.description}</textarea>
                                    </div>}

                                    {
                                        (!applicationType === 'INCIDENT' && data.description) && <div className="mb-3">
                                            <label className="form-label">Description</label>
                                            <textarea className="form-control" rows="3" readOnly>{data.description}</textarea>
                                        </div>
                                    }

                                    {
                                        applicationType === 'AuditFinding' ? <></> : <>
                                            {data.actionToBeTaken && <div className="mb-3">
                                                <label className="form-label">Actions to be taken</label>
                                                <textarea className="form-control" rows="3" value={data.actionToBeTaken} readOnly></textarea>
                                            </div>}</>
                                    }
                                    {
                                        applicationType === 'AuditFinding' ? <>  <div className="mb-3">
                                            {/* <label className="form-label">Identify the Root Cause(s)</label> */}
                                            <p>Identified the Root Cause(s):</p>
                                            <div dangerouslySetInnerHTML={{ __html: JSON.parse(data.actionTaken).rootCause }}></div>
                                            <p>Identified Corrective Actions:</p>
                                            <div dangerouslySetInnerHTML={{ __html: JSON.parse(data.actionTaken).correctiveAction }}></div>
                                            <p>Description of the Action Taken:</p>
                                            <div dangerouslySetInnerHTML={{ __html: JSON.parse(data.actionTaken).actionDesc }}></div>
                                        </div></> : <>
                                            <div className="mb-3">
                                                <label className="form-label">
                                                    Action taken {applicationType === "Inspection" && data?.applicationDetails?.assignedTo?.firstName ? `by ${data.applicationDetails.assignedTo.firstName}` : ''} on {applicationType === "Inspection" && getCreatedDateBySequence(data.applicationDetails.actions, data.sequenceNo)}
                                                </label>

                                                <textarea value={data.actionTaken} className="form-control" rows="3" readOnly></textarea>
                                            </div></>
                                    }

                                    {
                                        data.uploads && data.uploads.length > 0 && (
                                            <div className="mb-3">
                                                <label className="form-label">Evidence</label>
                                                <div className="border p-3 row">
                                                    {(() => {
                                                        const images = [];
                                                        const otherFiles = [];

                                                        data.uploads.forEach((i) => {
                                                            const fileExtension = i.split('.').pop().toLowerCase();

                                                            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                                // Collect image files for gallery
                                                                images.push({ src: `${STATIC_URL}/${i}`, width: 4, height: 3 });
                                                            } else {
                                                                // Collect non-image files
                                                                otherFiles.push({ file: i, fileExtension });
                                                            }
                                                        });

                                                        return (
                                                            <>
                                                                {/* Render image gallery */}
                                                                {images.length > 0 && (
                                                                    <div className="col-12 mb-3">
                                                                        <GalleryPage photos={images} />
                                                                    </div>
                                                                )}

                                                                {/* Render other files */}
                                                                {otherFiles.map(({ file, fileExtension }) => {
                                                                    if (fileExtension === 'pdf') {
                                                                        // Handle PDF files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-pdf"
                                                                                        style={{
                                                                                            color: "red",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    View PDF
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                                        // Handle Excel files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-excel"
                                                                                        style={{
                                                                                            color: "green",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    Download Excel File
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else if (['doc', 'docx'].includes(fileExtension)) {
                                                                        // Handle Word files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-word"
                                                                                        style={{
                                                                                            color: "blue",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    Download Word File
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else {
                                                                        // Handle other file types
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <i
                                                                                    className="pi pi-file"
                                                                                    style={{ fontSize: "1.5em" }}
                                                                                ></i>{" "}
                                                                                <p>Unsupported file type: {fileExtension}</p>
                                                                            </div>
                                                                        );
                                                                    }
                                                                })}
                                                            </>
                                                        );
                                                    })()}
                                                </div>
                                            </div>
                                        )
                                    }



                                    <div className="mb-3">
                                        <label className="form-label">{applicationType === 'AuditFinding' ? 'Audit Action Verifier Comments' : 'Comments'}</label>
                                        <textarea ref={comments} className="form-control" rows="3" required></textarea>
                                    </div>


                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="success"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('approve')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Approve
                    </Button>
                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => {
                            if (applicationType === 'Inspection') {

                                if (!comments.current.value) {
                                    cogoToast.error('Please enter your comments!')
                                    return;
                                }
                                setShowReturnModal(true);
                            } else {
                                handleSubmit('reject');
                            }
                        }}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Return
                    </Button>

                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>

            <Modal show={showReturnModal} onHide={() => setShowReturnModal(false)} backdrop="static">
                <Modal.Header closeButton>
                    <Modal.Title>Return Action - Provide Details</Modal.Title>
                </Modal.Header>
                <Modal.Body>

                    <Form.Group className="mb-3">
                        <Form.Label>Action to be Taken</Form.Label>
                        <textarea
                            className="form-control"
                            rows="3"
                            value={enteredAction}
                            onChange={(e) => setEnteredAction(e.target.value)}
                            placeholder="Describe the action to be taken"
                            required
                        />
                    </Form.Group>


                    <Form.Group className="mb-3">
                        <Form.Label>New Due Date</Form.Label>
                        <ReactDatePicker
                            selected={newDueDate}
                            onChange={(date) => setNewDueDate(date)}
                            className="form-control"
                            minDate={new Date()}
                            dateFormat="dd/MM/yyyy"
                            placeholderText="Select due date"
                        />
                    </Form.Group>

                    <Form.Group className="mb-3">
                        <Form.Label>Action Owner</Form.Label>
                        <Select
                            options={inspectorOptions}
                            value={actionOwnerOption}
                            onChange={(selected) => setActionOwnerOption(selected)}
                            placeholder="Select Action Owner"
                            isClearable
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowReturnModal(false)}>
                        Cancel
                    </Button>
                    <Button
                        variant="primary"
                        onClick={() => {
                            if (!newDueDate || !actionOwnerOption || !enteredAction) {
                                cogoToast.error('Fields are required');
                                return;
                            }
                            const formattedDate = moment(newDueDate).format('YYYY-MM-DD');
                            handleSubmit('reject');
                            setShowReturnModal(false);
                        }}
                    >
                        Submit Return
                    </Button>
                </Modal.Footer>
            </Modal>

        </>
    )
}

export default VerifyActionModal;