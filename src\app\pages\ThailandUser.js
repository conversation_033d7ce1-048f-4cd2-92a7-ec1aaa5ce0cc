// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { INTERNAL_USERS_URL, KOREA_USERS_URL, USERS_URL, USERS_URL_WITH_ID } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@material-ui/core';
import axios from 'axios';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-danger',
    cancelButton: 'btn btn-light'
  },
  buttonsStyling: false
})

const customSwal2 = Swal.mixin({
  customClass: {
    confirmButton: 'btn btn-primary',

  },
  buttonsStyling: false
})

const ThailandUser = () => {

  const [mdShow, setMdShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const history = useHistory();
  const uName = useRef();
  const uEmail = useRef();
  const uCompany = useRef();

  const [data, setData] = useState([]);
  const [nextLink, setNextLink] = useState("https://graph.microsoft.com/v1.0/users?$filter=country eq 'Thailand'&$top=50&$orderby=displayName asc&$count=true&$select=mail,displayName,givenName,surname,jobTitle,mobilePhone,userPrincipalName,id,country");
  const [prevLinks, setPrevLinks] = useState("https://graph.microsoft.com/v1.0/users?$filter=country eq 'Thailand'&$top=50&$orderby=displayName asc&$count=true&$select=mail,displayName,givenName,surname,jobTitle,mobilePhone,userPrincipalName,id,country");


  const [accessToken, setAccessToken] = useState('');
  const [users, setUsers] = useState([])
  useEffect(() => {
    getToken()
  }, [])

  const getToken = async () => {
    const response = await API.get(INTERNAL_USERS_URL)
    if (response.status === 200) {
      setAccessToken(response.data.token)
      setUsers(response.data.data)
    }
  }

  const fetchData = async (url, search = '') => {
    let fullUrl = url;
    if (search) {
      const encodedSearchQuery = encodeURIComponent(`"displayName:${search}"`);
      fullUrl = `${url}&$search=${encodedSearchQuery}`;
    }
    const response = await axios.get(fullUrl, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'consistencylevel': 'eventual'
      }
    });
    setData(response.data.value.map((user) => {
      const whiteListed = users.find(b => b.email === user.mail);
      if (whiteListed) {
        return { ...user, country: user.country, status: 'active' };
      }
      return { ...user, country: '', status: 'inactive' };
    }));
    setNextLink(response.data['@odata.nextLink']);
  };

  useEffect(() => {
    if (accessToken)
      fetchData(nextLink);
  }, [accessToken]);

  const handleNext = () => {
    setPrevLinks(prevLinks => [...prevLinks, nextLink]);
    fetchData(nextLink);
  };

  const handleBack = () => {
    setPrevLinks(prevLinks => {
      const newPrevLinks = prevLinks.slice(0, -1);
      const prevLink = prevLinks[prevLinks.length - 2];
      if (prevLink) {
        fetchData(prevLink);
      }
      return newPrevLinks;
    });
  };

  const [searchQuery, setSearchQuery] = useState('');
  const handleSearch = () => {
    setPrevLinks([]);
    setNextLink("https://graph.microsoft.com/v1.0/users?$filter=country eq 'Thailand'&$top=50&$orderby=displayName asc&$count=true&$select=mail,displayName,givenName,surname,jobTitle,mobilePhone,userPrincipalName,id,country");
    fetchData(nextLink, searchQuery);
  };

  useEffect(() => {
    if (searchQuery) {
      handleSearch()
    }
  }, [searchQuery])
  const uRole = useRef();

  const thead = [
    'Name',
    'Email',
    'Country Assigned',
    'Actions',

  ];


  $(document).on('click', '.unassign-user', async function (e) {


    deletePopup.fire({
      title: 'Are you sure?',
      text: "",
      icon: 'warning',
      showCancelButton: true,
      reverseButtons: true,

      confirmButtonText: 'Unassign'
    }).then(async (result) => {
      if (result.isConfirmed) {
        //   deleteChecklist(id);
        const id = $(this).data('id')
        const response = await API.delete(USERS_URL_WITH_ID(id))

        if (response.status === 204) {

          $(this).removeClass('text-success').removeClass('mdi-check-circle').removeClass('unassign-user').addClass('assign-user').addClass('mdi-plus-circle').addClass('text-danger');
          cogoToast.info('User unassigned', { position: 'top-right' })

        }

      }
    })







  })


  $(document).on('click', '.assign-user', async function (e) {
    const response = await fetch(
      USERS_URL,
      {
        method: 'POST',
        body: JSON.stringify({
          id: $(this).data('id'),
          firstName: $(this).data('name'),
          email: $(this).data('email'),
          company: 'STT GDC',
          password: 'DEFAULTDISABLED',
          country: 'Singapore'


        }),
        headers: { "Content-type": "application/json; charset=UTF-8", "Authorization": "Bearer " + localStorage.getItem("access_token") }
      })

    if (response.ok) {
      $(this).addClass('text-success').addClass('mdi-check-circle').addClass('unassign-user').removeClass('assign-user').removeClass('mdi-plus-circle').removeClass('text-danger');
      cogoToast.info('User added to the Application!', { position: 'top-right' })
    } else {
      //show error
      cogoToast.error('Please try again', { position: 'top-right' })
    }




  })


  return (
    <>
      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-body">

                <h4 className="card-title">STT Employee Database User Management</h4>
                <div className="row">
                  <div className="col-12">
                    <div>
                      {/* <button type="button" className="btn btn-primary btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}

                      <div className='d-flex justify-content-end'>
                        <input type='text' className='form-control w-25' value={searchQuery} placeholder='Search User'
                          onChange={(e) => setSearchQuery(e.target.value)} />
                      </div>
                      <table className='table table-striped table-responsive'>
                        <thead>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Country Assigned</th>
                          <th>Actions</th>
                        </thead>
                        <tbody>
                          {data.map((row) => (
                            <tr key={row.id}> {/* It's also a good practice to add a unique key for each row */}
                              <td>{row.displayName}</td>
                              <td>{row.mail}</td>
                              <td>{row.country}</td>
                              <td>
                                {row.status === 'active' ? (
                                  <div style={{ fontSize: '22px' }}>
                                    <i className="mdi mdi-check-circle text-success unassign-user cursor-pointer" data-id={row.id}></i>
                                  </div>
                                ) : (
                                  <div style={{ fontSize: '22px' }}>
                                    <i className="mdi mdi-plus-circle text-danger assign-user cursor-pointer" data-id={row.id} data-email={row.mail} data-name={row.displayName}></i>
                                  </div>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      <div className='mt-3'>
                        <Button className="me-2" variant='light' onClick={handleBack} disabled={prevLinks.length === 0}>Back</Button>
                        <Button onClick={handleNext} disabled={!nextLink}>Next</Button>
                      </div>


                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={mdShow}
        size="md"
        onHide={() => setMdShow(false)}
        aria-labelledby="example-modal-sizes-title-md"
      >

        <Modal.Body>
          {/* <form className="forms">
            <div className="form-group">
              <label htmlFor="user_name" >Name</label>
              <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
            </div>

            <div className="form-group">
              <label htmlFor="user_category" >Email</label>
              <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
            </div>

            <div className="form-group">
              <label htmlFor="user_description" >Company</label>
              <Form.Control type="text" ref={uCompany} id="user_description" placeholder="Enter Company Name" />
            </div>

            <div className="form-group">
              <label htmlFor="user_description" >Assign Role</label>
              <select ref={uRole} className='form-control'>
                <option value=''>Choose Role</option>
                <option value='manager'>Manager</option>
                <option value='contractor'>Contractors</option>
              </select>
            </div>



          </form> */}
        </Modal.Body>

        <Modal.Footer className="flex-wrap">

          <>
            <Button variant="light" onClick={() => setMdShow(false)}>Cancel</Button>

          </>


        </Modal.Footer>
      </Modal>
    </>
  )
}


export default ThailandUser;
