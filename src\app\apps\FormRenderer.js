import React, { useState } from 'react';
import { API_URL } from '../constants';
import axios from 'axios';

const FormRenderer = ({ formData, handleFormSubmit, id, setShowModal }) => {
    const [formValues, setFormValues] = useState(formData);

    const handleCheckboxChange = (fieldIndex, optionIndex) => {
        const newFormValues = [...formValues];
        newFormValues[fieldIndex].values[optionIndex].selected = !newFormValues[fieldIndex].values[optionIndex].selected;
        setFormValues(newFormValues);
    };

    const handleRadioChange = (fieldIndex, optionIndex) => {
        const newFormValues = [...formValues];
        newFormValues[fieldIndex].values.forEach((option, idx) => {
            option.selected = idx === optionIndex;
        });
        setFormValues(newFormValues);
    };

    const handleInputChange = (fieldIndex, value) => {
        const newFormValues = [...formValues];
        newFormValues[fieldIndex].value = value;  // Assuming 'value' field for textareas and file inputs
        setFormValues(newFormValues);
    };

    const handleSubmit = (event) => {
        event.preventDefault();
        console.log('Form Data:', formValues);
        handleFormSubmit(formValues, id)
        // API call to send formValues could go here
    };

    // Inline styles
    const styles = {
        formContainer: {
            maxWidth: '600px',
            margin: '20px auto',
            padding: '20px',
            background: '#f4f4f4',
            borderRadius: '8px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
        },
        formGroup: {
            marginBottom: '15px',
        },
        label: {
            display: 'block',
            marginBottom: '5px',

        },
        input: {
            marginRight: '10px', // Gives space between the radio/checkbox and the label
        },
        inputWrapper: {
            marginBottom: '5px', // Spacing between each input option
            alignItems: 'center', // Aligns the checkbox/radio button vertically with the label
            display: 'flex',
        },
        button: {
            padding: '10px 20px',
            color: '#fff',
            background: '#007BFF',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
        }
    };
    const handleFileUpload = async (fieldIndex, files) => {
        const formData = new FormData();
        Array.from(files).forEach(file => {
            formData.append('file', file);
        });

        const token = localStorage.getItem('access_token');  // Retrieve auth token

        try {
            const response = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.status === 200) {
                // Assuming the server returns an array of file info
                const uploadedFileNames = response.data.files.map(file => file.originalname);

                // Update the state with the uploaded file names
                const newFormValues = [...formValues];
                newFormValues[fieldIndex].value = uploadedFileNames.join(', ');  // Join file names into a string if necessary
                setFormValues(newFormValues);
            }
        } catch (error) {
            console.error('Error uploading files:', error);
            // Optionally handle errors in UI
        }
    };
    return (
        <div style={styles.formContainer}>
            <form onSubmit={handleSubmit}>
                {formValues.map((field, fieldIndex) => (
                    <div key={fieldIndex} style={styles.formGroup}>
                        <label style={styles.label} dangerouslySetInnerHTML={{ __html: field.label }} />
                        {field.type === 'checkbox-group' && field.values.map((option, optionIndex) => (
                            <div key={option.value} style={styles.inputWrapper}>
                                <input
                                    type="checkbox"
                                    checked={option.selected}
                                    onChange={() => handleCheckboxChange(fieldIndex, optionIndex)}
                                    style={styles.input}
                                />
                                <label style={styles.label}>{option.label}</label>
                            </div>
                        ))}

                        {field.type === 'radio-group' && field.values.map((option, optionIndex) => (
                            <div key={option.value} style={styles.inputWrapper}>
                                <input
                                    type="radio"
                                    checked={option.selected}
                                    onChange={() => handleRadioChange(fieldIndex, optionIndex)}
                                    style={styles.input}
                                />
                                <label style={styles.label}>{option.label}</label>
                            </div>
                        ))}
                        {field.type === 'textarea' && (
                            <textarea
                                style={{ ...styles.input, height: '100px' }} // Adjust height for textarea
                                value={field.value || ''}
                                onChange={(e) => handleInputChange(fieldIndex, e.target.value)}
                            />
                        )}
                        {(field.type === 'multimedia' || field.type === 'sign') && (
                            <input
                                type="file"
                                multiple={true}  // Allow multiple files to be selected
                                onChange={(e) => handleFileUpload(fieldIndex, e.target.files)}
                                style={{ margin: '10px 0', width: '100%' }}
                            />
                        )}
                    </div>
                ))}
                <button type="submit" className='btn btn-primary'>Submit</button>

                <button onClick={() => { setShowModal(false) }} className="btn btn-secondary ms-3" >Close</button>
            </form>
        </div>
    );
};

// Simulated file upload function
const simulateFileUpload = (file) => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(`uploaded_${file.name}`);
        }, 1000);
    });
};

export default FormRenderer;
