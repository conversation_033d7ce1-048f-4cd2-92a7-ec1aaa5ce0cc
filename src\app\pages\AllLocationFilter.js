import React, { useState, useEffect } from "react";
import API from "../services/API";
import { LOCATION1_URL, LOCATION_TIER1_URL, TIER1_TIER2_URL, TIER2_TIER3_URL, DYNAMIC_TITLES_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL, API_URL } from "../constants";

import { Dropdown } from 'primereact/dropdown';


const AllFilterLocation = (props) => {

    console.log(props.getLocation)
    const BASE_URL = `${API_URL}/`; // Example base URL

    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [locationFour, setLocationFour] = useState([]);


    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');


    useEffect(() => {
        if (props.getLocation && Object.keys(props.getLocation).length > 0) {
            setSelectedLocationOne(props.getLocation.locationOneId)
            setSelectedLocationTwo(props.getLocation.locationTwoId)
            setSelectedLocationThree(props.getLocation.locationThreeId)
            setSelectedLocationFour(props.getLocation.locationFourId)
        }

    }, [props.location])


    useEffect(() => {
        const fetchLocationOne = async () => {
            const response = await API.get(`${BASE_URL}location-ones`);
            if (response.status === 200) {



                setLocationOne(response.data);
            }
        };
        fetchLocationOne();
    }, []);

    // Fetch Location Two based on Location One selection
    useEffect(() => {
        if (selectedLocationOne === '') return;
        const fetchLocationTwo = async () => {
            const response = await API.get(`${BASE_URL}location-ones/${selectedLocationOne}/location-twos`);
            if (response.status === 200) {



                setLocationTwo(response.data);
            }
        };
        fetchLocationTwo();
    }, [selectedLocationOne]);

    // Fetch Location Three based on Location Two selection
    useEffect(() => {
        if (selectedLocationTwo === '') return;
        const fetchLocationThree = async () => {
            const response = await API.get(`${BASE_URL}location-twos/${selectedLocationTwo}/location-threes`);
            if (response.status === 200) {


                setLocationThree(response.data);
            }
        };
        fetchLocationThree();
    }, [selectedLocationTwo]);

    // Fetch Location Four based on Location Three selection
    useEffect(() => {
        if (selectedLocationThree === '') return;
        const fetchLocationFour = async () => {
            const response = await API.get(`${BASE_URL}location-threes/${selectedLocationThree}/location-fours`);
            if (response.status === 200) {


                setLocationFour(response.data);
            }
        };
        fetchLocationFour();
    }, [selectedLocationThree]);





    const [title, setTitles] = useState({ tier1: 'Tier I', tier2: 'Tier II', tier3: 'Tier III', tier4: 'Tier IV' });
    useEffect(() => {

        getLocationConfigs();
    }, [])

    const getLocationConfigs = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);

        if (response.status === 200) {
            const titles = response.data;
            // setTitleData(titles)
            const locationsObject = titles.reduce((obj, item) => {
                obj[item.title] = item.altTitle;

                return obj;
            }, {});
            setTitles({ tier1: locationsObject.LocationOne, tier2: locationsObject.LocationTwo, tier3: locationsObject.LocationThree, tier4: locationsObject.LocationFour })
        }


    }

    useEffect(() => {
        props.handleFilter(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour)
        console.log(selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour)
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])
    return (
        <>
            <div className="row">
                <div className="col-4">
                    <div className='mb-4 d-flex flex-column '>
                        <label className='mb-2 font-sm'> {title.tier1}</label>
                        <select className='form-control' onChange={(e) => setSelectedLocationOne(e.target.value)} value={selectedLocationOne}>
                            <option value={''}>{'All'} </option>

                            {
                                locationOne.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }

                        </select>

                        {/* <Dropdown value={selectedLocationOne} onChange={(e) => setSelectedLocationOne(e.value)} options={locationOne} optionLabel="name"
                            placeholder="Select" className="" /> */}
                    </div>
                </div>

                <div className="col-4">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier2}</label>
                        <select
                            className='form-control'
                            onChange={(e) => setSelectedLocationTwo(e.target.value)}
                            value={selectedLocationTwo}
                        >
                            <option value={''}>{'All'}</option>

                            {
                                locationTwo.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>
                        {/* <Dropdown value={selectedLocationTwo} onChange={(e) => setSelectedLocationTwo(e.value)} options={locationTwo} optionLabel="name"
                            placeholder="Select" className="" /> */}
                    </div>
                </div>

                <div className="col-4">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier3}</label>
                        <select
                            className='form-control'
                            onChange={(e) => setSelectedLocationThree(e.target.value)}
                            value={selectedLocationThree}
                        >
                            <option value={''}>{'All'} </option>

                            {
                                locationThree.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>
                        {/* <Dropdown value={selectedLocationThree} onChange={(e) => setSelectedLocationThree(e.value)} options={locationThree} optionLabel="name"
                            placeholder="Select" className="" /> */}
                    </div>
                </div>

                <div className="col-12">
                    <div className='mb-4 d-flex flex-column'>
                        <label className='mb-2 font-sm'> {title.tier4}</label>
                        <select
                            className='form-control'
                            onChange={(e) => setSelectedLocationFour(e.target.value)}
                            value={selectedLocationFour}
                        >
                            <option value={''}>{'All'} </option>

                            {
                                locationFour.map(i => {
                                    return <option key={i.id} value={i.id}>{i.name}</option>

                                })
                            }
                        </select>

                        {/* <Dropdown value={selectedLocationFour} onChange={(e) => setSelectedLocationFour(e.value)} options={locationFour} optionLabel="name"
                            placeholder="Select" className="" /> */}

                    </div>
                </div>



            </div>
        </>
    )
}

export default AllFilterLocation;