import React, { useState, useEffect, useRef } from 'react';
import { Form, ListGroup } from 'react-bootstrap';

function AutoComplete({ options, optionLabel = 'name', optionValue, getSelectedValue, initialValue }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [selectedValue, setSelectedValue] = useState(initialValue);
    const searchContainerRef = useRef();
    const inputRef = useRef();

    useEffect(() => {
        setSelectedValue(initialValue);
    }, [initialValue]);

    const handleSearchChange = (event) => {
        const newSearchTerm = event.target.value;
        setSearchTerm(newSearchTerm);

        const filteredOptions = options.filter(option =>
            option[optionLabel].toLowerCase().includes(newSearchTerm.toLowerCase())
        );

        setSearchResults(filteredOptions);
    };
    const handleClearInput = () => {
        setSearchTerm('');
        setSelectedValue(null);
    };
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchContainerRef.current && !searchContainerRef.current.contains(event.target)) {
                setSearchResults([]);

                const foundOption = options.find(option => option[optionLabel] === searchTerm);
                if (!foundOption) {
                    setSearchTerm(''); // Reset the input value
                    setSelectedValue(null); // Reset the selected value
                }
            }
        };

        window.addEventListener('click', handleClickOutside);

        return () => {
            window.removeEventListener('click', handleClickOutside);
        };
    }, [options, optionLabel, searchTerm]);

    const handleOptionSelect = (selectedOption) => {
        setSelectedValue(selectedOption);
        setSearchTerm(selectedOption[optionLabel]);
        setSearchResults([]);
        getSelectedValue(selectedOption);
    };

    return (
        <div ref={searchContainerRef} className="search-dropdown  col-12">
            <div className="search-input-container">
                <Form.Control
                    type="text"
                    placeholder="Search"
                    value={searchTerm}
                    onChange={handleSearchChange}
                    ref={inputRef}
                />
            </div>
            {searchTerm && (
                <i className="material-icons clear-icon" onClick={handleClearInput} >close</i>
            )}
            {searchResults.length > 0 && (
                <ListGroup className="search-list">
                    {searchResults.map((result, index) => (
                        <ListGroup.Item
                            key={index}
                            onClick={() => handleOptionSelect(result)}
                        >
                            {result[optionLabel]}
                        </ListGroup.Item>
                    ))}
                </ListGroup>
            )}

        </div>
    );
}

export default AutoComplete;
