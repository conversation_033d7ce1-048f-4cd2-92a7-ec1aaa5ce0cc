import React from "react";
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { userColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';

const UserTable = (props) => {
    const defaultMaterialTheme = createTheme();
    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
        zIndex: 0
    };
    return (
        <CardOverlay>
            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={props.data}
                    title="Assigned User Data"
                    style={tableStyle}
                  
                    options={tableOptions(props.customTitle)}
                    
          
                />
            </ThemeProvider>
        </CardOverlay>
    )
}

export default UserTable;