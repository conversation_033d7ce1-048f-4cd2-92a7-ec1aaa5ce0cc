import React from "react";
import Chart from 'react-apexcharts';

const WordChart = (props) => {
    const options = {
        "legend": {
            "show": false
        },
        "chart": {
            "height": 350,
            "type": "treemap"
        },
        "title": {
            "text": "",
            "align": "center"
        },
        "colors": [
            "#3B93A5",
            "#F7B844",
            "#ADD8C7",
            "#EC3C65",
            "#CDD7B6",
            "#C1F666",
            "#D43F97",
            "#1E5D8C",
            "#421243",
            "#7F94B0",
            "#EF6537",
            "#C0ADDB"
        ],
        "plotOptions": {
            "treemap": {
                "distributed": true,
                "enableShades": false
            }
        }
    }

    const series = [
        {
            "data": [
                {
                    "x": "Head",
                    "y": 218
                },
                {
                    "x": "Neck",
                    "y": 149
                },
                {
                    "x": "Chest",
                    "y": 184
                },
                {
                    "x": "Abdomen",
                    "y": 55
                },
                {
                    "x": "Right Arm",
                    "y": 84
                },
                {
                    "x": "Left Arm",
                    "y": 31
                },
                {
                    "x": "Right Leg",
                    "y": 70
                },
                {
                    "x": "Left Leg",
                    "y": 30
                },
                {
                    "x": "Back",
                    "y": 44
                },
                {
                    "x": "Shoulder",
                    "y": 68
                },
                {
                    "x": "Hand",
                    "y": 28
                },
                {
                    "x": "Foot",
                    "y": 19
                },
                {
                    "x": "Hip",
                    "y": 29
                }
            ]
        }
    ]

    return (
        <>
            <Chart
                options={options}
                series={series}
                type="treemap"
                height="400"
            />
        </>
    )
}

export default WordChart;
