import $ from "jquery";
import React, { useRef, useEffect } from "react";
import Swal from "sweetalert2";
import { useHistory } from 'react-router-dom';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;

// @ts-ignore
require("jquery-ui-sortable");
// @ts-ignore
require("formBuilder");

const FormBuilder = (props) => {
  const history = useHistory();
  const fb = useRef();

  useEffect(() => {
    if (props.values) {
      const fields = [{
        label: 'Media Picker',
        attrs: {
          type: 'multimedia'
        },
        icon: '<i class="mdi mdi-filmstrip"></i>',
      }, {
        label: 'Signature',
        attrs: {
          type: 'sign'
        },
        icon: '<i class="mdi mdi-pen"></i>',
      }];

      const templates = {
        multimedia: function (fieldData) {
          return {
            field: '<span id="' + fieldData.name + '">',
            onRender: function () {
              $(document.getElementById(fieldData.name));
            }
          };
        },
        sign: function (fieldData) {
          return {
            field: '<span id="' + fieldData.name + '">',
            onRender: function () {
              $(document.getElementById(fieldData.name));
            }
          };
        },
        'checklist-group': function (fieldData) {
          return {
            field: `
            <div id="${fieldData.name}" class="checklist-group">
              <input type="text" placeholder="Enter group name" name="${fieldData.name}-group-name" class="group-name"/><br/>
              <div class="checklist-items">
                <div class="checklist-item">
                  <input type="text" placeholder="Enter checklist item name" name="${fieldData.name}-item1" class="item-name"/><br/>
                  <input type="radio" name="${fieldData.name}-item1-response" value="Yes"> Yes<br>
                  <input type="radio" name="${fieldData.name}-item1-response" value="No"> No<br>
                  <input type="radio" name="${fieldData.name}-item1-response" value="N/A"> Not Applicable<br>
                </div>
              </div>
              <button type="button" class="add-checklist-item">Add Checklist Item</button>
            </div>
          `,
            onRender: function () {
              const $group = $(document.getElementById(fieldData.name));
              $group.find('.add-checklist-item').on('click', function () {
                const itemCount = $group.find('.checklist-item').length + 1;
                const newItem = `
                  <div class="checklist-item">
                    <input type="text" placeholder="Enter checklist item name" name="${fieldData.name}-item${itemCount}" class="item-name"/><br/>
                    <input type="radio" name="${fieldData.name}-item${itemCount}-response" value="Yes"> Yes<br>
                    <input type="radio" name="${fieldData.name}-item${itemCount}-response" value="No"> No<br>
                    <input type="radio" name="${fieldData.name}-item${itemCount}-response" value="N/A"> Not Applicable<br>
                  </div>
                `;
                $group.find('.checklist-items').append(newItem);
              });
            }
          };
        }
      };

      const options = {
        fields,
        templates,
        stickyControls: {
          enable: true
        },
        controlPosition: 'left',
        disabledActionButtons: ['data'],
        actionButtons: [{
          id: 'go-back',
          className: 'btn btn-light',
          label: 'Back',
          type: 'button',
          events: {
            click: function () {
              history.push('/checklist')
            }
          }
        }],
        disabledAttrs: [
          'access',
          'className',
          'inline',
          'name',
          'other',
          'rows',
          'step',
          'style',
          'subtype',
          'toggle'
        ],
        disableFields: ['autocomplete', 'button', 'hidden', 'number',
          'select', 'starRating', 'text',
          'file'
        ],
        onSave: function (evt, formData) {
          // Convert formData to JSON and ensure it's correctly formatted
          let parsedFormData = JSON.parse(formData);

          // Process each checklist-group to include group name, item options, and ensure default values
          parsedFormData = parsedFormData.map(field => {
            if (field.type === 'checklist-group') {
              const groupName = $(`[name="${field.name}-group-name"]`).val();
              const checklistItems = [];
              const itemCount = $(document.getElementById(field.name)).find('.checklist-item').length;
              for (let i = 1; i <= itemCount; i++) {
                checklistItems.push({
                  label: $(`[name="${field.name}-item${i}"]`).val(),
                  value: $(`[name="${field.name}-item${i}-response"]:checked`).val() || "",
                  options: [
                    { label: "Yes", value: "Yes" },
                    { label: "No", value: "No" },
                    { label: "Not Applicable", value: "N/A" }
                  ]
                });
              }
              field.groupName = groupName;
              field.items = checklistItems;
            }
            return field;
          });

          console.log(parsedFormData);

          props.onSubmit(JSON.stringify(parsedFormData));
        },
      };

      // @ts-ignore
      let formBuilderPlugin = $(fb.current).formBuilder(options);
      setTimeout(() => formBuilderPlugin.actions.setData(props.values), 1000)
    }
  }, [props.values]);

  return (
    <>
      <div id="fb-editor" ref={fb} />
      <button type="button" onClick={() => addChecklistGroup(fb.current)}>Add New Group</button>
    </>
  );

  function addChecklistGroup(fbEditor) {
    const newGroupId = `checklist-group-${Date.now()}`;
    $(fbEditor).append(`
      <div class="form-group" id="${newGroupId}">
        <label for="${newGroupId}-group-name">Group Name</label>
        <input type="text" id="${newGroupId}-group-name" name="${newGroupId}-group-name" class="form-control" placeholder="Enter group name" />
        <div class="checklist-items"></div>
        <button type="button" class="btn btn-secondary add-checklist-item">Add Checklist Item</button>
      </div>
    `);
    
    // Attach event listener for the new "Add Checklist Item" button
    $(`#${newGroupId} .add-checklist-item`).on('click', function() {
      addChecklistItem(newGroupId);
    });
  }

  function addChecklistItem(groupId) {
    const itemCount = $(`#${groupId} .checklist-item`).length + 1;
    $(`#${groupId} .checklist-items`).append(`
      <div class="checklist-item">
        <input type="text" name="${groupId}-item${itemCount}" class="form-control" placeholder="Enter checklist item name" />
        <div class="form-check">
          <input class="form-check-input" type="radio" name="${groupId}-item${itemCount}-response" value="Yes">
          <label class="form-check-label">Yes</label>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="radio" name="${groupId}-item${itemCount}-response" value="No">
          <label class="form-check-label">No</label>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="radio" name="${groupId}-item${itemCount}-response" value="N/A">
          <label class="form-check-label">Not Applicable</label>
        </div>
      </div>
    `);
  }
}

export default FormBuilder;
