# Image Upload Utilities

This module provides utilities for handling image uploads with HEIC format support in the Audit components.

## Features

- **HEIC Support**: Automatically converts HEIC/HEIF files to JPEG format
- **Compression**: Applies compression during HEIC to JPEG conversion (default 80% quality)
- **File Validation**: Validates supported image file types
- **Error Handling**: Graceful error handling with user-friendly messages

## Supported Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- HEIC (.heic)
- HEIF (.heif)

## Usage

### In Audit Components

The following components now support HEIC format:

1. `OpportunityForImprovementForm`
2. `NonConformancesForm` 
3. `GoodPracticesForm`

### Key Functions

#### `processUploadedFiles(files, quality = 0.8)`

Processes an array of files, converting HEIC files to JPEG with compression.

**Parameters:**
- `files`: Array of File objects or FileList
- `quality`: Compression quality for HEIC conversion (0.1 to 1.0, default: 0.8)

**Returns:** Promise<Array> - Array of processed files

#### `isHeicFile(file)`

Checks if a file is in HEIC/HEIF format.

**Parameters:**
- `file`: File object to check

**Returns:** boolean

#### `getAcceptedFileTypes()`

Returns a comma-separated string of accepted file types for use with DropzoneArea.

**Returns:** string

#### `isValidImageFile(file)`

Validates if a file is a supported image type.

**Parameters:**
- `file`: File object to validate

**Returns:** boolean

## Implementation Details

### HEIC Conversion Process

1. **Detection**: Files are checked for HEIC format using MIME type and file extension
2. **Conversion**: HEIC files are converted to JPEG using the `heic2any` library
3. **Compression**: Converted files are compressed to reduce file size
4. **File Creation**: New File objects are created with `.jpg` extension

### User Experience

- **Loading Indicator**: Shows "Converting HEIC files..." message during processing
- **Error Handling**: Displays user-friendly error messages if conversion fails
- **Disabled State**: Upload area is disabled during file processing
- **File Naming**: HEIC files are renamed with `.jpg` extension after conversion

### Performance Considerations

- **Asynchronous Processing**: File conversion is handled asynchronously
- **Quality Settings**: Default 80% quality provides good balance between file size and image quality
- **Memory Management**: Processed files replace original HEIC files in memory

## Dependencies

- `heic2any`: Library for HEIC to JPEG conversion
- `material-ui-dropzone`: File upload component

## Error Handling

The utility includes comprehensive error handling:

- **Conversion Errors**: Caught and logged with user-friendly messages
- **File Validation**: Invalid file types are rejected
- **Network Issues**: Graceful handling of upload failures

## Testing

Unit tests are provided in `__tests__/imageUtils.test.js` covering:

- File type detection
- File validation
- Accepted file types
- File processing workflows
