function transformApiResponse(apiResponseArray) {
    return apiResponseArray
        .filter(apiResponse => parseInt(apiResponse.yearAndMonth.split(" ")[1]) === 2025) // Only keep 2025 data
        .map(apiResponse => {
            // Extract year and month
            const [month, year] = apiResponse.yearAndMonth.split(" ");

            // Extract country from locationOne
            const country = apiResponse.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

            // Determine BU based on locationThree.name
            let bu = "Other";
            if (apiResponse.locationThree?.name.includes("Construction")) {
                bu = "Construction";
            } else if (apiResponse.locationThree?.name.includes("DC")) {
                bu = "DC";
            } else if (apiResponse.locationThree?.name.includes("Office")) {
                bu = "Office";
            }

            // Format site name (replace spaces with underscores)
            const site = apiResponse.locationFour?.name.replace(/\s+/g, "_") || "Unknown_Site";

            return {
                year: parseInt(year),
                country,
                bu,
                month: month.toLowerCase(),
                level: "site",
                site,

                // Employee data
                averageNoOfSTTGdcEmployeesPerDay: apiResponse.numberOfEmployees / (apiResponse.workingDaysOfEmployee || 1),
                averageNoOfContractorEmployeesPerDay: apiResponse.numberofContractors / (apiResponse.workingDaysOfContractors || 1),
                totalNoOfEmployees: apiResponse.numberOfEmployees + apiResponse.numberofContractors,

                // Work hours
                monthlyHoursWorked: (apiResponse.dailyHoursOfEmployee * apiResponse.workingDaysOfEmployee || 0) +
                    (apiResponse.dailyHoursOfContractors * apiResponse.workingDaysOfContractors || 0),
                cumulativeWorkHours: (apiResponse.dailyHoursOfEmployee * apiResponse.workingDaysOfEmployee || 0) +
                    (apiResponse.dailyHoursOfContractors * apiResponse.workingDaysOfContractors || 0),

                // Safety and Training
                noOfSafetyInductionsConducted: apiResponse.noOfSafety || 0,
                noOfToolboxMeetingsSafetyBriefingsSafeStarts: apiResponse.noOfToolbox || 0,
                noOfEHSTrainings: apiResponse.noOfEhsTraining || 0,
                noOfEHSInspectionsAudits: apiResponse.noOfInspection || 0,
                noOfManagementSiteWalkInspection: apiResponse.noOfManagmentSiteWalk || 0,
                authorityNGOUnionVisits: apiResponse.noOfAuthority || 0,

                // Observations
                noOfSafeObservations: apiResponse.noOfSafeObservation || 0,
                noOfAtRiskObservations: apiResponse.noOfRiskObservation || 0,
                totalNoOfObservations: (apiResponse.noOfSafeObservation || 0) + (apiResponse.noOfRiskObservation || 0),

                // Incidents & Compliance (default to 0 if not provided)
                noOfFatality: 0,
                noOfDaysAwayFromWorkCasesLTICases: 0,
                noOfRestrictedWorkCasesLightDutyJobTransfer: 0,
                noOfLossOfConsciousnessCases: 0,
                noOfMedicalTreatmentCases: 0,
                noOfHealthRelatedCases: 0,
                noOfRecordableIncidentCases: 0,
                legalAndOtherNonCompliances: 0,
                noticesOrStopWorkOrders: 0,
                authorityReportableIncident: 0,
                noOfNearMissCases: 0,
                noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence: 0,
                noOfFirstAidCases: 0
            };
        });
}