import React, { useState, useEffect } from 'react';
import HighchartsWrapper from './HighchartsWrapper';
import API from '../services/API';
import { ALL_INCIDENT_URL } from '../constants';
import moment from 'moment';
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';

const IncidentCategoryPieChart = ({ data, dateRange, filterCriteria }) => {
    const impactLevels = [
        'Near-Miss',
        'Level 1 (First Aid Incident - FAI)',
        'Level 2 (Medical Treatment Incident - MTI)',
        'Level 3 (Lost Time Incident - LTI)',
        'Level 4 (High Severity Incident)',
        'Level 5 (Critical Incident)'
    ];
    const [chartData, setChartData] = useState(null);
    const [selectedImpacts, setSelectedImpacts] = useState(impactLevels);

    useEffect(() => {
        const fetchData = async () => {
            try {


                if(!data) return;
                let incidents = data;

                console.log(incidents, ' incidents ')

                incidents = incidents.filter(item => {
                    // Extract country from locationOne.name (text inside parentheses)
                    const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                    // Determine BU from locationThree.name
                    let bu = "Other";
                    const buName = item.locationThree?.name || "";

                    if (/Construction|Fitouts/i.test(buName)) {
                        bu = "Construction";
                    } else if (/DC|Data Center|Data Centre/i.test(buName)) {
                        bu = "DC";
                    } else if (/Office/i.test(buName)) {
                        bu = "Office";
                    }

                    // Normalize impact level
                    const impactRaw = (item.actualImpact || '').toLowerCase().trim();
                    let impactLevel = null;
                    if (impactRaw.includes("near miss")) {
                        impactLevel = "Near-Miss";
                    } else if (impactRaw.includes("level 1")) {
                        impactLevel = "Level 1 (First Aid Incident - FAI)";
                    } else if (impactRaw.includes("level 2")) {
                        impactLevel = "Level 2 (Medical Treatment Incident - MTI)";
                    } else if (impactRaw.includes("level 3")) {
                        impactLevel = "Level 3 (Lost Time Incident - LTI)";
                    } else if (impactRaw.includes("level 4")) {
                        impactLevel = "Level 4 (High Severity Incident)";
                    } else if (impactRaw.includes("level 5")) {
                        impactLevel = "Level 5 (Critical Incident)";
                    }





                    // Now apply the filters
                    const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
                    const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
                    const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;
                    const isImpactSelected = selectedImpacts.length === 0 ||
                        (impactLevel && selectedImpacts.includes(impactLevel));

                    return isCountrySelected && isBUSelected && isSiteSelected && isImpactSelected;
                });
                // Parse start and end from dateRange
                const [startDate, endDate] = dateRange.map(date => new Date(date));

                // Filter incidents by date range using incidentDate
                const filteredIncidents = incidents.filter((incident) => {
                    const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A");
                    return incidentMoment.isBetween(startDate, endDate, null, '[]'); // Inclusive
                });
                console.log('Fetched filtered:', filteredIncidents);
                const totalIncidents = filteredIncidents.length;
                const categoryCounts = {};

                // Group and count incidents by category
                filteredIncidents.forEach((incident) => {
                    const category = incident.IncidentCategory || 'Unknown';
                    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
                });

                // Format for pie chart
                const categoryDistribution = Object.entries(categoryCounts).map(([category, count]) => ({
                    category,
                    count,
                    percentage: `${((count / totalIncidents) * 100).toFixed(2)}%`,
                }));

                // console.log(categoryDistribution, 'categoryDistribution');
                const labels = categoryDistribution.map(item => item.category);
                const chartDataValues = categoryDistribution.map(item => item.count);

                setChartData({ labels, data: chartDataValues });
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, [dateRange, selectedImpacts, data]); // Add selectedImpacts to dependency array

    // Highcharts Config
    const options = chartData
        ? {
            chart: { type: 'pie' },
            title: { text: `` },
            tooltip: { pointFormat: '<b>{point.name}</b>: {point.y}' },
            accessibility: { point: { valueSuffix: ' incidents' } },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.y}',
                    },
                    showInLegend: true,
                },
            },
            legend: {
                enabled: true,
                labelFormatter: function () {
                    return `${this.name} (${this.y})`;
                },
            },
            series: [
                {
                    name: 'Incidents',
                    colorByPoint: true,
                    data: chartData.labels.map((label, index) => ({
                        name: label,
                        y: chartData.data[index],
                    })),
                },
            ],
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            'downloadPNG',
                            'downloadJPEG',
                            'downloadPDF',
                            'downloadSVG',
                            'separator',
                            'downloadCSV',
                            'downloadXLS',
                        ],
                    },
                },
            },
        }
        : null;

    const handleImpactChange = (value) => {
        setSelectedImpacts(value);
    };

    return (
        <div>
            <div style={{ marginBottom: '20px' }}>
                <MultiSelect
                    value={selectedImpacts}
                    options={impactLevels}
                    onChange={(e) => handleImpactChange(e.value)}
                    placeholder="Select Impact Levels"
                    style={{ width: '100%' }}
                    display="chip"
                />
            </div>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default IncidentCategoryPieChart;
