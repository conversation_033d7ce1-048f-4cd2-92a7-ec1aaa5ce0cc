import React from 'react';


const ComplianceTable = () => {
    const scoreInfo = [
        {
            score: 0,
            category: "Not Applicable",
            criteria: `
                <ul>
                    <li>Item/clause not applicable to the project/DC ops.</li>
                </ul>
            `
        },
        {
            score: 1,
            category: "Major Non-Compliance",
            criteria: `
                <p style="font-weight: normal;">System failure or material reduction in the ability to provide EHS assurance due to:</p>
                <ul>
                    <li>System not in place or inadequately implemented on project/DC Ops</li>
                    <li>Absence or deficiency of a required standard or practice defined in the management system</li>
                    <li>Failure to address a key requirement or standard</li>
                    <li>Contravention of legislative or code requirements</li>
                    <li><strong>Significant risk</strong> of injury, illness, or environmental impact</li>
                </ul>
            `
        },
        {
            score: 2,
            category: "Minor Non-Compliance",
            criteria: `
                <p style="font-weight: normal;">Minimal risk of negative impact or non-compliance, which is not likely to result in a system failure or major EHS impacts, due to:</p>
                <ul>
                    <li>Some deviation from required standards or practices</li>
                    <li>Partial adherence to standard/practice</li>
                    <li>Single observed lapse or isolated condition</li>
                    <li><em>Minor deviation</em> from legislative or code requirements</li>
                </ul>
            `
        },
        {
            score: 3,
            category: "Compliant, with opportunity for improvement",
            criteria: `
                <p style="font-weight: normal;">System is adequately in place; adequate implementation to meet required compliance standards; or compliant with improvement opportunities.</p>
               
            `
        }
    ];

    return (
        <div className="score-details-container">
            {scoreInfo.map((info, index) => (
                <div key={index} className="score-box">
                    <h4>Score rating: {info.score}</h4>
                    <p>Category: {info.category}</p>
                    <div dangerouslySetInnerHTML={{ __html: info.criteria }}></div>
                </div>
            ))}
        </div>
    );
};

export default ComplianceTable;
