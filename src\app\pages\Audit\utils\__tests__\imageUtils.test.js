import { 
  isHeicFile, 
  isValidImageFile, 
  getAcceptedFileTypes,
  processUploadedFiles 
} from '../imageUtils';

// Mock heic2any since it requires browser environment
jest.mock('heic2any', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('imageUtils', () => {
  describe('isHeicFile', () => {
    it('should identify HEIC files by MIME type', () => {
      const heicFile = new File([''], 'test.heic', { type: 'image/heic' });
      const heifFile = new File([''], 'test.heif', { type: 'image/heif' });
      
      expect(isHeicFile(heicFile)).toBe(true);
      expect(isHeicFile(heifFile)).toBe(true);
    });

    it('should identify HEIC files by extension', () => {
      const heicFile = new File([''], 'test.HEIC', { type: '' });
      const heifFile = new File([''], 'test.heif', { type: '' });
      
      expect(isHeicFile(heicFile)).toBe(true);
      expect(isHeicFile(heifFile)).toBe(true);
    });

    it('should not identify non-HEIC files', () => {
      const jpgFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const pngFile = new File([''], 'test.png', { type: 'image/png' });
      
      expect(isHeicFile(jpgFile)).toBe(false);
      expect(isHeicFile(pngFile)).toBe(false);
    });
  });

  describe('isValidImageFile', () => {
    it('should validate supported image types', () => {
      const jpgFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const pngFile = new File([''], 'test.png', { type: 'image/png' });
      const heicFile = new File([''], 'test.heic', { type: 'image/heic' });
      
      expect(isValidImageFile(jpgFile)).toBe(true);
      expect(isValidImageFile(pngFile)).toBe(true);
      expect(isValidImageFile(heicFile)).toBe(true);
    });

    it('should reject unsupported file types', () => {
      const txtFile = new File([''], 'test.txt', { type: 'text/plain' });
      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      
      expect(isValidImageFile(txtFile)).toBe(false);
      expect(isValidImageFile(pdfFile)).toBe(false);
    });
  });

  describe('getAcceptedFileTypes', () => {
    it('should return correct accepted file types string', () => {
      const acceptedTypes = getAcceptedFileTypes();
      
      expect(acceptedTypes).toContain('image/jpeg');
      expect(acceptedTypes).toContain('image/png');
      expect(acceptedTypes).toContain('image/heic');
      expect(acceptedTypes).toContain('image/heif');
      expect(acceptedTypes).toContain('.heic');
      expect(acceptedTypes).toContain('.heif');
    });
  });

  describe('processUploadedFiles', () => {
    it('should process non-HEIC files without conversion', async () => {
      const jpgFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const files = [jpgFile];
      
      const result = await processUploadedFiles(files);
      
      expect(result).toHaveLength(1);
      expect(result[0]).toBe(jpgFile);
    });

    it('should handle empty file arrays', async () => {
      const result = await processUploadedFiles([]);
      
      expect(result).toHaveLength(0);
    });
  });
});
