import React, { useEffect, useState, useMemo } from "react";
import GalleryPage from '../apps/Gallery';
import LightingDropdown from './LightingDropdown';
import WeatherConditionDropdown from './WeatherConditionDropdown';
import { Modal, Button, Form } from 'react-bootstrap';
import { useSelector } from "react-redux";
import { DropzoneArea } from 'material-ui-dropzone';
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import DynamicLocation from './DynamicLocation';
import LocationDropdown from './LocationDropdown';
import RiskCategoryDropdown from './RiskCategoryDropdown';
import WorkActivityDropdown from './WorkActivityDropdown';
import SurfaceTypeDropdown from './SurfaceTypeDropdown';
import SurfaceConditionDropdown from './SurfaceConditionDropdown';
import IncidentCircumstanceDropdown from './IncidentCircumstanceDropdown';
import FilterLocation from './FilterLocation';
import { secondaryPopup } from '../notifications/Swal';
import cogoToast from 'cogo-toast';
import { deletePopup, singlePopup } from "../notifications/Swal";
import DataTables from '../tables/DataTables';
import { ALL_USERS_BY_LOCATION, API_URL, INCIDENT_OWNER_LIST_URL, LEAD_INVESTIGATOR_URL, REPORT_INCIDENT_INVESTIGATE_URL_WITH_ID, REPORT_INCIDENT_REVIEW_URL_WITH_ID, REPORT_INCIDENT_URL_WITH_ID, RESUBMIT_REPORT_INCIDENT_URL_WITH_ID, RETURN_REPORT_INCIDENT_URL_WITH_ID, SAVE_REPORT_INCIDENT_URL_WITH_ID, STATIC_URL } from "../constants";
import API from "../services/API";
import moment from 'moment';
import DynamicDropdowns from "./DynamicDropdowns";
import InfoTooltip from "./InfoTooltip";
import Select from 'react-select';
import ImageView from "./ImageView";
import { DataView } from 'primereact/dataview';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/themes/saga-blue/theme.css';  //theme
import 'primereact/resources/primereact.min.css';          //core css
import 'primeicons/primeicons.css';
import { Dialog } from 'primereact/dialog';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';

const IncidentInformationModal = ({ readOnly, type, id, showModal, setShowModal, setType, setReadOnly }) => {
    const me = useSelector((state) => state.login.user)
    const isEditable = useMemo(() => {
        console.log(me?.validationRoles)
        return me?.validationRoles?.some(item => item.name === 'Group EHS Team') || false;
    }, [me]);

    // useEffect(() => {console.log(type, 'type')}, [type])
    const [visible, setVisible] = useState(false);
    const [show, setShow] = useState(false);

    const handleClose = () => setShow(false);
    const handleShow = () => setShow(true);
    const data = [
        [1, "Client and Client's contractor incident happened in STT GDC areas", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [2, "Client and Client's contractor incident happened in client’s areas that is not accessible by STT GDC", "Client’s Investigation Process (if applicable)", "Report as Non-Work-Related Incident", "No"],
        [3, "STT GDC’s employee’s incident happened in client’s areas", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [4, "STT GDC’s employees and contractors (managing agent and their sub con) incident happened in STT GDC areas", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [5, "STT GDC’s employees using own vehicle to travel from home to work", "No Investigation Needed", "Report as Non-Work-Related Incident", "No"],
        [6, "STT GDC’s employees using company vehicle to travel from home to work", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [7, "STT GDC’s employees using own vehicle or public transport to travel from one STT location to another STT GDC’s location for work", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [8, "STT GDC’s employees using own vehicle to travel from one STT GDC location to another STT GDC location for work, but detour along the way for personal’s trip/reasons. E.g., to run personal errands.", "No Investigation Needed", "Report as Non-Work-Related Incident", "No"],
        [9, "STT GDC’s employee on overseas business trip", "STT GDC RCA", "Report as Work-Related Incident", "Yes"],
        [10, "STT GDC’s employee suffer incident while working from home", "No Investigation Needed", "Report as Non-Work-Related Incident", "No"],
        [11, "Health related issue (STT GDC’s employees, contractors, clients and visitors) that is level 3 and below, not caused by the work.", "No Investigation Needed", "Report as Non-Work-Related Incident", "No"],
        [12, "Health related with permanently disabling or fatal outcome (STT GDC’s employees, contractors, clients and visitors) that is not caused by the work.", "STT GDC RCA", "Report as Non-Work-Related Incident", "No"]
    ];

    const workRelated = data.filter(d => d[3] === "Report as Work-Related Incident");
    const nonWorkRelated = data.filter(d => d[3] === "Report as Non-Work-Related Incident");

    const columns = [
        { field: '0', header: 'No.' },
        { field: '1', header: 'Scenario' },
        { field: '2', header: 'Type of Incident Investigation' },
        { field: '3', header: 'Incident Scope' },
        { field: '4', header: 'Include in STT GDC EHS Statistics?' }
    ];

    const renderTable = (rows) => (
        <DataTable value={rows} responsiveLayout="scroll" className="p-datatable-sm mt-3">
            {columns.map(col => (
                <Column key={col.field} field={col.field} header={col.header} />
            ))}
        </DataTable>
    );

    const [files, setFiles] = useState([]);
    const [incidentData, setIncidentData] = useState({})
    const [incidentDate, setIncidentDate] = useState('')
    const [injury, setInjury] = useState(false)
    const [firstAid, setFirstAid] = useState(false)
    const [personInjured, setPersonInjured] = useState(false);
    const [medicalTreatment, setMedicalTreatment] = useState(false);
    const [medicalLeave, setMedicalLeave] = useState(false);
    const [isControlMeasure, setIsControlMeasure] = useState(false);
    const [isRiskAssessment, setIsRiskAssessment] = useState(false);
    const [isPropertyDamage, setIsPropertyDamage] = useState(false);
    const [isWorkRelated, setIsWorkRelated] = useState(true);
    const [workRelatedDetails, setWorkRelatedDetails] = useState(false);
    const [propertyDetails, setPropertyDetails] = useState('')
    const [option8, setOption8] = useState("");
    const [option7, setOption7] = useState("");
    const [option6, setOption6] = useState("");
    const [option5, setOption5] = useState("");
    const [option4, setOption4] = useState("");
    const [option3, setOption3] = useState("");
    const [option2, setOption2] = useState("");
    const [option1, setOption1] = useState("");
    const [status, setStatus] = useState("");
    const [remarks, setRemarks] = useState("");
    const [incidentOwnerId, setIncidentOwnerId] = useState("");
    const [reportAuthority, setReportAuthority] = useState('No');
    const [stopWorkOrder, setStopWorkOrder] = useState('No');
    const [classification, setClassification] = useState('Normal');
    const [authorityName, setAuthorityName] = useState("{\"name\":\"\",\"reportedDate\":\"\"}");

    const handleSwitchChange = (field, checked) => {
        const value = field !== 'workRelated' ? (checked ? 'Yes' : 'No') : checked;
        switch (field) {
            case 'reportAuthority':
                setReportAuthority(value);
                break;
            case 'stopWorkOrder':
                setStopWorkOrder(value);
                break;
            case 'workRelated':
                setIsWorkRelated(value);
                break;
            case 'propertyDamage':
                setIsPropertyDamage(value === 'Yes' ? true : false);

                break;
            default:
                break;
        }
    };

    const handleAuthorityChange = (value, part) => {
        const authority = JSON.parse(authorityName);
        authority[part] = value;
        setAuthorityName(JSON.stringify(authority));
    };

    //For Trigger Investigatin
    const [iusers, setIUsers] = useState([])
    const [requireInvestigation, setRequireInvestigation] = useState(false);
    const [investigationRemarks, setInvestigationRemarks] = useState('')
    const [investigatorId, setInvestigatorId] = useState('')
    const [bodyValues, setBodyValues] = useState({})
    //End for trigger investigation
    const [InformationData, setInformationData] = useState({
        whenIncidentHappen: "",
        files: [],
        personInvolved: "",
        witness: "",
        howIncidentHappen: "",
        equipment: "",
        activitiesPerformed: "",
        unsualActivity: "",
        safeProcedure: "",
        administeredBy: "",
        medicalFaciltiyName: "",
        driverName: "",
        treatmentDescription: "",
        medicalLeaveDays: 0,
        regularWorkDate: "",
        modifiedWorkDate: "",
        regularWork: false,
        modifiedWork: false,
        ambulance: false,
        companyVehicle: false,
        privateVehicle: false,
        body: {
            head: {
                show: true,
                selected: false
            },
            left_shoulder: {
                show: true,
                selected: false
            },
            right_shoulder: {
                show: true,
                selected: false
            },
            left_arm: {
                show: true,
                selected: false
            },
            right_arm: {
                show: true,
                selected: false
            },
            chest: {
                show: true,
                selected: false
            },
            stomach: {
                show: true,
                selected: false
            },
            left_leg: {
                show: true,
                selected: false
            },
            right_leg: {
                show: true,
                selected: false
            },
            left_hand: {
                show: true,
                selected: false
            },
            right_hand: {
                show: true,
                selected: false
            },
            left_foot: {
                show: true,
                selected: false
            },
            right_foot: {
                show: true,
                selected: false
            }
        },
        dropdownSelections: {}

    })
    const [activeStep, setActiveStep] = useState(0);

    const [riskControl, setRiskControl] = useState({
        immediateActions: [
            {
                date: new Date(),
                description: ""
            }
        ],
        controlMeasures: [
            {
                completionDate: new Date(),
                personResponsible: "",
                controlMeasures: ""
            }
        ],
        riskAssessment: [
            {
                name: "",
                completionDate: new Date(),
                personResponsible: ""
            }
        ]
    })

    const validateControlMeasures = () => {
        console.log(isControlMeasure)
        if (!isControlMeasure) return true
        const { controlMeasures } = riskControl;
        for (const measure of controlMeasures) {
            if (!measure.completionDate || !measure.personResponsible || !measure.controlMeasures) {
                cogoToast.error('All fields in control measures must be filled out.')
                return false;
            } else return true
        }
    };

    const validateRiskAssessment = () => {
        console.log(isRiskAssessment)
        if (!isRiskAssessment) return true
        const { riskAssessment } = riskControl;
        for (const assessment of riskAssessment) {
            if (!assessment.name || !assessment.completionDate || !assessment.personResponsible) {
                cogoToast.error('All fields in risk assessment must be filled out.')
                return false;
            } else return true
        }
    };

    const [locationOneId, setLocationOneId] = useState('')
    const [locationTwoId, setLocationTwoId] = useState('');
    const [locationThreeId, setLocationThreeId] = useState('');
    const [locationFourId, setLocationFourId] = useState('');
    const [locationFiveId, setLocationFiveId] = useState('');
    const [locationSixId, setLocationSixId] = useState('');

    const [selectedReviewer, setSelectedReviewer] = useState('');
    const [locationUsers, setLocationUsers] = useState([]);

    const [dropdownSets, setDropdownSets] = useState([{ id: 0, selectedHumanOne: '', selectedHumanTwo: '' }]);

    let nextId = 0;

    const addDropdownSet = () => {
        setDropdownSets([...dropdownSets, { id: ++nextId, selectedHumanOne: '', selectedHumanTwo: '' }]);
    };

    const deleteDropdownSet = (id) => {
        setDropdownSets(dropdownSets.filter(set => set.id !== id));
    };

    const updateSelectedValues = (id, selectedHumanOne, selectedHumanTwo) => {
        const updatedDropdowns = dropdownSets.map(set => {
            if (set.id === id) {
                return { ...set, selectedHumanOne, selectedHumanTwo };
            }
            return set;
        });
        setDropdownSets(updatedDropdowns);
    };
    useEffect(() => {
        getIUsers()
    }, [incidentData])

    const handleFileChange = async (files) => {
        if (files) {
            const uploadedFiles = await uploadFiles(files)
            setFiles((prev) => [...prev, ...uploadedFiles])
        }

    }

    const deleteFile = (index) => {
        const updatedFiles = [...files];
        updatedFiles.splice(index, 1);
        setFiles(updatedFiles);
    };

    useEffect(() => {
        setInformationData((prev) => ({
            ...prev,
            files: files
        }));
    }, [files]);

    const [incidentFiles, setIncidentFiles] = useState([])
    const handleIncidentFileChange = async (files) => {
        if (files) {
            const uploadedFiles = await uploadFiles(files)
            setIncidentFiles((prev) => [...prev, ...uploadedFiles])
        }

    }

    const deleteFileFromIncident = (index) => {
        const updatedFiles = [...incidentFiles];
        updatedFiles.splice(index, 1);
        setIncidentFiles(updatedFiles);
    };

    // Template for DataView item
    const handleWheel = (event) => {

        event.target.blur();  // Prevents scrolling changing the number
    };


    const getIUsers = async () => {

        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId) {
            const response = await API.post(LEAD_INVESTIGATOR_URL, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId });
            if (response.status === 200) {
                setIUsers(response.data)
            }
        }

    }

    useEffect(() => {
        if (id) {
            getReportIncident(id)
        }
    }, [id])




    useEffect(() => {

        const stringArray = [
            "fallofperson>2m/6ft",
            "fallofmaterials>2m/6ft-craneload",
            "fallofmaterials>2m/6ft-fixing/fixturefailure",
            "fallofmaterials>2m/6ft-scaffold",
            "fallofmaterials>2m/6ft-truckorvehicleload"
        ];
        if (option1 && option2 && option3 && option4 && option5 && option6 && incidentData) {


            if (
                option2 === "No. But, it could have" ||
                option3 === "No. But, it could have" ||
                (incidentData.incidentCircumstanceType?.name &&
                    stringArray.some(element =>
                        element.includes(incidentData.incidentCircumstanceType.name.replace(/\s+/g, '').toLowerCase())
                    )
                )
            ) {
                // setRemarks('Potentially Serious Incident')
            }

            if (option6 === "Yes") {
                setStatus("Level 1");


            }
            if (option5 === "Yes") {
                setStatus("Level 2");

            }
            if (option4 === "Yes") {
                setStatus("Level 3");


            }

            if (option3 === "Yes" || option3 === "No. But, it could have") {
                setStatus("Level 4");
                if (option3 === "Yes" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType?.name.replace(/\s+/g, '').toLowerCase()))) {
                    setRemarks('')
                }
            }

            if (option2 === "Yes") {
                setStatus("Level 5");
                if (option2 === "Yes" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType?.name.replace(/\s+/g, '').toLowerCase()))) {
                    setRemarks('')
                }
            }

            if (option2 === "No. But, it could have") {
                setStatus("Level 4");
            }
            if (option1 === "Yes" && option2 !== "Yes") {
                setStatus("Level 4");
            }

            if (option3 === "No" && option2 === "No" && option4 === "No" && !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType?.name.replace(/\s+/g, '').toLowerCase()))) {
                setRemarks('')
            }
            if (
                option6 === "No" &&
                option5 === "No" &&
                option4 === "No" &&
                option3 === "No" &&
                option2 === "No" &&
                option1 === "No" &&
                !stringArray.some(element => element.includes(incidentData.incidentCircumstanceType?.name.replace(/\s+/g, '').toLowerCase()))
            ) {
                setStatus("Near Miss");

                setRemarks('');

            }
        }

    }, [option6, option5, option4, option3, option2, option1, incidentData]);

    const [incidentOwners, setIncidentOwners] = useState([]);

    useEffect(() => {
        const level = getLevel(status);
        if (level !== null) {
            getIncidentOwner(level).then(data => {
                setIncidentOwners(data);
            }).catch(error => {
                console.error("Error fetching incident owners:", error);
            });
        }
    }, [status, id, incidentData]);

    // Function to get level based on status
    const getLevel = (status) => {
        switch (status) {
            case "Level 1":
                return '1';
            case "Level 2":
                return '2';
            case "Level 3":
                return '3';
            case "Level 4":
                return '4';
            case "Level 5":
                return '5';
            case "Near Miss":
                return '0';
            default:
                return null; // Return null for unknown status
        }
    };

    const getIncidentOwner = async (level) => {
        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId) {
            try {
                const response = await API.post(INCIDENT_OWNER_LIST_URL, {
                    locationOneId: incidentData.locationOneId,
                    locationTwoId: incidentData.locationTwoId,
                    locationThreeId: incidentData.locationThreeId,
                    locationFourId: incidentData.locationFourId,
                    level: level
                });
                if (response.status === 200) {
                    return response.data;
                } else {
                    return [];
                }
            } catch (error) {
                console.error("Error fetching incident owners:", error);
                return [];
            }
        } else {
            return [];
        }
    };

    // const getIncidentOwner = async () => {
    //     const response = await API.post()
    // }


    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const handleDateChange = (e) => {
        const selectedDate = e.value;
        const formattedDate = moment(selectedDate).format('DD/MM/YYYY hh:mm A');
        setIncidentDate(formattedDate);
    };

    const getReportIncident = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'weatherCondition', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

        const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {


            const data = response.data;
            if (data.informationStep)
                setDropdownSets(data.informationStep?.dropdownSelections || [])
            setFiles(data.informationStep?.files || [])
            setIncidentDate(data.incidentDate)
            setIncidentFiles(response.data.uploads)
            setIncidentData(data)
            setStatus(data.actualImpact)
            setOption1(data.dangerousOccurance)
            setOption2(data.fatality)
            setOption3(data.injury)
            setOption4(data.lostTime)
            setOption5(data.medicalTreatment)
            setOption6(data.firstAid)
            setOption7(data.lossOfConscious)
            setIncidentOwnerId(data.incidentOwnerId)
            setReportAuthority(data.reportAuthority)
            setStopWorkOrder(data.stopWorkOrder)
            setClassification(data.classification)

            setWorkRelatedDetails(data.workRelatedDetails ?? '')
            setPropertyDetails(data.propertyDamageDetails ?? '')
            setBodyValues(data.informationStep?.body)



            setIsWorkRelated(data.isWorkRelated)


            if (data.propertyDamage === 'Yes') {
                setIsPropertyDamage(true)
            }

            if (data.authorityName)
                setAuthorityName(data.authorityName)

            if (data.riskControl) {
                setRiskControl(data.riskControl);
            }

            if (data.isInjury) {
                setInjury(data.isInjury);
            }

            if (data.isFirstAid) {
                setFirstAid(data.isFirstAid);
            }

            if (data.isPersonInjured) {
                setPersonInjured(data.isPersonInjured);
            }

            if (data.isMedicalTreatment) {
                setMedicalTreatment(data.isMedicalTreatment);
            }

            if (data.isMedicalLeave) {
                setMedicalLeave(data.isMedicalLeave);
            }

            if (data.isControlMeasure) {
                setIsControlMeasure(data.isControlMeasure);
            }

            if (data.isRiskAssessment) {
                setIsRiskAssessment(data.isRiskAssessment);
            }


            if (data.informationStep) {
                setInformationData(data.informationStep);
            }

        }
    }

    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const result = await axios.post(ALL_USERS_BY_LOCATION, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId });
                setLocationUsers(result.data.map(user => ({
                    value: user.id,
                    label: user.firstName
                })));
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };
        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId)
            fetchUsers();

    }, [incidentData])



    const addImmediateAction = () => {
        setRiskControl(prevState => ({
            ...prevState,
            immediateActions: [...prevState.immediateActions, { date: new Date(), description: "" }],
        }));
    };

    const handleImmediateActionChange = (index, field, value) => {
        setRiskControl(prevState => {
            const updatedActions = [...prevState.immediateActions];
            updatedActions[index][field] = value;
            return { ...prevState, immediateActions: updatedActions };
        });
    };

    const addcontrolMeasures = () => {
        setRiskControl(prevState => ({
            ...prevState,
            controlMeasures: [...prevState.controlMeasures, {
                completionDate: new Date(),
                personResponsible: "",
                controlMeasures: ""
            }],
        }));
    };

    const handlecontrolMeasuresChange = (index, field, value) => {

        setRiskControl(prevState => {
            const updatedActions = [...prevState.controlMeasures];
            updatedActions[index][field] = value;
            return { ...prevState, controlMeasures: updatedActions };
        });
    };

    const addRiskAssessment = () => {
        setRiskControl(prevState => ({
            ...prevState,
            riskAssessment: [...prevState.riskAssessment, {
                name: "",
                completionDate: new Date(),
                personResponsible: ""
            }],
        }));
    };

    const handleRiskAssessmentChange = (index, field, value) => {
        setRiskControl(prevState => {
            const updatedActions = [...prevState.riskAssessment];
            updatedActions[index][field] = value;
            return { ...prevState, riskAssessment: updatedActions };
        });
    };


    const handleDeleteImmediateAction = (index) => {
        const newImmediateActions = [...riskControl.immediateActions];
        newImmediateActions.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, immediateActions: newImmediateActions }));
    };

    const handleDeleteControlMeasure = (index) => {
        const newControlMeasures = [...riskControl.controlMeasures];
        newControlMeasures.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, controlMeasures: newControlMeasures }));
    };

    const handleDeleteRiskAssessment = (index) => {
        const newRiskAssessments = [...riskControl.riskAssessment];
        newRiskAssessments.splice(index, 1);
        setRiskControl(prevState => ({ ...prevState, riskAssessment: newRiskAssessments }));
    };

    const [validationErrors, setValidationErrors] = useState({});

    const validateMandatoryFields = () => {
        let errors = {};

        if (reportAuthority === 'Yes') {
            if (!JSON.parse(authorityName).name) {
                errors.authorityName = true;
            }
            if (!JSON.parse(authorityName).reportedDate) {
                errors.reportedDate = true;
            }
        }

        // if (!incidentData.incidentCircumstanceCategoryId) {
        //     errors.incidentCircumstanceCategory = true;
        // }

        // if (!incidentData.incidentCircumstanceTypeId) {
        //     errors.incidentCircumstanceType = true;
        // }

        // if (!incidentData.incidentCircumstanceDescriptionId) {
        //     errors.incidentCircumstanceDescription = true;
        // }


        // if (!incidentData.surfaceTypeId) {
        //     errors.surfaceType = true;
        // }

        // if (!incidentData.surfaceConditionId) {
        //     errors.surfaceCondition = true;
        // }

        // if (!incidentData.lightingId) {
        //     errors.lighting = true;
        // }
        // if (!incidentData.weatherConditionId) {
        //     errors.weatherCondition = true;
        // }
        // if (!incidentData.workActivityId) {
        //     errors.workActivity = true;
        // }
        // if (!incidentData.riskCategoryId) {
        //     errors.riskCategory = true;
        // }
        // if (incidentFiles.length !== 0) {
        //     errors.files = true
        // }


        if (!classification) {
            errors.classification = true;
        }

        if (!InformationData.whenIncidentHappen) {
            errors.whenIncidentHappen = true;
        }
        if (!InformationData.personInvolved) {
            errors.personInvolved = true;
        }
        if (!InformationData.witness) {
            errors.witness = true;
        }
        if (!InformationData.howIncidentHappen) {
            errors.howIncidentHappen = true;
        }
        if (!InformationData.equipment) {
            errors.equipment = true;
        }
        if (!InformationData.activitiesPerformed) {
            errors.activitiesPerformed = true;
        }
        if (!InformationData.unsualActivity) {
            errors.unsualActivity = true;
        }
        if (!InformationData.safeProcedure) {
            errors.safeProcedure = true;
        }

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }

    const handleSubmit = async (type) => {
        let url = "";
        let toastText = "";
        let isValid = true;
        switch (type) {
            case 'save':

                url = SAVE_REPORT_INCIDENT_URL_WITH_ID(id);
                toastText = "Saved!"
                break;

            case 'return':

                url = RETURN_REPORT_INCIDENT_URL_WITH_ID(id);
                toastText = "Returned!"
                break;

            case 'modify':

                url = RESUBMIT_REPORT_INCIDENT_URL_WITH_ID(id);
                toastText = "Returned!"
                break;

            case 'submit':
                // if (!validateMandatoryFields()) {
                //     isValid = false;
                //     cogoToast.error('All mandatory fields must be filled out.')
                //     break;
                // }
                // if (isControlMeasure) {
                //     isValid = validateControlMeasures() && isValid;
                // }
                // if (isRiskAssessment) {
                //     isValid = validateRiskAssessment() && isValid;
                // }
                url = REPORT_INCIDENT_REVIEW_URL_WITH_ID(id);
                toastText = "Submitted!"
                break;
            default:
                isValid = false;
        }

        if (isValid) {
            const response = await API.patch(url, {
                actualImpact: status,
                incidentDate: incidentDate,
                dangerousOccurance: option1,
                fatality: option2,
                injury: option3,
                lostTime: option4,
                medicalTreatment: option5,
                firstAid: option6,
                lossOfConscious: option7,
                informationStep: { ...InformationData, dropdownSelections: dropdownSets },
                riskControl: riskControl,
                isInjury: injury,
                isWorkRelated: isWorkRelated,
                workRelatedDetails: workRelatedDetails ?? '',


                isFirstAid: firstAid,
                isPersonInjured: personInjured,
                isMedicalTreatment: medicalTreatment,
                propertyDamage: isPropertyDamage ? 'Yes' : 'No',
                propertyDamageDetails: propertyDetails,
                isMedicalLeave: medicalLeave,
                isControlMeasure: isControlMeasure,
                isRiskAssessment: isRiskAssessment,
                locationOneId: incidentData.locationOneId,
                locationTwoId: incidentData.locationTwoId,
                locationThreeId: incidentData.locationThreeId,
                locationFourId: incidentData.locationFourId,
                locationFiveId: incidentData.locationFiveId,
                locationSixId: incidentData.locationSixId,
                description: incidentData.description,
                IncidentCategory: incidentData.IncidentCategory,
                incidentCircumstanceCategoryId: incidentData.incidentCircumstanceCategoryId,
                incidentCircumstanceDescriptionId: incidentData.incidentCircumstanceDescriptionId,
                incidentCircumstanceTypeId: incidentData.incidentCircumstanceTypeId,
                surfaceTypeId: incidentData.surfaceTypeId,
                surfaceConditionId: incidentData.surfaceConditionId,
                workActivityId: incidentData.workActivityId,
                riskCategoryId: incidentData.riskCategoryId,
                title: incidentData.title,
                lightingId: incidentData.lightingId,
                weatherConditionId: incidentData.weatherConditionId,
                uploads: incidentFiles,
                incidentOwnerId: incidentOwnerId,
                reportAuthority: reportAuthority,
                stopWorkOrder: stopWorkOrder,
                classification: classification,
                authorityName: authorityName


            })

            if (response.status === 204) {

                setShowModal(false);
                setActiveStep(0);
                isEditable && setReadOnly(true);
                cogoToast.success(toastText)

            }
        }
    }

    const steps = [

        {
            label: 'Basic Information',
            description: (
                <>

                    <div>
                        <p>
                            Date &amp; Time of Incident:{' '}
                            {moment(incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A')}
                        </p>

                        <Calendar
                            value={moment(incidentDate, 'DD/MM/YYYY hh:mm A').toDate()}
                            onChange={handleDateChange}
                            showTime
                            hourFormat="12"
                            dateFormat="dd/mm/yy"
                            showIcon
                            className="mb-3"
                            disabled={readOnly}
                        />

                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Incident Title</label>
                                <input type='text' value={incidentData.title}

                                    onChange={(e) => { setIncidentData((prev) => ({ ...prev, title: e.target.value })) }} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    <div className='row'>

                        <div className='col-12'>

                            <div className='form-group'>
                                <label htmlFor="">Is there any property damage?</label>
                                <Switch
                                    disabled={readOnly}
                                    checked={isPropertyDamage}
                                    onChange={(checked) => handleSwitchChange('propertyDamage', checked)}
                                    className="ms-2"
                                />
                                {isPropertyDamage && <textarea type='text' value={propertyDetails}
                                    onChange={(e) => { setPropertyDetails(e.target.value) }} className='form-control' placeholder='Property Details' disabled={readOnly} />}
                            </div>
                        </div>


                    </div>

                    {incidentData.locationOne && (<>

                        <LocationDropdown setIncidentData={setIncidentData} incidentData={incidentData} readOnly={readOnly} />



                        <div className='row'>
                            <div className='col'>
                                <div className='form-group'>
                                    <label htmlFor="">Level and Zone</label>
                                    <textarea type='text'

                                        onChange={(e) => {
                                            setIncidentData((prev) => ({
                                                ...prev,
                                                incidentData: {
                                                    ...prev.incidentData,
                                                    location5: e.target.value
                                                }
                                            }));
                                        }} className='form-control' disabled={readOnly} >


                                        {`${incidentData.incidentData?.location5 || ''}${incidentData.incidentData?.location6 ? ' - ' + incidentData.incidentData.location6 : ''}`}


                                    </textarea>
                                </div>
                            </div>
                        </div>
                        <div className='row'>
                            <div className='col'>
                                <div className='form-group'>
                                    <label htmlFor="">Brief Description of the Incident</label>
                                    <textarea type='text' value={incidentData.description}

                                        onChange={(e) => { setIncidentData((prev) => ({ ...prev, description: e.target.value })) }} className='form-control' disabled={readOnly} ></textarea>
                                </div>
                            </div>
                        </div>

                        {<IncidentCircumstanceDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}

                        <div className='row'>
                            <div className='col-md-3'>
                                {<SurfaceTypeDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>
                            <div className='col-md-3'>
                                {<SurfaceConditionDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>
                            <div className='col-md-3'>
                                {<LightingDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>
                            <div className='col-md-3'>
                                {<WeatherConditionDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>
                        </div>

                        <div className='row'>
                            <div className='col-md-4'>
                                {<WorkActivityDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>

                            <div className='col-md-4'>
                                {<RiskCategoryDropdown incidentData={incidentData} setIncidentData={setIncidentData} readOnly={readOnly} />}
                            </div>
                        </div>




                        {incidentData.uploads && incidentData.uploads.length > 0 && (<div className='row'>
                            <p>
                                Image Uploads <span style={{ color: 'red' }}>*</span>
                            </p>
                            {!readOnly && <div className=""> <DropzoneArea
                                acceptedFiles={[

                                    'image/jpeg',
                                    'image/png'

                                ]}
                                dropzoneText={"Drag and Drop Evidence Images"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                showPreviews={false}
                                showPreviewsInDropzone={false}
                                onChange={handleIncidentFileChange}

                            />
                                <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                                    {incidentFiles.map((fileName, index) => (
                                        <div key={index} style={{ margin: '10px', position: 'relative' }}>
                                            <img src={STATIC_URL + '/' + fileName} alt={fileName} style={{ maxWidth: '100px', maxHeight: '100px' }} />
                                            <button className="btn btn-danger" onClick={() => deleteFileFromIncident(index)} style={{ position: 'absolute', top: '5px', right: '5px' }}><i className="mdi mdi-delete"></i></button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            }

                            {readOnly && <div className="table-responsive mb-3">
                                <table className="table table-striped">
                                    <tbody>
                                        <tr>

                                            <td><ImageView photos={incidentData.uploads} /></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>}


                        </div>)}




                    </>)}
                </>
            )
        },
        {
            label: 'Re-assessment',
            description: (
                <>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <p>Was this as a result of a Dangerous Occurrence?

                                    <InfoTooltip text="Here is some helpful information" /> </p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options7" id="option18" checked={option1 === "Yes"} onChange={(e) => setOption1('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options7" id="option19" checked={option1 === "No"} onChange={(e) => setOption1('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>


                            <div className='form-group'>
                                <div className="card">
                                    <p>Is the incident work related?    <i
                                        className="pi pi-info-circle text-primary"
                                        style={{ fontSize: '1.2rem', cursor: 'pointer' }}
                                        onClick={handleShow}
                                        title="View incident classification info"
                                    ></i>
                                    </p>



                                </div>
                                <Switch
                                    disabled={readOnly}
                                    checked={isWorkRelated}
                                    onChange={(checked) => handleSwitchChange('workRelated', checked)}
                                    className="ms-2"
                                />
                                {isWorkRelated && <textarea type='text' value={workRelatedDetails}
                                    onChange={(e) => { setWorkRelatedDetails(e.target.value) }} className='form-control' placeholder='Work Related Details' disabled={readOnly} />}

                            </div>

                        </div>
                    </div>
                    <div className='row full-width-btn-group'>
                        <h5>Did this incident result in</h5>
                        <div className='col-md-6'>

                            <div className='form-group'>
                                <p>A Fatality?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options1" id="option1" checked={option2 === "Yes"} onChange={(e) => setOption2('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options1" id="option2" checked={option2 === "No. But, it could have"} onChange={(e) => setOption2('No. But, it could have')} autocomplete="off" disabled={readOnly} /> No. But, it could have
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options1" id="option3" checked={option2 === "No"} onChange={(e) => setOption2('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className='col-md-6'>
                            <div className='form-group'>
                                <p>An Injury or Occupational illness resulting in permanent disability?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options2" id="option4" checked={option3 === "Yes"} onChange={(e) => setOption3('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options2" id="option5" checked={option3 === "No. But, it could have"} onChange={(e) => setOption3('No. But, it could have')} autocomplete="off" disabled={readOnly} /> No. But, it could have
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options2" id="option6" checked={option3 === "No"} onChange={(e) => setOption3('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className='col-md-3'>
                            <div className='form-group'>
                                <p>Lost Time Incident (LTI)?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options3" id="option7" checked={option4 === "Yes"} onChange={(e) => setOption4('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>

                                    <label className="btn btn-light">
                                        <input type="radio" name="options3" id="option9" checked={option4 === "No"} onChange={(e) => setOption4('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className='col-md-3'>
                            <div className='form-group'>
                                <p>Medical Treatment of illness / injury?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options4" id="option10" checked={option5 === "Yes"} onChange={(e) => setOption5('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>

                                    <label className="btn btn-light">
                                        <input type="radio" name="options4" id="option12" checked={option5 === "No"} onChange={(e) => setOption5('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className='col-md-3'>
                            <div className='form-group'>
                                <p>Need to administer First Aid?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options5" id="option13" checked={option6 === "Yes"} onChange={(e) => setOption6('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>

                                    <label className="btn btn-light">
                                        <input type="radio" name="options5" id="option15" checked={option6 === "No"} onChange={(e) => setOption6('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className='col-md-3'>
                            <div className='form-group'>
                                <p>loss of consciousness?</p>
                                <div className="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label className="btn btn-light">
                                        <input type="radio" name="options6" id="option16" checked={option7 === "Yes"} onChange={(e) => setOption7('Yes')} autocomplete="off" disabled={readOnly} /> Yes
                                    </label>
                                    <label className="btn btn-light">
                                        <input type="radio" name="options6" id="option17" checked={option7 === "No"} onChange={(e) => setOption7('No')} autocomplete="off" disabled={readOnly} /> No
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div role="alert" class="fade alert alert-danger show">{`Impact Classification - ${status}  ${remarks && `(${remarks})`}`}  </div>
                        <h4 className='text-center'></h4>

                        <div className="row">
                            <div className="col-md-3">
                                <div className="form-group">
                                    <label>Choose Incident Owner</label>
                                    <select disabled={readOnly} className="form-select" value={incidentOwnerId} onChange={(e) => { setIncidentOwnerId(e.target.value) }}>
                                        <option value={''}>Choose Incident Owner</option>
                                        {
                                            incidentOwners.map((user, index) => {
                                                return (
                                                    <option key={index} value={user.id}>{user.firstName}</option>
                                                );
                                            })
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div className="row">
                            <div>
                                <label className="d-flex align-items-center mb-3">
                                    Is the incident reportable to local authority?
                                    <Switch
                                        disabled={readOnly}
                                        checked={reportAuthority === 'Yes'}
                                        onChange={(checked) => handleSwitchChange('reportAuthority', checked)}
                                        className="ms-2"
                                    />
                                </label>
                                {reportAuthority === 'Yes' && <div className="mb-3">
                                    <div className="form-group">
                                        <label>Authoriy Name *</label>
                                        <br />
                                        <InputText
                                            disabled={readOnly}
                                            value={JSON.parse(authorityName).name}
                                            onChange={(e) => handleAuthorityChange(e.target.value, 'name')}
                                            placeholder="Authority Name"
                                        />
                                    </div>

                                    <div className="form-group">
                                        <label>Reported Date *</label>
                                        <br />
                                        <Calendar
                                            disabled={readOnly}
                                            value={
                                                moment(
                                                    JSON.parse(authorityName)?.reportedDate || new Date(),
                                                    "DD-MM-YYYY hh:mm A"
                                                ).isValid()
                                                    ? moment(
                                                        JSON.parse(authorityName)?.reportedDate || new Date(),
                                                        "DD-MM-YYYY hh:mm A"
                                                    ).toDate()
                                                    : new Date()
                                            }
                                            onChange={(e) =>
                                                handleAuthorityChange(
                                                    moment(e.value).format("DD-MM-YYYY hh:mm A"), // Format the date here
                                                    "reportedDate"
                                                )
                                            }
                                            dateFormat="dd M yy" // Set your desired date format here
                                            showTime
                                            hourFormat="12"
                                            touchUI={true}
                                            minDate={
                                                moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").isValid()
                                                    ? moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").toDate()
                                                    : null
                                            }
                                        />
                                    </div>
                                </div>}
                                <label className="d-flex align-items-center mb-3">
                                    Is the incident subjected to any authority notice or stop work order?
                                    <Switch
                                        disabled={readOnly}
                                        checked={stopWorkOrder === 'Yes'}
                                        onChange={(checked) => handleSwitchChange('stopWorkOrder', checked)}
                                        className="ms-2"
                                    />
                                </label>
                                <label>
                                    Legal Classification? *
                                    <select disabled={readOnly} className="form-select" value={classification} onChange={(e) => setClassification(e.target.value)}>
                                        <option value="Normal">Normal</option>
                                        <option value="Client Privilege">Client Privilege</option>
                                    </select>
                                </label>

                            </div>
                        </div>
                    </div>

                </>
            ),
        },
        {
            label: 'Information gathering',
            description:
                (<>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Where and when did the incident happen? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, whenIncidentHappen: e.target.value }))} value={InformationData.whenIncidentHappen} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>
                    {!readOnly && <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <DropzoneArea
                                    acceptedFiles={[
                                        'application/pdf',
                                        'image/jpeg',
                                        'image/png'

                                    ]}
                                    dropzoneText={"Drag and drop files that includes location plan/sketch/pictures"}
                                    filesLimit={5}

                                    onChange={handleFileChange}
                                />
                                <div >
                                    {files.map((fileName, index) => (
                                        <div key={index} style={{ margin: '10px', position: 'relative', display: 'flex', alignItems: 'center' }}>
                                            {/* Hyperlink to the file */}
                                            <a href={`${STATIC_URL}/${fileName}`} target="_blank" rel="noopener noreferrer" style={{ marginRight: '10px' }}>
                                                {fileName}
                                            </a>
                                            {/* Delete button */}

                                            <i onClick={() => deleteFile(index)} className="cursor-pointer text-red mdi mdi-delete"></i>

                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>}

                    {
                        readOnly && (<>
                            <p>Uploaded Documents</p>
                            <div >

                                {files.map((fileName, index) => (
                                    <div key={index} style={{ margin: '10px', position: 'relative', display: 'flex', alignItems: 'center' }}>
                                        {/* Hyperlink to the file */}
                                        <a href={`${STATIC_URL}/${fileName}`} target="_blank" rel="noopener noreferrer" style={{ marginRight: '10px' }}>
                                            {fileName}
                                        </a>
                                        {/* Delete button */}

                                    </div>
                                ))}
                            </div>
                        </>
                        )
                    }


                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Who was injured/suffered ill health or was otherwise involved in the incident? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, personInvolved: e.target.value }))} value={InformationData.personInvolved} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Who witnessed the incident? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, witness: e.target.value }))} value={InformationData.witness} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">How did the incident happen? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, howIncidentHappen: e.target.value }))} value={InformationData.howIncidentHappen} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">What equipment was involved? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, equipment: e.target.value }))} value={InformationData.equipment} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">What activities were being performed at the time of the incident? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, activitiesPerformed: e.target.value }))} value={InformationData.activitiesPerformed} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Was there anything unusual or different about the working conditions? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, unsualActivity: e.target.value }))} value={InformationData.unsualActivity} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Were there adequate safe working procedures and were they followed? *</label>
                                <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, safeProcedure: e.target.value }))} value={InformationData.safeProcedure} className='form-control' disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group d-flex align-items-center'>
                                <label htmlFor="" className='m-0 me-3'>Were injuries sustained? *</label>

                                <Switch onChange={(value) => setInjury(value)} checked={injury} disabled={readOnly} />
                            </div>
                        </div>
                    </div>

                    {
                        injury && <>
                            <div className='row'>
                                {!readOnly && <> <h4>Parts of Body Injured</h4>
                                    <p>Please indicate the location of all injuries and describe the types of injuries.</p>
                                    <div className='col'>
                                        <BodyComponent
                                            partsInput={InformationData.body}
                                            onChange={(value) => { console.log(value, 'value'); setBodyValues(value); setInformationData(prev => ({ ...prev, value })) }}
                                        />
                                    </div></>}
                                <div className='col mb-3'>
                                    <DynamicDropdowns readOnly={readOnly} dropdownSets={dropdownSets}
                                        onAddDropdownSet={addDropdownSet}
                                        onDeleteDropdownSet={deleteDropdownSet} bodyParts={bodyValues} onUpdateSelectedValues={updateSelectedValues} />
                                </div>
                            </div>
                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Was first aid administered on site?</label>

                                        <Switch onChange={(value) => setFirstAid(value)} checked={firstAid} disabled={readOnly} />
                                    </div>
                                </div>
                            </div>
                            {
                                firstAid && <div className='row'>
                                    <div className='col'>

                                        <div className='form-group'>
                                            <label htmlFor="">Administered By</label>
                                            <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, administeredBy: e.target.value }))} value={InformationData.administeredBy} className='form-control' disabled={readOnly} />
                                        </div>

                                    </div>
                                </div>
                            }

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Was Injured Person taken to an offsite
                                            medical facility?</label>

                                        <Switch onChange={(value) => setPersonInjured(value)} checked={personInjured} disabled={readOnly} />
                                    </div>
                                </div>
                            </div>
                            {
                                personInjured && <div className='row'>
                                    <div className='col'>

                                        <div className='form-group'>
                                            <label htmlFor="">Medical Facility Name</label>
                                            <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, medicalFaciltiyName: e.target.value }))} value={InformationData.medicalFaciltiyName} className='form-control' disabled={readOnly} />
                                        </div>

                                    </div>
                                </div>
                            }

                            {/* checkbox mode of transport */}

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="">Mode of Transport:</label><br />
                                        <input type='checkbox' className='ms-2' checked={InformationData.ambulance} onChange={(e) => setInformationData(prev => ({ ...prev, ambulance: e.target.checked }))} disabled={readOnly} /> Ambulance
                                        <input type='checkbox' className='ms-2' checked={InformationData.companyVehicle} onChange={(e) => setInformationData(prev => ({ ...prev, companyVehicle: e.target.checked }))} disabled={readOnly} /> Company Vehicle
                                        <input type='checkbox' checked={InformationData.privateVehicle} onChange={(e) => setInformationData(prev => ({ ...prev, privateVehicle: e.target.checked }))} className='ms-2' disabled={readOnly} /> Private Vehicle
                                        <input type='text' value={InformationData.driverName} onChange={(e) => setInformationData(prev => ({ ...prev, driverName: e.target.value }))} className='form-control ms-2' placeholder='Name/Role of Driver' disabled={readOnly} />
                                    </div>
                                </div>
                            </div>

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Was medical treatment received?</label>

                                        <Switch onChange={(value) => setMedicalTreatment(value)} checked={medicalTreatment} disabled={readOnly} />
                                    </div>
                                </div>
                            </div>
                            {
                                medicalTreatment && <div className='row'>
                                    <div className='col'>

                                        <div className='form-group'>
                                            <label htmlFor="">Brief Description of the treatment</label>
                                            <input type='text' onChange={(e) => setInformationData(prev => ({ ...prev, treatmentDescription: e.target.value }))} value={InformationData.treatmentDescription} className='form-control' disabled={readOnly} />
                                        </div>

                                    </div>
                                </div>
                            }

                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group d-flex align-items-center'>
                                        <label htmlFor="" className='m-0 me-3'>Is medical leave given?</label>

                                        <Switch onChange={(value) => setMedicalLeave(value)} checked={medicalLeave} disabled={readOnly} />
                                    </div>
                                </div>
                            </div>
                            {
                                medicalLeave && <div className='row'>
                                    <div className='col-3'>

                                        <div className='form-group'>
                                            <label htmlFor="">Days</label>
                                            <input type='number' onWheel={handleWheel} onChange={(e) => setInformationData(prev => ({ ...prev, medicalLeaveDays: e.target.value }))} value={InformationData.medicalLeaveDays} min="1" className='form-control' disabled={readOnly} />
                                        </div>

                                    </div>
                                </div>
                            }
                            <div className='row'>
                                <div className='col'>
                                    <div className='form-group'>
                                        <label htmlFor="">Injured Person's Return-to-Work info:</label> <br />
                                        <input type='checkbox' checked={InformationData.regularWork} onChange={(e) => setInformationData(prev => ({ ...prev, regularWork: e.target.checked }))} className='ms-2' disabled={readOnly} /> Regular Work <br />
                                        <input type='date' min={moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").format("YYYY-MM-DD")} value={InformationData.regularWorkDate} onChange={(e) => setInformationData(prev => ({ ...prev, regularWorkDate: e.target.value }))} className='form-control ms-2' disabled={readOnly} /><br /><br />
                                        <input type='checkbox' checked={InformationData.modifiedWork} onChange={(e) => setInformationData(prev => ({ ...prev, modifiedWork: e.target.checked }))} className='ms-2' disabled={readOnly} /> Modified Work<br />

                                        <input type='date' min={moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").format("YYYY-MM-DD")} value={InformationData.modifiedWorkDate} onChange={(e) => setInformationData(prev => ({ ...prev, modifiedWorkDate: e.target.value }))} className='form-control ms-2' disabled={readOnly} />
                                    </div>
                                </div>
                            </div>
                        </>
                    }
                </>)
        },
        {
            label: 'Risk Control Action Plan',
            description: (<>
                <p className="h5 mb-4">Immediate Actions Taken (Post Damage / Incident)? *</p>
                {riskControl.immediateActions && riskControl.immediateActions.map((action, index) => (<>


                    <div className="form-group d-flex align-items-center" key={index}>
                        <label className="me-2">
                            Date

                            {
                                readOnly ?
                                    <input
                                        className="form-control"
                                        type="text"
                                        value={moment(action.date, 'YYYY-MM-DD').format('Do MMM YYYY')}

                                        disabled={readOnly}

                                    />
                                    :
                                    <input
                                        className="form-control"
                                        type="date"
                                        value={action.date}
                                        onChange={(e) =>
                                            handleImmediateActionChange(index, "date", e.target.value)
                                        }
                                        disabled={readOnly}
                                        min={moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").format("YYYY-MM-DD")}
                                        max={new Date().toISOString().split("T")[0]}
                                    />
                            }

                        </label>
                        <br />
                        <label className="me-2 w-50">
                            Description
                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleImmediateActionChange(index, "description", e.target.value)
                                }
                                disabled={readOnly}
                            />
                        </label>
                        <br />
                        {!readOnly && <button
                            type="button"
                            className="btn btn-danger margin-bottom-5"
                            onClick={() => handleDeleteImmediateAction(index)}
                        >
                            <i className='mdi mdi-delete'></i>
                        </button>}
                    </div>

                </>
                ))}
                {!readOnly && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addImmediateAction}>
                    <i className="mdi mdi-plus-circle"></i>   Add More
                </button>}


                <div className='row'>
                    <div className='col'>
                        <div className='form-group d-flex align-items-center'>
                            <label htmlFor="" className='m-0 me-3'>Is control measures required?</label>

                            <Switch onChange={(value) => setIsControlMeasure(value)} checked={isControlMeasure} disabled={readOnly} />
                        </div>
                    </div>
                </div>
                {
                    isControlMeasure && <>
                        <p className="h5 mb-4">Which control measures should be implemented in the short and long term?</p>
                        {riskControl.controlMeasures.map((action, index) => (<>
                            <div className="form-group d-flex align-items-center" key={index}>
                                <label className="me-2 w-50">
                                    Corrective/Control measures:
                                    <input
                                        className="form-control"
                                        type="text"
                                        value={action.controlMeasures}
                                        onChange={(e) =>
                                            handlecontrolMeasuresChange(index, "controlMeasures", e.target.value)
                                        }
                                        disabled={readOnly}
                                    />
                                </label>
                                <br />
                                <label className="me-2">
                                    Due Date
                                    {readOnly ?
                                        <input
                                            className="form-control"
                                            type="text"
                                            value={moment(action.completionDate, 'YYYY-MM-DD').format('Do MMM YYYY')}

                                            disabled={readOnly}
                                        /> :
                                        <input
                                            className="form-control"
                                            type="date"
                                            value={action.completionDate}
                                            onChange={(e) =>
                                                handlecontrolMeasuresChange(index, "completionDate", e.target.value)
                                            }
                                            min={moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").format("YYYY-MM-DD")}
                                            disabled={readOnly}
                                        />
                                    }
                                </label>
                                <br />
                                <label className="w-25 me-2">
                                    Person Responsible
                                    <Select

                                        value={locationUsers.find(option => option.value === action.personResponsible)}
                                        isDisabled={readOnly}
                                        options={locationUsers.sort((a, b) => a.label.localeCompare(b.label))} // Sort alphabetically
                                        onChange={(selectedOption) =>
                                            handlecontrolMeasuresChange(index, "personResponsible", selectedOption.value)
                                        }

                                        placeholder="Choose" // Optional placeholder text
                                        isClearable={false} // Optional: Disable clearing selection
                                    />


                                </label>
                                {!readOnly && <button
                                    type="button"
                                    className="btn btn-danger margin-bottom-5"
                                    onClick={() => handleDeleteControlMeasure(index)}
                                >
                                    <i className='mdi mdi-delete'></i>
                                </button>}
                                <br />
                            </div>

                        </>
                        ))}
                    </>
                }
                <br />


                {!readOnly && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addcontrolMeasures}>
                    <i className="mdi mdi-plus-circle"></i>   Add More
                </button>}

                <div className='row'>
                    <div className='col'>
                        <div className='form-group d-flex align-items-center'>
                            <label htmlFor="" className='m-0 me-3'>Is risk assessments and safe working procedures need to be reviewed and updated?</label>

                            <Switch onChange={(value) => setIsRiskAssessment(value)} checked={isRiskAssessment} disabled={readOnly} />
                        </div>
                    </div>
                </div>

                {isRiskAssessment && <>
                    <p className="h5 mb-4">Which risk assessments and safe working procedures need to be reviewed and updated?</p>
                    {riskControl.riskAssessment &&
                        riskControl.riskAssessment.map((action, index) => (<>
                            <div className="form-group d-flex align-items-center" key={index}>
                                <label className="me-2 w-50">
                                    Name of risk assessment / safe working procedure: reporting and
                                    refined in investigation if needed
                                    <input
                                        className="form-control"
                                        type="text"
                                        value={action.name}
                                        onChange={(e) =>
                                            handleRiskAssessmentChange(index, "name", e.target.value)
                                        }
                                        disabled={readOnly}
                                    />
                                </label>
                                <br />
                                <label className="me-2">
                                    Due Date
                                    {readOnly ?
                                        <input
                                            className="form-control"
                                            type="text"
                                            value={moment(action.completionDate, 'YYYY-MM-DD').format('Do MMM YYYY')}

                                            disabled={readOnly}
                                        /> :
                                        <input
                                            className="form-control"
                                            type="date"
                                            value={action.completionDate}
                                            onChange={(e) =>
                                                handleRiskAssessmentChange(index, "completionDate", e.target.value)
                                            }
                                            disabled={readOnly}
                                            min={moment(incidentData.incidentDate, "DD/MM/YYYY hh:mm A").format("YYYY-MM-DD")}
                                        />}
                                </label>
                                <br />
                                <label className="w-25 me-2">
                                    Person Responsible
                                    <Select
                                        className=""
                                        value={locationUsers.find(option => option.value === action.personResponsible)}
                                        isDisabled={readOnly}
                                        options={locationUsers.sort((a, b) => a.label.localeCompare(b.label))} // Sort alphabetically
                                        onChange={(selectedOption) =>
                                            handleRiskAssessmentChange(index, "personResponsible", selectedOption.value)
                                        }

                                        placeholder="Choose" // Optional placeholder text
                                        isClearable={false} // Optional: Disable clearing selection
                                    />


                                </label>
                                {!readOnly && <button
                                    type="button"
                                    className="btn btn-danger margin-bottom-5"
                                    onClick={() => handleDeleteRiskAssessment(index)}
                                >
                                    <i className="mdi mdi-delete" />
                                </button>}
                                <br />

                            </div>

                        </>
                        ))
                    }
                </>}
                <br />
                {!readOnly && <button variant="light" className='btn btn-light mb-4' type="button" onClick={addRiskAssessment}>
                    <i className="mdi mdi-plus-circle"></i>  Add More
                </button>}



            </>),
        },
    ];

    if (type === 'Trigger') {
        const triggerInvesigationForm = {
            label: 'Trigger Investigation',
            description: (<>

                <p className="h5 mb-4">Does this require investigation?</p>
                <Switch onChange={(value) => setRequireInvestigation(value)} checked={requireInvestigation} />

                {
                    requireInvestigation && (
                        <div className='w-50'>
                            <div className='form-group w-50'>
                                <label htmlFor="">Choose Lead Investigator</label>
                                <select value={investigatorId} onChange={(e) => setInvestigatorId(e.target.value)} className='form-control'>
                                    <option value={''}>Choose Lead Investigator</option>
                                    {
                                        iusers.map(u => {
                                            return (
                                                <option value={u.id}>{u.firstName}</option>
                                            )
                                        })
                                    }
                                </select>
                            </div>

                            <div className='form-group'>
                                <label htmlFor="">Remarks</label>
                                <textarea value={investigationRemarks} onChange={(e) => setInvestigationRemarks(e.target.value)} className='form-control'></textarea>
                            </div>
                        </div>
                    )
                }
            </>)
        }
        steps.push(triggerInvesigationForm)
    }

    const handleTriggerInvestigationSubmit = async () => {
        const response = await API.patch(REPORT_INCIDENT_INVESTIGATE_URL_WITH_ID(id), {
            investigationStatus: requireInvestigation,
            investigationRemarks: investigationRemarks,
            investigatorId: investigatorId
        })

        if (response.status === 204) {
            setShowModal(false);
            cogoToast.success('Investigation Triggered!')
        }
    }

    const uploadFiles = async (files) => {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('file', file);
        });
        const token = localStorage.getItem('access_token');
        try {
            const response = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });
            if (response.status === 200) {
                return response.data.files.map(file => file.originalname);  // Return file names
            } else {
                throw new Error('Failed to upload files');
            }
        } catch (error) {
            console.error('Error uploading files:', error);
            throw error;
        }
    };

    return (
        <>
            {incidentData && <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>

                        Information Gathering - {incidentData.maskId}


                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box sx={{ width: '100%' }}>
                        <Stepper activeStep={activeStep} orientation="horizontal">
                            {steps.map((step, index) => (
                                <Step key={step.label}>
                                    <StepLabel
                                        onClick={() => setActiveStep(index)}
                                        style={{ cursor: 'pointer' }}>
                                        {step.label}
                                    </StepLabel>
                                </Step>
                            ))}
                        </Stepper>
                        <div className="mt-3">
                            {steps.map((step, index) => (
                                <div key={step.label} hidden={index !== activeStep}>
                                    <Typography>{step.description}</Typography>
                                    <Box sx={{ mb: 2 }}>
                                        <Button
                                            disabled={index === 0}
                                            onClick={handleBack}
                                            sx={{ mt: 1, mr: 1 }}
                                        >
                                            Back
                                        </Button>
                                        {index === steps.length - 1 ? (
                                            <></>
                                        ) : (
                                            <Button
                                                variant="contained"
                                                onClick={handleNext}
                                                sx={{ mt: 1, mr: 1 }}
                                            >
                                                Next
                                            </Button>
                                        )}
                                    </Box>
                                </div>
                            ))}
                        </div>
                        {activeStep === steps.length && (
                            <Paper square elevation={0} sx={{ p: 3 }}>
                                <Typography>Submitted</Typography>
                            </Paper>
                        )}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    {type === 'Trigger' && <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => handleTriggerInvestigationSubmit()}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Submit
                    </Button>}
                    {type === 'Review' && <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('submit')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Submit
                    </Button>}



                    {(type === 'Modify' && !readOnly) && <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('save')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Update
                    </Button>}

                    {type === 'Review' && <Button
                        variant="success"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('save')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Update
                    </Button>}



                    {!readOnly && <Button
                        variant="light"
                        onClick={() => {
                            secondaryPopup.fire({
                                title: 'Warning!',
                                text: 'All the newly added information will not be saved if you click close. Are you sure?',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: 'Yes, close it!',
                                cancelButtonText: 'No, keep it'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    setShowModal(false);

                                    isEditable && setReadOnly(true);
                                    setActiveStep(0);
                                }
                            })
                        }}
                    >
                        Close
                    </Button>}

                    {readOnly && (
                        <>
                            <Button
                                variant="light"
                                onClick={() => {
                                    setShowModal(false);
                                    setReadOnly(true);
                                    setActiveStep(0);

                                }}
                            >
                                Close
                            </Button>

                            {(isEditable && type !== 'ReadOnly') && (
                                <Button
                                    variant="primary"
                                    onClick={() => {
                                        // Enable editing logic here
                                        // For example: setReadOnly(false);
                                        setReadOnly(false);
                                        setType('Modify');
                                    }}
                                    className="ms-2"
                                >
                                    Edit
                                </Button>
                            )}
                        </>
                    )}




                </Modal.Footer>
            </Modal>}

            <Modal show={show} onHide={handleClose} size="xl" backdropClassName="custom-black-backdrop">
                <Modal.Header closeButton>
                    <Modal.Title>Incident Classification Details</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="row">
                        <div className="col-md-6">
                            <h5>Work-Related Incidents</h5>
                            <ul className="list-group">
                                {workRelated.map(([no, scenario]) => (
                                    <li key={no} className="list-group-item">{scenario}</li>
                                ))}
                            </ul>
                        </div>
                        <div className="col-md-6">
                            <h5>Non-Work-Related Incidents</h5>
                            <ul className="list-group">
                                {nonWorkRelated.map(([no, scenario]) => (
                                    <li key={no} className="list-group-item">{scenario}</li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </Modal.Body>
            </Modal>
        </>
    )
}

IncidentInformationModal.defaultProps = {
    readOnly: false
}

export default IncidentInformationModal;