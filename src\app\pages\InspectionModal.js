import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, GET_USERS_BY_ROLE, INSPECTIONS_ACTIONS_SUBMIT_WITH_ID, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import FormBuilder from "../apps/FormBuilder";
import FormRender from "../apps/FormRender";
import FormRenderer from "../apps/FormRenderer";
import Select from 'react-select'

const InspectionModal = ({ data, applicationType, showModal, setShowModal }) => {

    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])

    const comments = useRef();

    const actionTaken = useRef();

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {
            if (applicationType === 'Inspection') {
                getObsUsers();


            }
        }

    }, [])

    const getObsUsers = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId, mode: 'inspection-action-plan-implementor' });
        if (response.status === 200) {

            setUsers(response.data.map(user => ({
                value: user.id,
                label: user.firstName
            })));

        }
    }

    const [postActionForm, setPostActionForm] = useState(false)
    const [postActions, setPostActions] = useState([
        {
            dueDate: new Date(),
            actionOwner: "",
            actionToBeTaken: ""
        }
    ])

    const addcontrolMeasures = () => {
        setPostActions(prevState => ([
            ...prevState,
            {
                dueDate: new Date(),
                actionOwner: "",
                actionToBeTaken: ""
            },
        ]));
    };

    const handlecontrolMeasuresChange = (index, field, value) => {
        setPostActions(prevState => {
            const updatedActions = [...prevState];
            updatedActions[index][field] = value;
            return updatedActions; // Directly return the updated array
        });
    };


    const handleDeleteControlMeasure = (index) => {
        setPostActions(prevState => {
            const newControlMeasures = [...prevState];
            newControlMeasures.splice(index, 1);
            return newControlMeasures; // Correctly update the state by returning the new array
        });
    };

    const [checklistReport, setChecklistReport] = useState(null)
    const handleFormSubmit = async (formValues, id) => {

        setPostActionForm(true)

        setChecklistReport(formValues)
        // cogoToast.success('Submitted!')


    }

    const handleSubmit = async () => {
        const response = await API.patch(INSPECTIONS_ACTIONS_SUBMIT_WITH_ID(data.applicationDetails.id, data.id), { checklistReport: checklistReport, postActions: postActions })

        if (response.status === 204) {
            cogoToast.success('Submitted!')
            setShowModal(false)
        }
    }


    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>

                        <p className="card-text">{data.applicationDetails?.checklist?.name}</p>

                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>
                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                    <h5 className="card-title"># ({data.applicationDetails?.maskId})</h5>
                                    {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}



                                    <div className="mb-3">
                                        <label className="form-label">Description</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.applicationDetails.checklist.description}</textarea>
                                    </div>

                                    {/* {!postActionForm && <FormRenderer handleFormSubmit={handleFormSubmit} setShowModal={setShowModal} formData={data.applicationDetails.checklist.value ? data.applicationDetails.checklist.value : ""} id={data.applicationDetails.id} />} */}

                                    {postActionForm &&

                                        (<>

                                            <p>Assign Actions</p>

                                            {postActions.map((action, index) => (
                                                <div className="form-group" key={index}>
                                                    <label>
                                                        Corrective/Control measures:
                                                        <input
                                                            className="form-control"
                                                            type="text"
                                                            value={action.actionToBeTaken}
                                                            onChange={(e) =>
                                                                handlecontrolMeasuresChange(index, "actionToBeTaken", e.target.value)
                                                            }
                                                        />
                                                    </label>

                                                    <label>
                                                        Due Date
                                                        <input
                                                            className="form-control"
                                                            type="date"
                                                            value={action.dueDate}
                                                            onChange={(e) =>
                                                                handlecontrolMeasuresChange(index, "dueDate", e.target.value)
                                                            }
                                                        />
                                                    </label>

                                                    <label>
                                                        Person Responsible
                                                        <Select

                                                            value={users.find(option => option.value === action.actionOwner)}

                                                            options={users.sort((a, b) => a.label.localeCompare(b.label))} // Sort alphabetically
                                                            onChange={(selectedOption) =>
                                                                handlecontrolMeasuresChange(index, "actionOwner", selectedOption.value)
                                                            }

                                                            placeholder="Choose" // Optional placeholder text
                                                            isClearable={false} // Optional: Disable clearing selection
                                                        />
                                                    </label>
                                                    <button
                                                        type="button"
                                                        className="btn btn-danger"
                                                        onClick={() => handleDeleteControlMeasure(index)}
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            ))}
                                            <button variant="light" className='btn btn-light mb-4' type="button" onClick={addcontrolMeasures}>
                                                Add More
                                            </button>


                                            <button variant="primary" className='btn btn-light mb-4' type="button" onClick={handleSubmit}>
                                                Submit
                                            </button>
                                        </>)


                                    }

                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>


            </Modal>
        </>
    )
}

export default InspectionModal;