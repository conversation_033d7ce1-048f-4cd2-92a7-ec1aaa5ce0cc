import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";

const GhsGroupedBarChart = ({ info }) => {
    const [ghsData, setGhsData] = useState({
        totalTopGhs: 5,
        topGhsNames: [
            "Others",
            "GMS 3.1 Establishing Projects & Operations",
            "GMS 4.2 Fall Prevention – Persons",
            "GMS 4.6 Fire and Explosion",
            "GMS 4.5 Management of Hazardous Energy",
        ],
        typeConditionCounts: {
            Others: {
                "Safe (Condition)": 49,
                "At Risk (Condition)": 24,
                "At Risk (Act)": 13,
                "Safe (Act)": 5,
            },
            "GMS 4.2 Fall Prevention – Persons": {
                "At Risk (Act)": 24,
                "Safe (Condition)": 2,
                "At Risk (Condition)": 59,
                "Safe (Act)": 5,
            },
            "GMS 4.5 Management of Hazardous Energy": {
                "Safe (Condition)": 2,
                "At Risk (Act)": 7,
                "At Risk (Condition)": 46,
                "Safe (Act)": 1,
            },
            "GMS 3.1 Establishing Projects & Operations": {
                "At Risk (Act)": 18,
                "At Risk (Condition)": 90,
                "Safe (Condition)": 10,
                "Safe (Act)": 47,
            },
            "GMS 4.6 Fire and Explosion": {
                "At Risk (Condition)": 30,
                "At Risk (Act)": 5,
                "Safe (Condition)": 1,
            },
        },
    });

    useEffect(() => {
        if (info) setGhsData(info);
    }, [info]);

    // Calculate total sums and sort categories
    const sortedGhsNames = ghsData.topGhsNames
        .map((name) => {
            const counts = ghsData.typeConditionCounts[name] || {};
            const total = Object.values(counts).reduce((sum, value) => sum + value, 0);
            return { name, total };
        })
        .sort((a, b) => b.total - a.total) // Sort in descending order of total
        .map((item) => item.name);

    // Function to extract values for each condition type
    const getTypeCounts = (type) =>
        sortedGhsNames.map((ghs) => ghsData.typeConditionCounts[ghs]?.[type] || 0);

    // Highcharts Configuration for Grouped Bar Chart
    const options = {
        chart: {
            type: "column",
            zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: sortedGhsNames,
            title: {
                text: "GHS Categories",
            },
            crosshair: true,
        },
        yAxis: {
            min: 0,
            title: {
                text: "Counts",
            },
        },
        tooltip: {
            shared: true,
            pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
        },
        plotOptions: {
            column: {
                grouping: true, // 🔹 Groups bars instead of stacking
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: true,
        },
        series: [
            {
                name: "Safe (Condition)",
                data: getTypeCounts("Safe (Condition)"),
            },
            {
                name: "Safe (Act)",
                data: getTypeCounts("Safe (Act)"),
            },
            {
                name: "At Risk (Condition)",
                data: getTypeCounts("At Risk (Condition)"),
            },
            {
                name: "At Risk (Act)",
                data: getTypeCounts("At Risk (Act)"),
            },
        ],
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default GhsGroupedBarChart;
