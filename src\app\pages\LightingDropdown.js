import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    LIGHTING_URL,
    SURFACE_CONDITIONS_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const LightingDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.lighting ? incidentData.lighting.id ?? '' : '');

    useEffect(() => {
        setSelected(incidentData.lighting ? incidentData.lighting.id ?? '' : '')
    }, [incidentData.lighting])
    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(LIGHTING_URL)
            .then(response => {
                setData(response.data);
            })
            .catch(error => {
                console.error('Error fetching risk categories', error);
            });
    }, []);


    return (
        <div className=''>


            <div className='form-group'>
                <label className=''> Lighting <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, lightingId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.name}</option>
                    ))}
                </select>
            </div>



        </div>
    );
};

LightingDropdown.defaultProps = {
    readOnly: false
}
export default LightingDropdown;
