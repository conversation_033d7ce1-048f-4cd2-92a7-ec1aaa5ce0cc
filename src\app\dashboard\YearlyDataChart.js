import React, { useState, useEffect, useRef } from 'react';
import Select from 'react-select';
import HighchartsWrapper from './HighchartsWrapper';
import html2canvas from 'html2canvas';
import { Button } from '@material-ui/core';
import GetAppIcon from '@material-ui/icons/GetApp'; // Material UI Download Icon
import moment from 'moment';
const YearlyDataChart = ({
  initialYear,
  data,
  preprocessData,
  calculateTRIR,
  chartId,
  manHoursKey,
  chartConfig,
}) => {

  const headerRef = useRef(null);

  const [selectedYear, setSelectedYear] = useState(initialYear);
  const [selectedYearData, setSelectedYearData] = useState([]);
  const currentMonth = moment().format("MMM"); // Get current month abbreviation

  const yearOptions = [
    { value: 2022, label: '2022' },
    { value: 2023, label: '2023' },
    { value: 2024, label: '2024' },
    { value: 2025, label: '2025' },
  ];

  useEffect(() => {

    const yearData = data.filter((item) => {
      // console.log(
      //   `item.year: ${item.year}, selectedYear: ${selectedYear}, country: ${item.country}, bu: ${item.bu}, site: ${item.site}`
      // );
      return item.year === selectedYear;
    });
    setSelectedYearData(yearData);
  }, [selectedYear, data]);

  const handleYearChange = (selectedOption) => {
    setSelectedYear(selectedOption.value);
  };


  // Preprocess and calculate TRIR data
  let processedData = calculateTRIR(preprocessData(selectedYearData), manHoursKey);

  // Step 1: Extract existing months from processedData
  const existingMonths = new Set(processedData.map(item => item.month));

  // Step 2: Generate all months from January to current month
  const allMonths = [];
  for (let i = 0; i < moment().month() + 1; i++) {
    allMonths.push(moment().month(i).format("MMM"));
  }

  // Step 3: Fill missing months with zero values
  allMonths.forEach(month => {
    if (!existingMonths.has(month)) {
      processedData.push({
        year: selectedYear,
        month: month,
        monthlyTRIR: 0,
        cumulativeTRIR: 0,
        monthlyFatalityRate: 0,
        cumulativeFatalityRate: 0,
        monthlyLTIFR: 0,
        cumulativeLTIFR: 0,
        monthlyHCIR: 0,
        cumulativeHCIR: 0,
        totalMonthlyHCIR: 0,
        noOfRecordableIncidentCases: 0,
        noOfFatality: 0,
        noOfDaysAwayFromWorkCasesLTICases: 0,
      });
    }
  });

  // Step 4: Sort processedData by month order (Jan - current month)
  processedData.sort((a, b) => moment(a.month, "MMM").month() - moment(b.month, "MMM").month());


  // Extract months and chart data
  const categories = processedData.map((item) => {
    const shortYear = item.year.toString().slice(-2); // Extract last two digits
    return `${item.month} '${shortYear}`;
  });

  const barData = processedData.map((item) => item[chartConfig.barKey]); // Bar chart values
  const lineData = processedData.map((item) => item[chartConfig.lineKey]); // Line chart values

  // Highcharts configuration for Bar & Line Chart
  const options = {
    chart: {
      zoomType: 'xy',
    },
    title: {
      text: ``,
    },
    xAxis: {
      categories: categories,
      crosshair: true, labels: {
        style: {
          fontSize: '15px', // Adjust this value as needed

        }
      }
    },
    yAxis: [
      {
        title: {
          text: chartConfig.barLegend, style: {
            fontSize: '15px', // Adjust font size for Y-Axis title

          }
        },
        opposite: false,
        allowDecimals: false, // Ensures only whole numbers are displayed
        tickInterval: 1,      // Forces tick marks at intervals of 1
        min: 0,
        labels: {
          style: {
            fontSize: '15px', // Adjust this value as needed

          }
        }              // Ensures the axis starts from 0
      },
      {
        title: {
          text: chartConfig.lineLegend, style: {
            fontSize: '15px', // Adjust font size for Y-Axis title

          }
        },
        opposite: true,
      },
    ],
    tooltip: {
      shared: true,
    },
    legend: {
      itemStyle: {
        fontSize: '15px',  // Adjust the font size for series name

      }
    },
    series: [
      {
        name: chartConfig.barLegend,
        type: 'column',
        data: barData,
        color: chartConfig.barColor || '#007bff',
        yAxis: 0,
        dataLabels: {
          enabled: true,
          inside: true,
          format: '{y}',
          verticalAlign: 'bottom', // Align at the bottom of the bar
          align: 'center',
          y: -5,
          style: {
            textOutline: 'white',
            color: 'black',
            fontSize: '15px', // Adjust the font size for data labels

          }
        },
      },
      {
        name: chartConfig.lineLegend,
        type: 'spline',
        data: lineData,
        color: chartConfig.lineColor || '#FF5733',
        yAxis: 1,
        dataLabels: {
          enabled: true,
          format: '{y}', style: {
            fontSize: '15px', // Adjust the font size for data labels

          }
        },
      },
    ],

  };

  const downloadHeader = async () => {
    if (headerRef.current) {
      const canvas = await html2canvas(headerRef.current);
      const link = document.createElement('a');
      link.href = canvas.toDataURL('image/png');
      link.download = `chart-header-${chartId}.png`;
      link.click();
    }
  };

  return (
    <div className='card shadow'>
      <div className='card-body' ref={headerRef}>
        <div className='chart-header d-flex justify-content-between'>
          <div className='left-content'>
            <h5 className='font-weight-bold' style={{ display: 'inline-flex', alignItems: 'center', gap: '5px' }}>
              {chartConfig.title} for every {' '}
              <select
                id={`manHoursChart${chartId}`}
                value={manHoursKey}
                onChange={(e) => chartConfig.onManHoursChange(`chart${chartId}`, Number(e.target.value))}
                style={{ display: 'inline', marginLeft: '5px', marginRight: '5px' }}
              >
                {[200000, 1000000].map((val) => (
                  <option key={val} value={val}>{val.toLocaleString()}</option>
                ))}
              </select>{' '}
              hours worked as of Year{' '}
              <Select
                defaultValue={yearOptions.find((option) => option.value === selectedYear)}
                options={yearOptions}
                onChange={handleYearChange}
                placeholder='Select Year'
                styles={{

                  container: (provided) => ({
                    ...provided,
                    display: 'inline-flex',
                    width: '150px',

                    marginLeft: '5px'
                  }),
                  control: (provided) => ({
                    ...provided,
                    width: '100px'
                  }),
                }}
              />
            </h5>


          </div>
          <div>
            <GetAppIcon
              onClick={downloadHeader}
              style={{
                fontSize: '18px',
                cursor: 'pointer',
                color: 'black',
              }}
            />
          </div>
        </div>
        <HighchartsWrapper options={options} />
      </div>
    </div>
  );
};

export default YearlyDataChart;
