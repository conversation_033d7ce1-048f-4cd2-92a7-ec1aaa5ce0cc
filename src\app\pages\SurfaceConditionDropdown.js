import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    SURFACE_CONDITIONS_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const SurfaceConditionDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.surfaceCondition ? incidentData.surfaceCondition.id ?? '' : '');
    useEffect(() => {
        setSelected(incidentData.surfaceCondition ? incidentData.surfaceCondition.id ?? '' : '')
    }, [incidentData.surfaceCondition])

    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(SURFACE_CONDITIONS_URL)
            .then(response => {
                setData(response.data);
            })
            .catch(error => {
                console.error('Error fetching risk categories', error);
            });
    }, []);


    return (
        <div className=''>


            <div className='form-group'>
                <label className=''> Surface Condition <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, surfaceConditionId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.name}</option>
                    ))}
                </select>
            </div>



        </div>
    );
};

SurfaceConditionDropdown.defaultProps = {
    readOnly: false
}
export default SurfaceConditionDropdown;
