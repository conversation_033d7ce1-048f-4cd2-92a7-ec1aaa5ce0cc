import React, { useState, useEffect } from 'react';
import { Card } from 'react-bootstrap';

const NewIncident = () => {
  const [iframeSrc, setIframeSrc] = useState('');

  useEffect(() => {
    // Get access token from localStorage
    const accessToken = localStorage.getItem('access_token');

    // Base URL for the iframe
    const baseUrl = 'https://incident-new-ui.web.app';

    // Construct URL with access token as parameter
    if (accessToken) {
      const urlWithToken = `${baseUrl}?access_token=${encodeURIComponent(accessToken)}`;
      setIframeSrc(urlWithToken);
    } else {
      // Fallback to base URL if no token found
      setIframeSrc(baseUrl);
    }
  }, []);

  return (
    <div className="row">
      <div className="col-12 grid-margin">
        <Card>

          <Card.Body className="p-0">
            <iframe
              src={iframeSrc}
              title="New Incident Reporter"
              width="100%"
              height="800px"
              style={{
                border: 'none',
                borderRadius: '0 0 0.375rem 0.375rem'
              }}
              allowFullScreen
            />
          </Card.Body>
        </Card>
      </div>
    </div>
  );
};



export default NewIncident;