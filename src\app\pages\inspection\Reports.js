import React, { useEffect, useState, useMemo,useRef } from 'react'

import { Modal, Button, Form } from 'react-bootstrap';


import moment from 'moment-timezone';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import Box from '@mui/material/Box';
import Select from 'react-select'
import FormRender from '../../apps/FormRender';
import ActionTable from './ActionTable';
import { MultiSelect } from 'primereact/multiselect';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useReactToPrint } from 'react-to-print';
import InspectionReport from './InspectioReport';


const InspectionTable = ({ inspection }) => {

    console.log(inspection)

    const contentRef =useRef()


    const [actionModal, setActionModal] = useState(false)
    const [current, setCurrent] = useState('')
    const [auditActionModal, setAuditActionModal] = useState(false)
    const [maskId, setMaskId] = useState('')
    const [totalAction, setTotalAction] = useState('')

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        maskId: { value: null, matchMode: FilterMatchMode.IN },
        "checklist.name": { value: null, matchMode: FilterMatchMode.IN },
        "assignedTo.firstName": { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        permitType: { value: null, matchMode: FilterMatchMode.IN },
    });


    const [data, setData] = useState(null);
    const [filterData, setFilterData] = useState([]);
    const [checklist, setChecklist] = useState([])
    const [assignee, setAssignee] = useState([])

    useEffect(() => {
        if (inspection) {
            setFilterData(inspection)

            const obs = inspection.map(item => {
                return { name: item.checklist?.name || '', value: item.checklist?.name || '' }
            })
            setChecklist(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

            const obs1 = inspection.map(item => {
                return { name: item.assignedTo?.firstName || '', value: item.assignedTo?.firstName || '' }
            })
            setAssignee(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
        }
    }, [inspection])

    // const getInspectionData = async () => {
    //     const params = {
    //         "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

    //     };
    //     const response = await API.get(`${INSPECTION_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);


    //     if (response.status === 200) {
    //         setData(response.data)
    //         setFilterData(response.data)
    //     }
    // }


    const [showModal, setShowModal] = useState(false)





    const viewInspection = (row) => {

        console.log('Row data:', row);

        // Set the current row for further operations (assuming `setCurrent` is available in your scope)
        setCurrent(row);

        // Group actions by their description using the `groupByDescription` function
        const totalActionData = groupByDescription(row.inspectionData.totalActions);
        console.log('Grouped action data:', totalActionData);

        // Filter the grouped actions to find those that are 'approve' and 'submitted'
        // const totalCompleted = totalActionData.filter(
        //     (item) => item.lastActionType === 'approve' && item.lastStatus === 'submitted'
        // // );
        // console.log('Total completed actions:', totalCompleted);

        // Example: Extracting maskId and action counts if needed
        const id = row.maskId;
        // Assuming you want to store the count of completed actions

        // Update the state with the maskId and the number of completed actions
        setMaskId(id);
        setTotalAction(totalActionData);
        setData(row)
        setShowModal(true)
    }
    const viewTemplate = (option) => {
        return (
            <i onClick={() => viewInspection(option)} style={{ fontSize: '18px' }} className='mdi mdi-eye'></i>
        )

    }

    const maskIdBodyTemplate = (row) => {


        // Render the clickable mask ID with a callback to view more details about the inspection
        return (
            <div className='maskid' onClick={() => viewInspection(row)}>
                {row.maskId}
            </div>
        );
    };


    const locationBodyTemplate = (rowData) => {


        return ` ${rowData.locationFour?.name || ''}`



    }
    function groupByDescription(data) {
        console.log(data);
        console.log('in');

        const filterData = data.filter(item => item.actionType !== 'inspect')

        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status } = item;
            if (!descriptionMap[description]) {
                descriptionMap[description] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[description].lastActionType = actionType;
                descriptionMap[description].actionTypes.push(actionType);
                descriptionMap[description].lastAssignedToId = assignedToId;
                descriptionMap[description].lastStatus = status;

            }
            descriptionMap[description].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }


    const actionBodyTemplate = (rowData) => {


        const totalActionData = groupByDescription(rowData.inspectionData.totalActions)



        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';




        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleAuditActionCard(rowData, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }

    // Function to determine status based on action completion ratio
    const getStatusFromActionRatio = (rowData) => {
        const totalActionData = groupByDescription(rowData.inspectionData.totalActions);
        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted');

        // If ratio is 3/3 (all actions completed)
        if (totalActionData.length > 0 && totalActionData.length === totalCompleted.length) {
            return "Completed with all actions closed";
        }
        // If ratio is 2/3 (some actions completed)
        else if (totalActionData.length > 0 && totalCompleted.length < totalActionData.length) {
            return "Completed with Open Actions";
        }
        // If ratio is 0/0 (no actions)
        else if (totalActionData.length === 0) {
            return "Completed";
        }

        // Return the original status if none of the conditions match
        return rowData.status;
    }

    const handleAuditActionCard = (data, actions) => {

        console.log(actions)
        setAuditActionModal(true)
        setCurrent(data)

        setMaskId(data.maskId)
        setTotalAction(actions)
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    }
    const checklistFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={checklist} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const assigneeFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={assignee} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const generatePdf = () => {
        const input = document.getElementById('pdf-content');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const marginBottom = 10; // margin at the bottom of each page

        html2canvas(input).then((canvas) => {
            const imgData = canvas.toDataURL('image/png');
            const imgProps = pdf.getImageProperties(imgData);
            const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
            heightLeft -= pdfHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight + marginBottom;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
                heightLeft -= pdfHeight;
            }

            pdf.save('permit-report.pdf');
        });
    }

    const header = () => {
        return (
            <div className='d-flex justify-content-end align-items-end'>
                {/* {hasPTWApplicantRole && <>
                                   <Button label="Apply Construction Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setConshow(true)} />
                                   <Button label="Apply DC Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setDcshow(true)} />
                               </>} */}



            </div>
        )
    }
    const reactToPrintFn = useReactToPrint({ contentRef });

    const dueDateBodyTemplate =(row)=>{
        return(
            moment(row.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")
        )
    }

     const convertToLocalTime = (gmtDate) => {
            // Get the system's time zone
            const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Define formats to handle
            const customFormat = 'DD-MM-YYYY'; // Format for "23-07-2024 13:35"

            let localDate;

            if (moment(gmtDate, customFormat, true).isValid()) {
                // If the input matches the custom format
                localDate = moment.tz(gmtDate, customFormat, 'GMT').tz(systemTimeZone).format('Do MMM YYYY');
            } else if (moment(gmtDate).isValid()) {
                // If the input is a valid ISO 8601 date
                localDate = moment.tz(gmtDate, 'GMT').tz(systemTimeZone).format('Do MMM YYYY');
            } else {
                throw new Error('Invalid date format');
            }

            return localDate;
        };
    return (
        <>

            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="">

                                <h4 className="card-title"></h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>



                                            <DataTable value={filterData} filters={filters} header={header} paginator rows={10} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                rowsPerPageOptions={[10, 25, 50]}
                                                emptyMessage="No Data found.">
                                                <Column field='maskId' header='ID' body={maskIdBodyTemplate} ></Column>
                                                <Column field='checklist.name' header='Type of Inspection' filterElement={checklistFilterTemplate} filter showFilterMatchModes={false}></Column>
                                                <Column field="location" header="Location" body={locationBodyTemplate} />
                                                <Column field='dateTime' header="DueDate" body={dueDateBodyTemplate}></Column>
                                                <Column field="assignedTo.firstName" header="Assignee" filterElement={assigneeFilterTemplate} filter showFilterMatchModes={false} />
                                                <Column field="status" header="Status" body={(rowData) => getStatusFromActionRatio(rowData)} />
                                                <Column field='' header="Action Status" body={actionBodyTemplate}></Column>


                                            </DataTable>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <Box>
                        {/* <div className='d-flex justify-content-end'>

                            <Button label="Export" icon="pi pi-upload" className="p-button" onClick={()=>reactToPrintFn()} >Export</Button>
                        </div> */}
                        {data && <InspectionReport inspection={data}  totalActions={totalAction} />
                        
                        // <div className="container " id='pdf-content' ref={contentRef}>
                        //     <div className="card page">

                        //         <div className="card-body">
                        //             <h5 className="card-title"># ({data.maskId})</h5>
                        //             {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}
                        //             <div className="row mb-3">
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title">Checklist</p>
                        //                     <p className="obs-content">
                        //                         {data.checklist?.name}
                        //                     </p>
                        //                 </div>
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title">Description</p>
                        //                     <p className="obs-content">
                        //                         {data.checklist?.description}
                        //                     </p>
                        //                 </div>
                        //             </div>
                        //             <div className="row mb-3">
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title">Location</p>
                        //                     <p className="obs-content">
                        //                         {[
                        //                             data.locationOne?.name,
                        //                             data.locationTwo?.name,
                        //                             data.locationThree?.name,
                        //                             data.locationFour?.name
                        //                         ]
                        //                             .filter(Boolean)
                        //                             .join(" > ")}
                        //                     </p>

                        //                 </div>
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title"> Date</p>
                        //                     <p className="obs-content">
                        //                         {moment(data.created).format('Do MMM YYYY') || ''}
                        //                     </p>
                        //                 </div>
                        //             </div>

                                   

                        //             <div className="row mb-3">
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title">Scheduler</p>
                        //                     <p className="obs-content">
                        //                         {data.assignedBy?.firstName}
                        //                     </p>
                        //                 </div>
                        //                 <div className="col-md-6">
                        //                     <p className="obs-title"> Inspector</p>
                        //                     <p className="obs-content">
                        //                         {data.assignedTo?.firstName || ''}
                        //                     </p>
                        //                 </div>
                        //             </div>



                                 

                        //             <p className="obs-title">Checklists</p>
                        //             <FormRender formData={data.checklistReport || []} />



                        //             <>


                        //                 <ActionTable id={maskId} actions={totalAction} current={current} />

                        //             </>




                        //         </div>
                        //     </div>
                        // </div>
                        }
                    </Box>

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setShowModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
            {auditActionModal &&
                <Modal
                    show={auditActionModal}
                    size="lg"
                    onHide={() => setAuditActionModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>

                        <ActionTable id={maskId} actions={totalAction} current={current} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="light"
                            onClick={() => {
                                setAuditActionModal(false);
                            }}
                        >
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
        </>
    )
}

export default InspectionTable;
