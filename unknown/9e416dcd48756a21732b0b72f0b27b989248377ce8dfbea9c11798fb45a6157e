import React, { useState, useEffect } from "react";
import $ from "jquery";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import { Button as PrimeButton } from "primereact/button";

import "primereact/resources/primereact.css";
import "primereact/resources/themes/saga-blue/theme.css";
import "primeicons/primeicons.css";

window.jQuery = $;
window.$ = $;

const MonthlyReport = ({ data, exportYearWiseExcelWithProjectName }) => {
  const [final, setFinal] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  // Get the year from the first item in the data array
  const [selectedYear, setSelectedYear] = useState(() => {
    if (data && data.length > 0 && data[0].year) {
      return data[0].year;
    }
    return new Date().getFullYear().toString();
  });
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  useEffect(() => {
    setFinal(data);
    setFilteredData(data);

    // Update selectedYear when data changes
    if (data && data.length > 0 && data[0].year) {
      setSelectedYear(data[0].year);
    }
  }, [data]);

  // Filter data when search term or status changes
  useEffect(() => {
    filterData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, selectedStatus, final]);

  const filterData = () => {
    if (!final || !final.length) return;

    let filtered = [...final];

    // Filter by location name
    if (searchTerm) {
      filtered = filtered.filter((location) =>
        location.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status if selected
    if (selectedStatus) {
      filtered = filtered.filter((location) => {
        // Check if any month has the selected status
        return months.some((month) => {
          const status = getStatusForMonth(location, month);

          if (selectedStatus === "reviewed" && status.color === "#28A745") {
            return true;
          } else if (
            selectedStatus === "submitted" &&
            status.color === "#F6C344"
          ) {
            return true;
          } else if (
            selectedStatus === "no-submission" &&
            status.color === "#6E6E6E"
          ) {
            return true;
          }

          return false;
        });
      });
    }

    setFilteredData(filtered);
  };

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const getStatusForMonth = (location, month) => {
    // Find the data for this location and month
    if (!location.data) return { status: "No Submission", color: "#6E6E6E" }; // Grey for no submission

    // Check if there's a reporter for this month
    const reportedBy = location.data.find(
      (item) => item.name === "Reported By"
    );
    const reviewedBy = location.data.find(
      (item) => item.name === "Reviewed By"
    );

    if (reportedBy) {
      const reporter = reportedBy[month.toLowerCase()];
      const reviewer = reviewedBy ? reviewedBy[month.toLowerCase()] : "-";

      // Clean and validate reporter name
      const cleanReporter =
        reporter &&
        typeof reporter === "string" &&
        reporter.trim() !== "" &&
        reporter !== "-"
          ? reporter.trim()
          : null;
      const cleanReviewer =
        reviewer &&
        typeof reviewer === "string" &&
        reviewer.trim() !== "" &&
        reviewer !== "-"
          ? reviewer.trim()
          : null;

      if (cleanReporter) {
        if (cleanReviewer) {
          return {
            status: cleanReporter,
            color: "#28A745", // Green for submitted and reviewed
            reporter: cleanReporter,
            reviewer: cleanReviewer,
          };
        }
        return {
          status: cleanReporter,
          color: "#F6C344", // Yellow for submitted but not reviewed
          reporter: cleanReporter,
          reviewer: "-",
        };
      }
    }

    // Check if the site has no submission for this month
    const date = location.data.find((item) => item.name === "date");
    if (date && date[month.toLowerCase()] === "-") {
      return { status: "No Submission", color: "#6E6E6E" }; // Grey for no submission
    }

    return {
      status: "No Submission",
      color: "#6E6E6E", // Grey for no submission
      reporter: "-",
      reviewer: "-",
    };
  };

  const handleViewReport = (location, month) => {
    // Find the data for this location and month
    const monthData = {};

    if (location.data) {
      location.data.forEach((item) => {
        monthData[item.name] = item[month.toLowerCase()];
      });
    }

    setSelectedReport({
      location: location.name,
      month: month,
      year: selectedYear,
      data: monthData,
    });

    setShowReportModal(true);
  };

  const renderMonthCell = (location, month) => {
    const status = getStatusForMonth(location, month);

    // Use the color directly from status
    const statusColor = status.color;
    let showUserInfo = false;
    let reporterName = "";
    let reviewerName = "";

    // Show user info for submitted reports (yellow or green)
    if (status.color === "#F6C344" || status.color === "#28A745") {
      showUserInfo = true;
      // Ensure we have valid names and handle edge cases
      reporterName =
        status.reporter && status.reporter !== "-"
          ? status.reporter
          : "Unknown";

      // Show reviewer name only for reviewed reports (green)
      if (status.color === "#28A745") {
        reviewerName =
          status.reviewer && status.reviewer !== "-"
            ? status.reviewer
            : "Unknown";
      }
    }

    return (
      <div
        key={`${location.name}-${month}`}
        className="month-cell"
        style={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "flex-start",
          position: "relative",
          cursor: status.color !== "#6E6E6E" ? "pointer" : "default",
          padding: "0",
        }}
        onClick={() => {
          if (status.color !== "#6E6E6E") {
            handleViewReport(location, month);
          }
        }}
      >
        {/* Color block indicator at the top */}
        <div
          className="status-color-block"
          style={{
            width: "100%",
            height: "8px",
            backgroundColor: statusColor,
            borderRadius: "0",
            marginBottom: "8px",
          }}
        />

        {/* User info for submitted reports */}
        {showUserInfo && (
          <div className="user-info">
            {/* Reporter name with icon */}
            <div
              style={{
                fontSize: "0.7rem",
                color: "#495057",
                fontWeight: "500",
                textAlign: "center",
                lineHeight: "1.1",
                width: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "2px",
                flexWrap: "wrap",
                wordBreak: "break-word",
                hyphens: "auto",
              }}
            >
              <i
                className="pi pi-user"
                style={{ color: "#007bff", fontSize: "0.6rem", flexShrink: 0 }}
                title="Reporter"
              ></i>
              <span
                title={`Reporter: ${reporterName}`}
                style={{
                  fontSize: "0.65rem",
                  maxWidth: "100%",
                  wordBreak: "break-word",
                  lineHeight: "1.1",
                }}
              >
                {reporterName}
              </span>
            </div>

            {/* Reviewer name with icon (only for reviewed reports) */}
            {reviewerName && reviewerName !== "Unknown" && (
              <div
                style={{
                  fontSize: "0.65rem",
                  color: "#6c757d",
                  fontWeight: "400",
                  textAlign: "center",
                  lineHeight: "1.1",
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "2px",
                  flexWrap: "wrap",
                  wordBreak: "break-word",
                  hyphens: "auto",
                  marginTop: "2px",
                }}
              >
                <i
                  className="pi pi-check"
                  style={{
                    color: "#28a745",
                    fontSize: "0.6rem",
                    flexShrink: 0,
                  }}
                  title="Reviewer"
                ></i>
                <span
                  title={`Reviewer: ${reviewerName}`}
                  style={{
                    fontSize: "0.6rem",
                    maxWidth: "100%",
                    wordBreak: "break-word",
                    lineHeight: "1.1",
                  }}
                >
                  {reviewerName}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const ReportDetailModal = () => {
    if (!selectedReport) return null;

    // Group data into categories
    const gdcEmployees = {
      "# of STT GDC Employees": selectedReport.data.numberOfEmployees || "0",
      "Total Hours Worked": selectedReport.data.dailyHoursOfEmployee || "0",
      "Total Working Days": selectedReport.data.workingDaysOfEmployee || "0",
    };

    const contractors = {
      "# of Contractor Employees":
        selectedReport.data.numberofContractors || "0",
      "Daily Hours Worked": selectedReport.data.dailyHoursOfContractors || "0",
      "Total Working Days": selectedReport.data.workingDaysOfContractors || "0",
    };

    const safetyActivities = {
      "# of Safety Inductions": selectedReport.data.noOfSafety || "0",
      "# of Toolbox Meetings": selectedReport.data.noOfToolbox || "0",
    };

    const ehsActivities = {
      "# of EHS Trainings": selectedReport.data.noOfEhsTraining || "0",
      "# of EHS Inspections/Audits": selectedReport.data.noOfInspection || "0",
      "# of Site Walk/Inspection":
        selectedReport.data.noOfManagmentSiteWalk || "0",
    };

    const external = {
      "# of Authority/NGO/Union Visits":
        selectedReport.data.noOfAuthority || "0",
    };

    // Get reporter and reviewer information
    const reporterName = selectedReport.data["Reported By"] || "Not Available";
    const reviewerName = selectedReport.data["Reviewed By"] || "Not Available";
    const reviewedDate =
      selectedReport.data["Reviewed Date"] || "Not Available";
    const reporterDate = selectedReport.data["date"] || "Not Available";

    return (
      <Modal
        show={showReportModal}
        onHide={() => setShowReportModal(false)}
        size="xl"
        centered
        dialogClassName="report-modal"
      >
        <Modal.Header
          closeButton
          style={{
            borderBottom: "2px solid #e9ecef",
            paddingBottom: "20px",
            backgroundColor: "#f8f9fa",
            position: "relative",
          }}
          className="modal-header-custom"
        >
          <div
            className="d-flex flex-column w-100"
            style={{ paddingRight: "50px" }}
          >
            {/* Main Header */}
            <div className="d-flex align-items-center justify-content-between w-100 mb-3">
              <div className="d-flex align-items-center flex-grow-1">
                <div
                  className="text-danger me-3"
                  style={{ minWidth: "140px", textAlign: "left" }}
                >
                  <i
                    className="pi pi-file-text me-2"
                    style={{ fontSize: "1.2rem" }}
                  ></i>
                  <strong style={{ fontSize: "1rem" }}>EHS Report</strong>
                </div>
                <div className="flex-grow-1">
                  <h4
                    className="mb-1"
                    style={{
                      color: "#2c3e50",
                      fontWeight: "600",
                      fontSize: "1.3rem",
                    }}
                  >
                    {selectedReport.location}
                  </h4>
                </div>
              </div>
              <div className="report-period-display">
                <div
                  className="d-flex align-items-center p-3 rounded-3"
                  style={{
                    backgroundColor: "#f3e5f5",
                    border: "1px solid #e1bee7",
                    minWidth: "160px",
                  }}
                >
                  <i
                    className="pi pi-calendar me-2"
                    style={{ color: "#8e24aa", fontSize: "1.1rem" }}
                  ></i>
                  <div>
                    <small
                      className="text-muted d-block"
                      style={{ fontSize: "0.75rem", fontWeight: "500" }}
                    >
                      Report Period
                    </small>
                    <strong style={{ color: "#8e24aa", fontSize: "0.9rem" }}>
                      {selectedReport.month} {selectedReport.year}
                    </strong>
                  </div>
                </div>
              </div>
            </div>

            {/* Report and Reviewer Information */}
            <div className="row g-3">
              <div className="col-lg-3 col-md-6">
                <div
                  className="d-flex align-items-center p-3 rounded-3 shadow-sm"
                  style={{
                    backgroundColor: "#e3f2fd",
                    border: "1px solid #bbdefb",
                  }}
                >
                  <i
                    className="pi pi-user me-2"
                    style={{ color: "#1976d2", fontSize: "1.2rem" }}
                  ></i>
                  <div className="flex-grow-1">
                    <small
                      className="text-muted d-block"
                      style={{ fontSize: "0.75rem", fontWeight: "500" }}
                    >
                      Reported By
                    </small>
                    <strong
                      style={{
                        color: "#1976d2",
                        fontSize: "0.85rem",
                        lineHeight: "1.2",
                      }}
                    >
                      {reporterName}
                    </strong>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div
                  className="d-flex align-items-center p-3 rounded-3 shadow-sm"
                  style={{
                    backgroundColor: "#fff3e0",
                    border: "1px solid #ffe0b2",
                  }}
                >
                  <i
                    className="pi pi-calendar me-2"
                    style={{ color: "#f57c00", fontSize: "1.2rem" }}
                  ></i>
                  <div className="flex-grow-1">
                    <small
                      className="text-muted d-block"
                      style={{ fontSize: "0.75rem", fontWeight: "500" }}
                    >
                      Reported Date
                    </small>
                    <strong
                      style={{
                        color: "#f57c00",
                        fontSize: "0.85rem",
                        lineHeight: "1.2",
                      }}
                    >
                      {reporterDate}
                    </strong>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div
                  className="d-flex align-items-center p-3 rounded-3 shadow-sm"
                  style={{
                    backgroundColor: "#e8f5e8",
                    border: "1px solid #c8e6c9",
                  }}
                >
                  <i
                    className="pi pi-check-circle me-2"
                    style={{ color: "#388e3c", fontSize: "1.2rem" }}
                  ></i>
                  <div className="flex-grow-1">
                    <small
                      className="text-muted d-block"
                      style={{ fontSize: "0.75rem", fontWeight: "500" }}
                    >
                      Reviewed By
                    </small>
                    <strong
                      style={{
                        color: "#388e3c",
                        fontSize: "0.85rem",
                        lineHeight: "1.2",
                      }}
                    >
                      {reviewerName}
                    </strong>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div
                  className="d-flex align-items-center p-3 rounded-3 shadow-sm"
                  style={{
                    backgroundColor: "#fff3e0",
                    border: "1px solid #ffe0b2",
                  }}
                >
                  <i
                    className="pi pi-calendar me-2"
                    style={{ color: "#f57c00", fontSize: "1.2rem" }}
                  ></i>
                  <div className="flex-grow-1">
                    <small
                      className="text-muted d-block"
                      style={{ fontSize: "0.75rem", fontWeight: "500" }}
                    >
                      Reviewed Date
                    </small>
                    <strong
                      style={{
                        color: "#f57c00",
                        fontSize: "0.85rem",
                        lineHeight: "1.2",
                      }}
                    >
                      {reviewedDate}
                    </strong>
                  </div>
                </div>
              </div>
              {/* <div className="col-lg-3 col-md-6">
                                <div className="d-flex align-items-center p-3 rounded-3 shadow-sm" style={{ backgroundColor: '#f3e5f5', border: '1px solid #e1bee7' }}>
                                    <i className="pi pi-info-circle me-2" style={{ color: '#8e24aa', fontSize: '1.2rem' }}></i>
                                    <div className="flex-grow-1">
                                        <small className="text-muted d-block" style={{ fontSize: '0.75rem', fontWeight: '500' }}>Report Period</small>
                                        <strong style={{ color: '#8e24aa', fontSize: '0.85rem', lineHeight: '1.2' }}>
                                            {selectedReport.month} {selectedReport.year}
                                        </strong>
                                    </div>
                                </div>
                            </div> */}
            </div>
          </div>
        </Modal.Header>
        <Modal.Body style={{ padding: "30px", backgroundColor: "#fafafa" }}>
          <div className="container-fluid p-0">
            <div className="row g-4">
              {/* GDC Employees Section */}
              <div className="col-lg-6 mb-3">
                <div
                  className="card h-100 shadow-sm border-0"
                  style={{
                    backgroundColor: "#e3f2fd",
                    borderRadius: "12px",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow =
                      "0 8px 25px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div className="bg-primary rounded-circle p-2 me-3">
                        <i
                          className="pi pi-users text-white"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </div>
                      <h5
                        className="card-title mb-0"
                        style={{
                          color: "#1565c0",
                          fontWeight: "700",
                          fontSize: "1.1rem",
                        }}
                      >
                        GDC Employees
                      </h5>
                    </div>
                    {Object.entries(gdcEmployees).map(([key, value]) => (
                      <div
                        className="d-flex justify-content-between align-items-center py-2 border-bottom border-light"
                        key={key}
                      >
                        <span style={{ fontSize: "0.9rem", color: "#495057" }}>
                          {key}:
                        </span>
                        <strong
                          className="badge bg-primary rounded-pill px-3 py-2"
                          style={{ fontSize: "0.85rem" }}
                        >
                          {value}
                        </strong>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Contractors Section */}
              <div className="col-lg-6 mb-3">
                <div
                  className="card h-100 shadow-sm border-0"
                  style={{
                    backgroundColor: "#e8f5e8",
                    borderRadius: "12px",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow =
                      "0 8px 25px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div
                        className="rounded-circle p-2 me-3"
                        style={{ backgroundColor: "#2e7d32" }}
                      >
                        <i
                          className="pi pi-briefcase text-white"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </div>
                      <h5
                        className="card-title mb-0"
                        style={{
                          color: "#2e7d32",
                          fontWeight: "700",
                          fontSize: "1.1rem",
                        }}
                      >
                        Contractors
                      </h5>
                    </div>
                    {Object.entries(contractors).map(([key, value]) => (
                      <div
                        className="d-flex justify-content-between align-items-center py-2 border-bottom border-light"
                        key={key}
                      >
                        <span style={{ fontSize: "0.9rem", color: "#495057" }}>
                          {key}:
                        </span>
                        <strong
                          className="badge rounded-pill px-3 py-2"
                          style={{
                            backgroundColor: "#2e7d32",
                            color: "white",
                            fontSize: "0.85rem",
                          }}
                        >
                          {value}
                        </strong>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Safety Activities Section */}
              <div className="col-lg-6 mb-3">
                <div
                  className="card h-100 shadow-sm border-0"
                  style={{
                    backgroundColor: "#fff8e1",
                    borderRadius: "12px",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow =
                      "0 8px 25px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div
                        className="rounded-circle p-2 me-3"
                        style={{ backgroundColor: "#f57f17" }}
                      >
                        <i
                          className="pi pi-shield text-white"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </div>
                      <h5
                        className="card-title mb-0"
                        style={{
                          color: "#f57f17",
                          fontWeight: "700",
                          fontSize: "1.1rem",
                        }}
                      >
                        Safety Activities
                      </h5>
                    </div>
                    {Object.entries(safetyActivities).map(([key, value]) => (
                      <div
                        className="d-flex justify-content-between align-items-center py-2 border-bottom border-light"
                        key={key}
                      >
                        <span style={{ fontSize: "0.9rem", color: "#495057" }}>
                          {key}:
                        </span>
                        <strong
                          className="badge rounded-pill px-3 py-2"
                          style={{
                            backgroundColor: "#f57f17",
                            color: "white",
                            fontSize: "0.85rem",
                          }}
                        >
                          {value}
                        </strong>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* EHS Activities Section */}
              <div className="col-lg-6 mb-3">
                <div
                  className="card h-100 shadow-sm border-0"
                  style={{
                    backgroundColor: "#f3e5f5",
                    borderRadius: "12px",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow =
                      "0 8px 25px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div
                        className="rounded-circle p-2 me-3"
                        style={{ backgroundColor: "#8e24aa" }}
                      >
                        <i
                          className="pi pi-cog text-white"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </div>
                      <h5
                        className="card-title mb-0"
                        style={{
                          color: "#8e24aa",
                          fontWeight: "700",
                          fontSize: "1.1rem",
                        }}
                      >
                        EHS Activities
                      </h5>
                    </div>
                    {Object.entries(ehsActivities).map(([key, value]) => (
                      <div
                        className="d-flex justify-content-between align-items-center py-2 border-bottom border-light"
                        key={key}
                      >
                        <span style={{ fontSize: "0.9rem", color: "#495057" }}>
                          {key}:
                        </span>
                        <strong
                          className="badge rounded-pill px-3 py-2"
                          style={{
                            backgroundColor: "#8e24aa",
                            color: "white",
                            fontSize: "0.85rem",
                          }}
                        >
                          {value}
                        </strong>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* External Section */}
              <div className="col-lg-12 mb-3">
                <div
                  className="card shadow-sm border-0"
                  style={{
                    backgroundColor: "#fce4ec",
                    borderRadius: "12px",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow =
                      "0 8px 25px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <div
                        className="rounded-circle p-2 me-3"
                        style={{ backgroundColor: "#c2185b" }}
                      >
                        <i
                          className="pi pi-globe text-white"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </div>
                      <h5
                        className="card-title mb-0"
                        style={{
                          color: "#c2185b",
                          fontWeight: "700",
                          fontSize: "1.1rem",
                        }}
                      >
                        External Activities
                      </h5>
                    </div>
                    <div className="row">
                      {Object.entries(external).map(([key, value]) => (
                        <div className="col-md-6" key={key}>
                          <div className="d-flex justify-content-between align-items-center py-2 border-bottom border-light">
                            <span
                              style={{ fontSize: "0.9rem", color: "#495057" }}
                            >
                              {key}:
                            </span>
                            <strong
                              className="badge rounded-pill px-3 py-2"
                              style={{
                                backgroundColor: "#c2185b",
                                color: "white",
                                fontSize: "0.85rem",
                              }}
                            >
                              {value}
                            </strong>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer
          style={{
            borderTop: "2px solid #e9ecef",
            backgroundColor: "#f8f9fa",
            padding: "20px 30px",
            borderBottomLeftRadius: "12px",
            borderBottomRightRadius: "12px",
          }}
        >
          <div className="d-flex justify-content-between align-items-center w-100">
            <div className="text-muted">
              <small>
                <i className="pi pi-info-circle me-1"></i>
                Report data for {selectedReport.month} {selectedReport.year}
              </small>
            </div>
            <div className="d-flex gap-2">
              <Button
                variant="secondary"
                onClick={() => setShowReportModal(false)}
                style={{
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  padding: "10px 20px",
                  fontWeight: "500",
                  transition: "all 0.2s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = "#5a6268";
                  e.target.style.transform = "translateY(-1px)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = "#6c757d";
                  e.target.style.transform = "translateY(0)";
                }}
              >
                <i className="pi pi-times me-2"></i>
                Close
              </Button>
            </div>
          </div>
        </Modal.Footer>
      </Modal>
    );
  };

  return (
    <div className="monthly-report-container">
      <div className="report-controls">
        <div className="d-flex flex-column flex-lg-row justify-content-between align-items-start align-items-lg-center gap-3 mb-4">
          {/* Wrapper for Search and Export */}
          <div className="d-flex flex-grow-1 justify-content-between flex-wrap gap-3">
            {/* Search Container - Left */}
            <div className="search-container">
              <div className="input-group shadow-sm">
                <span className="input-group-text bg-light border-end-0">
                  <i className="pi pi-search text-muted"></i>
                </span>
                <input
                  type="text"
                  className="form-control border-start-0 ps-0"
                  placeholder="Search locations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    fontSize: "0.95rem",
                    padding: "12px 16px",
                  }}
                />
              </div>
            </div>

            {/* Export Button - Right */}
            <div>
              <button
                className="btn btn-export shadow-sm"
                onClick={() =>
                  exportYearWiseExcelWithProjectName(
                    data,
                    `Statistics_${selectedYear}.xlsx`
                  )
                }
              >
                <i className="pi pi-file-excel me-2"></i>
                Export Excel
              </button>
            </div>
          </div>
        </div>

        {/* Status Legend */}
        <div className="status-legend shadow-sm">
          <div className="d-flex flex-column flex-lg-row align-items-start align-items-lg-center gap-3">
            <div className="legend-title">
              <i className="pi pi-info-circle me-2 text-primary"></i>
              <strong>Legend:</strong>
            </div>
            <div className="legend-items">
              <div className="legend-item">
                <div
                  className="legend-color-block"
                  style={{ backgroundColor: "#28A745" }}
                ></div>
                <span>Reviewed & Submitted</span>
              </div>
              <div className="legend-item">
                <div
                  className="legend-color-block"
                  style={{ backgroundColor: "#F6C344" }}
                ></div>
                <span>Submitted (Pending Review)</span>
              </div>
              <div className="legend-item">
                <div
                  className="legend-color-block"
                  style={{ backgroundColor: "#6E6E6E" }}
                ></div>
                <span>No Submission</span>
              </div>
            </div>

            <div className="legend-divider"></div>

            <div className="legend-icons">
              <div className="legend-item">
                <i
                  className="pi pi-user"
                  style={{
                    color: "#007bff",
                    fontSize: "1rem",
                    marginRight: "6px",
                  }}
                ></i>
                <span>Reporter</span>
              </div>
              <div className="legend-item">
                <i
                  className="pi pi-check"
                  style={{
                    color: "#28a745",
                    fontSize: "1rem",
                    marginRight: "6px",
                  }}
                ></i>
                <span>Reviewer</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="calendar-grid">
        <div className="calendar-header">
          <div className="location-header">Location</div>
          {months.map((month) => (
            <div key={month} className="month-header">
              {month} {selectedYear.slice(-2)}
            </div>
          ))}
        </div>

        <div className="calendar-body">
          {filteredData.length > 0 ? (
            filteredData.map((location, index) => (
              <div key={index} className="calendar-row">
                <div className="location-cell">{location.name}</div>
                {months.map((month) => (
                  <div key={month} className="month-cell-container">
                    {renderMonthCell(location, month)}
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="no-data-message">
              <p>No locations found matching your search criteria.</p>
            </div>
          )}
        </div>
      </div>

      {/* Status Legend */}
      {/* <div className="status-legend">
                <div className="legend-title">Legend:</div>
                <div className="legend-items">
                    <div className="legend-item">
                        <div className="legend-color-block" style={{ backgroundColor: '#28A745' }}></div>
                        <span>Report Reviewed and Submitted</span>
                    </div>
                    <div className="legend-item">
                        <div className="legend-color-block" style={{ backgroundColor: '#F6C344' }}></div>
                        <span>Report Submitted but Not Reviewed</span>
                    </div>
                    <div className="legend-item">
                        <div className="legend-color-block" style={{ backgroundColor: '#6E6E6E' }}></div>
                        <span>No Report Submitted</span>
                    </div>
                </div>
            </div> */}

      <ReportDetailModal />

      <style jsx="true">{`
        .calendar-grid {
          display: flex;
          flex-direction: column;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          overflow: hidden;
          width: 100%;
          table-layout: fixed;
          background-color: white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .calendar-header {
          display: flex;
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          border-bottom: 2px solid #e9ecef;
        }

        .location-header {
          width: 250px;
          padding: 12px 16px;
          border-right: 1px solid #e9ecef;
          font-size: 0.9rem;
          color: #495057;
        }

        .month-header {
          width: calc((100% - 250px) / 12);
          min-width: 100px;
          padding: 12px 4px;
          text-align: center;
          border-right: 1px solid #e9ecef;
          white-space: nowrap;
          font-size: 0.8rem;
          color: #495057;
        }

        .calendar-body {
          display: flex;
          flex-direction: column;
        }

        .calendar-row {
          display: flex;
          border-bottom: 1px solid #e9ecef;
          transition: background-color 0.2s ease;
        }

        .calendar-row:hover {
          background-color: #f8f9fa;
        }

        .location-cell {
          width: 250px;
          padding: 16px;
          border-right: 1px solid #e9ecef;
          background-color: white;
          font-size: 0.85rem;
          color: #495057;
          font-weight: 500;
          line-height: 1.4;
        }

        .month-cell-container {
          width: calc((100% - 250px) / 12);
          min-width: 100px;
          border-right: 1px solid #e9ecef;
          min-height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px 2px;
          background-color: white;
          overflow: visible;
        }

        .month-cell {
          border-radius: 4px;
          transition: all 0.2s ease;
          font-size: 0.85rem;
        }

        .month-cell:hover {
          background-color: #f8f9fa;
          transform: scale(1.02);
        }

        .status-color-block {
          transition: all 0.2s ease;
        }

        .month-cell:hover .status-color-block {
          height: 10px;
        }

        .monthly-report-container {
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          padding: 32px;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          margin-bottom: 24px;
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Modal Improvements */
        .report-modal .modal-content {
          border-radius: 16px;
          border: none;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
          overflow: hidden;
        }

        /* Custom Modal Header Styling */
        .modal-header-custom {
          position: relative;
        }

        .modal-header-custom .btn-close,
        .report-modal .modal-header .btn-close {
          position: absolute !important;
          top: 15px !important;
          right: 15px !important;
          z-index: 1050 !important;
          background: rgba(255, 255, 255, 0.95) !important;
          border-radius: 50% !important;
          width: 35px !important;
          height: 35px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
          transition: all 0.2s ease !important;
          border: 1px solid rgba(0, 0, 0, 0.1) !important;
          opacity: 1 !important;
          visibility: visible !important;
        }

        .modal-header-custom .btn-close:hover,
        .report-modal .modal-header .btn-close:hover {
          background: rgba(220, 53, 69, 0.1) !important;
          transform: scale(1.1) !important;
          border-color: rgba(220, 53, 69, 0.3) !important;
        }

        .modal-header-custom .btn-close:focus,
        .report-modal .modal-header .btn-close:focus {
          box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
        }

        /* Improved Report Period Display */
        .report-period-display {
          flex-shrink: 0;
          margin-left: 20px;
        }

        .report-modal .modal-header {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-bottom: 2px solid #dee2e6;
        }

        /* Ensure close button is always visible */
        .report-modal .modal-header button[aria-label="Close"],
        .report-modal .modal-header .btn-close,
        .modal-header button[aria-label="Close"] {
          position: absolute !important;
          top: 15px !important;
          right: 15px !important;
          z-index: 1051 !important;
          background: rgba(255, 255, 255, 0.95) !important;
          border-radius: 50% !important;
          width: 35px !important;
          height: 35px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
          transition: all 0.2s ease !important;
          border: 1px solid rgba(0, 0, 0, 0.1) !important;
          opacity: 1 !important;
          visibility: visible !important;
          color: #6c757d !important;
          font-size: 1.2rem !important;
          font-weight: bold !important;
        }

        /* Add X symbol inside the circle */
        .report-modal .modal-header button[aria-label="Close"]::before,
        .report-modal .modal-header .btn-close::before,
        .modal-header button[aria-label="Close"]::before {
          content: "×" !important;
          font-size: 20px !important;
          font-weight: bold !important;
          color: #6c757d !important;
          line-height: 1 !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .report-modal .modal-header button[aria-label="Close"]:hover::before,
        .report-modal .modal-header .btn-close:hover::before,
        .modal-header button[aria-label="Close"]:hover::before {
          color: #dc3545 !important;
        }

        .report-modal .modal-body {
          background-color: #fafafa;
        }

        .report-modal .modal-footer {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-top: 2px solid #dee2e6;
        }

        .report-controls {
          margin-bottom: 32px;
        }

        .search-container {
          min-width: 300px;
          max-width: 450px;
        }

        .search-container .input-group {
          border-radius: 8px;
          overflow: hidden;
        }

        .search-container .input-group-text {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          color: #6c757d;
          border-right: none;
        }

        .search-container .form-control {
          border: 1px solid #e9ecef;
          font-size: 0.95rem;
          transition: all 0.2s ease;
        }

        .search-container .form-control:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
          background-color: #fff;
        }

        .btn-export {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          border: none;
          color: white;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 0.9rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          min-width: 150px;
          justify-content: center;
        }

        .btn-export:hover {
          background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }

        .no-data-message {
          padding: 30px;
          text-align: center;
          color: #666;
          font-size: 1.1rem;
          background-color: #f9f9f9;
          border-radius: 4px;
          width: 100%;
        }

        .status-legend {
          padding: 20px 24px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-radius: 12px;
          border: 1px solid #dee2e6;
          margin-bottom: 24px;
        }

        .legend-title {
          font-weight: 700;
          color: #495057;
          font-size: 1rem;
          display: flex;
          align-items: center;
          margin-bottom: 0;
        }

        .legend-items {
          display: flex;
          gap: 24px;
          flex-wrap: wrap;
          align-items: center;
        }

        .legend-icons {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;
          align-items: center;
        }

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.9rem;
          color: #495057;
          font-weight: 500;
          padding: 6px 12px;
          background-color: rgba(255, 255, 255, 0.7);
          border-radius: 20px;
          transition: all 0.2s ease;
        }

        .legend-item:hover {
          background-color: rgba(255, 255, 255, 0.9);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .legend-color-block {
          width: 24px;
          height: 10px;
          border-radius: 5px;
          flex-shrink: 0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .legend-divider {
          width: 1px;
          height: 30px;
          background-color: #dee2e6;
          margin: 0 8px;
        }

        /* Ensure PrimeReact icons display properly */
        .pi {
          font-family: "primeicons" !important;
          font-style: normal;
          font-weight: normal;
          font-variant: normal;
          text-transform: none;
          line-height: 1;
          display: inline-block;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        /* Ensure user info displays properly in cells */
        .month-cell .user-info {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1px;
          padding: 2px;
          box-sizing: border-box;
        }

        .month-cell .user-info > div {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 2px;
          word-break: break-word;
          overflow-wrap: break-word;
          hyphens: auto;
          text-align: center;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1600px) {
          .month-cell-container {
            min-width: 95px;
            padding: 4px 2px;
          }

          .month-header {
            min-width: 95px;
            font-size: 0.8rem;
            padding: 12px 4px;
          }

          .location-cell {
            width: 280px;
            font-size: 0.9rem;
          }

          .location-header {
            width: 280px;
            font-size: 0.95rem;
          }
        }

        @media (max-width: 1400px) {
          .month-cell-container {
            min-width: 85px;
            padding: 3px 1px;
            min-height: 75px;
          }

          .month-header {
            min-width: 85px;
            font-size: 0.75rem;
            padding: 10px 2px;
          }

          .location-cell {
            width: 260px;
            font-size: 0.85rem;
            padding: 14px;
          }

          .location-header {
            width: 260px;
            font-size: 0.9rem;
            padding: 10px 14px;
          }

          .legend-items {
            gap: 16px;
          }

          .legend-icons {
            gap: 16px;
          }

          .monthly-report-container {
            padding: 28px 24px;
          }
        }

        @media (max-width: 1200px) {
          .month-cell-container {
            min-width: 80px;
            min-height: 70px;
            padding: 2px 1px;
          }

          .month-header {
            min-width: 80px;
            font-size: 0.7rem;
            padding: 8px 2px;
          }

          .location-cell {
            width: 240px;
            font-size: 0.8rem;
            padding: 12px;
            line-height: 1.3;
          }

          .location-header {
            width: 240px;
            font-size: 0.85rem;
            padding: 8px 12px;
          }

          .monthly-report-container {
            padding: 24px 16px;
            border-radius: 12px;
          }

          .status-legend {
            padding: 16px 20px;
            border-radius: 10px;
          }

          .legend-items {
            gap: 12px;
          }

          .legend-icons {
            gap: 12px;
          }

          .search-container {
            min-width: 280px;
          }

          .btn-export {
            padding: 10px 18px;
            font-size: 0.85rem;
            min-width: 140px;
          }

          /* Modal responsiveness */
          .report-modal .modal-dialog {
            max-width: 95%;
            margin: 1rem auto;
          }
        }

        @media (max-width: 992px) {
          .month-cell-container {
            min-width: 75px;
            min-height: 65px;
            padding: 2px 0px;
          }

          .month-header {
            min-width: 75px;
            font-size: 0.65rem;
            padding: 6px 1px;
          }

          .location-cell {
            width: 220px;
            font-size: 0.75rem;
            padding: 10px;
          }

          .location-header {
            width: 220px;
            font-size: 0.8rem;
            padding: 6px 10px;
          }

          .calendar-grid {
            font-size: 0.8rem;
          }

          .user-info div {
            font-size: 0.6rem !important;
          }

          .user-info i {
            font-size: 0.5rem !important;
          }
        }

        @media (max-width: 768px) {
          .search-container {
            min-width: 100%;
            max-width: 100%;
          }

          .btn-export {
            min-width: 100%;
            padding: 12px 16px;
            font-size: 0.9rem;
            justify-content: center;
          }

          .legend-items {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            width: 100%;
          }

          .legend-icons {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            width: 100%;
          }

          .legend-divider {
            width: 100%;
            height: 1px;
            margin: 8px 0;
          }

          .monthly-report-container {
            padding: 16px 8px;
            border-radius: 8px;
            margin: 0 -8px;
          }

          .status-legend {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 16px;
          }

          .legend-title {
            font-size: 0.9rem;
            margin-bottom: 8px;
          }

          .legend-item {
            padding: 8px 12px;
            font-size: 0.8rem;
            width: 100%;
            justify-content: flex-start;
          }

          /* Mobile Calendar Adjustments */
          .month-cell-container {
            min-width: 60px;
            min-height: 55px;
            padding: 1px;
          }

          .month-header {
            min-width: 60px;
            font-size: 0.6rem;
            padding: 4px 1px;
          }

          .location-cell {
            width: 180px;
            font-size: 0.7rem;
            padding: 8px;
            line-height: 1.2;
          }

          .location-header {
            width: 180px;
            font-size: 0.75rem;
            padding: 4px 8px;
          }

          .status-color-block {
            height: 6px !important;
            margin-bottom: 4px !important;
          }

          .user-info {
            padding: 1px !important;
            gap: 0px !important;
          }

          .user-info div {
            font-size: 0.55rem !important;
            gap: 1px !important;
            line-height: 1 !important;
          }

          .user-info i {
            font-size: 0.45rem !important;
          }

          /* Modal Mobile Adjustments */
          .report-modal .modal-dialog {
            max-width: 98%;
            margin: 0.5rem auto;
          }

          .report-modal .modal-content {
            border-radius: 8px;
          }

          .report-modal .modal-header {
            padding: 16px;
            flex-direction: column;
            align-items: flex-start;
          }

          .report-modal .modal-body {
            padding: 16px;
          }

          .report-modal .modal-footer {
            padding: 12px 16px;
            flex-direction: column;
            gap: 8px;
          }

          .report-modal .modal-footer .d-flex {
            flex-direction: column;
            width: 100%;
            gap: 8px;
          }

          .report-modal .modal-footer .d-flex .d-flex {
            flex-direction: row;
            gap: 8px;
          }
        }

        @media (max-width: 576px) {
          .monthly-report-container {
            padding: 12px 4px;
            margin: 0 -4px;
          }

          .report-controls {
            margin-bottom: 16px;
          }

          .search-container .form-control {
            font-size: 0.9rem;
            padding: 10px;
          }

          .btn-export {
            padding: 10px 12px;
            font-size: 0.85rem;
          }

          /* Ultra Mobile Calendar */
          .calendar-grid {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
          }

          .month-cell-container {
            min-width: 50px;
            min-height: 50px;
            padding: 0px;
          }

          .month-header {
            min-width: 50px;
            font-size: 0.55rem;
            padding: 2px 0px;
            line-height: 1.1;
          }

          .location-cell {
            width: 160px;
            font-size: 0.65rem;
            padding: 6px;
            line-height: 1.1;
          }

          .location-header {
            width: 160px;
            font-size: 0.7rem;
            padding: 2px 6px;
          }

          .status-color-block {
            height: 4px !important;
            margin-bottom: 2px !important;
          }

          .user-info div {
            font-size: 0.5rem !important;
            line-height: 0.9 !important;
          }

          .user-info i {
            font-size: 0.4rem !important;
          }

          /* Mobile Modal Full Screen */
          .report-modal .modal-dialog {
            max-width: 100%;
            margin: 0;
            height: 100vh;
          }

          .report-modal .modal-content {
            height: 100vh;
            border-radius: 0;
            display: flex;
            flex-direction: column;
          }

          .report-modal .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 12px;
          }

          .report-modal .modal-header {
            padding: 12px;
            border-radius: 0;
          }

          .report-modal .modal-footer {
            padding: 8px 12px;
            border-radius: 0;
          }

          /* Status Legend Mobile */
          .status-legend {
            padding: 8px;
            margin-bottom: 12px;
          }

          .legend-title {
            font-size: 0.85rem;
            margin-bottom: 6px;
          }

          .legend-item {
            padding: 6px 8px;
            font-size: 0.75rem;
            border-radius: 15px;
          }

          .legend-color-block {
            width: 20px;
            height: 8px;
          }
        }

        @media (max-width: 480px) {
          .month-cell-container {
            min-width: 45px;
            min-height: 45px;
          }

          .month-header {
            min-width: 45px;
            font-size: 0.5rem;
            padding: 1px 0px;
          }

          .location-cell {
            width: 140px;
            font-size: 0.6rem;
            padding: 4px;
          }

          .location-header {
            width: 140px;
            font-size: 0.65rem;
            padding: 1px 4px;
          }

          .user-info div {
            font-size: 0.45rem !important;
          }

          .user-info i {
            font-size: 0.35rem !important;
          }
        }

        /* Landscape orientation adjustments */
        @media (max-width: 768px) and (orientation: landscape) {
          .report-modal .modal-dialog {
            max-width: 95%;
            margin: 0.5rem auto;
            height: auto;
          }

          .report-modal .modal-content {
            height: auto;
            max-height: 95vh;
            overflow-y: auto;
          }

          .monthly-report-container {
            padding: 16px 12px;
          }
        }

        /* Print styles */
        @media print {
          .monthly-report-container {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
            background: white !important;
          }

          .btn-export,
          .search-container,
          .status-legend {
            display: none !important;
          }

          .calendar-grid {
            break-inside: avoid;
          }

          .calendar-row {
            break-inside: avoid;
          }

          .month-cell-container {
            min-height: 60px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default MonthlyReport;
