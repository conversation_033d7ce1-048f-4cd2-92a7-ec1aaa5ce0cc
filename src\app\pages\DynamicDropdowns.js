import React, { useState, useEffect } from 'react';
import API from '../services/API';
import { HUMAN_ONE_TWO_URL, HUMAN_ONE_URL } from '../constants';

// Mock API calls (replace with actual API calls)

const DropdownSet = ({ id, bodyParts, readOnly, onDelete, selectedHumanOne, selectedHumanTwo, onUpdateSelectedValues }) => {
    const [humanOnes, setHumanOnes] = useState([]);
    const [humanTwos, setHumanTwos] = useState([]);

    const subPartsMap = {
        "head": ["Ear", "Eye", "Face", "Head", "Nose", "Mouth", "Neck"],
        "left_shoulder": ["Shoulder"],
        "right_shoulder": ["Shoulder"],
        "chest": ["Chest"],
        "left_arm": ["Upper Arm", "Forearm"],
        "right_arm": ["Upper Arm", "Forearm"],
        "left_hand": ["Hand", "Finger", "Thumb"],
        "right_hand": ["Hand", "Finger", "Thumb"],
        "left_leg": ["Upper Leg", "Lower Leg"],
        "right_leg": ["Upper Leg", "Lower Leg"],
        "left_foot": ["Foot", "Toe"],
        "right_foot": ["Foot", "Toe"],
        "stomach": ["Abdomen", "Groin", "Pelvis", "Upper Back", "Lower Back"],
    };

    const getSelectedParts = () => {
        let selectedParts = [];
        for (const [part, info] of Object.entries(bodyParts)) {
            if (info.selected) {
                selectedParts = [...selectedParts, ...subPartsMap[part]];
            }
        }
        return [...new Set(selectedParts)]; // Remove duplicates
    };

    useEffect(() => {
        const fetchHumanOnes = async () => {
            const response = await API.get(HUMAN_ONE_URL);
            if (response.status === 200) {
                const selectedParts = getSelectedParts();
                setHumanOnes(response.data.filter(item => selectedParts.includes(item.name)));
            }
        };
        fetchHumanOnes();
    }, [bodyParts]); // Fetch humanOnes when bodyParts change

    useEffect(() => {
        const fetchHumanTwos = async () => {
            if (selectedHumanOne) {
                const response = await API.get(HUMAN_ONE_TWO_URL(selectedHumanOne));
                if (response.status === 200) {
                    setHumanTwos(response.data);
                }
            }
        };
        fetchHumanTwos();
    }, [selectedHumanOne]); // Fetch humanTwos when selectedHumanOne changes

    const handleHumanOneChange = (e) => {
        const newSelectedHumanOne = e.target.value;
        onUpdateSelectedValues(id, newSelectedHumanOne, selectedHumanTwo);
    };

    const handleHumanTwoChange = (e) => {
        const newSelectedHumanTwo = e.target.value;
        onUpdateSelectedValues(id, selectedHumanOne, newSelectedHumanTwo);
    };
    return (
        <div>

            <select disabled={readOnly} className="form-select" onChange={handleHumanOneChange} value={selectedHumanOne}>
                <option value="">Select Body Parts</option>
                {humanOnes.map((humanOne, index) => (
                    <option key={index} value={humanOne.id}>{humanOne.name}</option>
                ))}
            </select>
            {selectedHumanOne && (
                <select disabled={readOnly} className='form-select' onChange={handleHumanTwoChange} value={selectedHumanTwo}>
                    <option value="">Select Injuries</option>
                    {humanTwos.map((humanTwo, index) => (
                        <option key={index} value={humanTwo.id}>{humanTwo.name}</option>
                    ))}
                </select>
            )}
            {readOnly && <hr />}
            {!readOnly && <button className="btn btn-danger" onClick={() => onDelete(id)}><i className='mdi mdi-delete'></i></button>}
        </div>
    );
};

const DynamicDropdowns = ({ bodyParts, readOnly, dropdownSets, onAddDropdownSet, onDeleteDropdownSet, onUpdateSelectedValues }) => {




    return (
        <div>
            {readOnly ? <h4>Selected Body Parts & Injuries</h4> : <h4>Select Body Parts & Injuries</h4>}
            {dropdownSets.map(({ id, selectedHumanOne, selectedHumanTwo }) => (
                <DropdownSet readOnly={readOnly} key={id} id={id} bodyParts={bodyParts} selectedHumanOne={selectedHumanOne}
                    selectedHumanTwo={selectedHumanTwo}
                    onDelete={onDeleteDropdownSet}
                    onUpdateSelectedValues={onUpdateSelectedValues} />
            ))}
            {!readOnly && <button className="btn btn-primary mt-3" onClick={onAddDropdownSet}>Add More</button>}
        </div>
    );
};

export default DynamicDropdowns;
