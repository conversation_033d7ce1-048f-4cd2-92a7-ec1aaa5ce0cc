// @ts-nocheck
import React, { useState, useRef, useEffect, useCallback } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form, Table } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EHS_ROLE_URL, EPTW_ROLE_URL, EXTERNAL_USERS_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, AUDIT_ROLE_URL, GROUP_EHS_ROLE_URL, REPORT_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, USERS_URL, USERS_URL_WITH_ID, GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, INDIVIDUAL_USER_LOCATION_ROLE_URL, USER_LOCATION_ROLE_WITH_ID_URL, GET_MY_USER_LOCATION_ROLE_URL, LOCATION2_URL, LOCATION3_URL, LOCATION4_URL, UPDATE_BULK_USER_URL, OTHER_ROLE_URL } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { userColumns, tableOptions } from './TableColumns';
import CardOverlay from './CardOverlay';
import { DropzoneArea } from 'material-ui-dropzone';
import { LinearProgress } from '@material-ui/core';
import * as XLSX from 'xlsx';
import AllFilterLocation from './AllFilterLocation';
import FilterLocation from './FilterLocation';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const MasterUser = () => {
    const defaultMaterialTheme = createTheme();

    const [mdShow, setMdShow] = useState(false);
    const [countryAssignShow, setCountryAssignShow] = useState(false)
    const [userShow, setUserShow] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const uName = useRef();
    const uEmail = useRef();
    const uPassword = useRef();
    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [], groupEhs: [], report: [], other: [] })
    const [selectedRoles, setSelectedRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [], groupEhs: [], report: [], other: [] })
    const [selectedUserId, setSelectedUserId] = useState({ id: "", email: "", name: "" });
    const [allLocationRoles, setAllLocationRoles] = useState([])
    const [country, setCountry] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    useEffect(() => {
        getCountry();
        getLocationTwo();
        getLocationThree();
        getLocationFour();
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();
        getGroupEhsRole();
        getReportRole();
        getAuditRole();
        getOtherRole();


    }, [])
    const getUserLocationRole = async (id) => {
        const response = await API.get(GET_MY_USER_LOCATION_ROLE_URL(id))

        if (response.status === 200) {

            setAllLocationRoles(response.data)
        }
    }


    const getCountry = async () => {
        const response = await API.get(LOCATION1_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, country: response.data } })
            setCountry(response.data)
        }
    }

    const getLocationTwo = async () => {
        const response = await API.get(LOCATION2_URL)

        if (response.status === 200) {

            setLocationTwo(response.data)
        }
    }

    const getLocationThree = async () => {
        const response = await API.get(LOCATION3_URL)

        if (response.status === 200) {

            setLocationThree(response.data)
        }
    }

    const getLocationFour = async () => {
        const response = await API.get(LOCATION4_URL)

        if (response.status === 200) {

            setLocationFour(response.data)
        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getAuditRole = async () => {
        const response = await API.get(AUDIT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, audit: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }

    const getGroupEhsRole = async () => {
        const response = await API.get(GROUP_EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, groupEhs: response.data } })
        }
    }

    const getReportRole = async () => {
        const response = await API.get(REPORT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, report: response.data } })
        }
    }
    const getOtherRole = async () => {
        const response = await API.get(OTHER_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, other: response.data } })
        }
    }

    const uRole = useRef();

    const thead = [
        'Name',
        'Email',
        'Organization',
        'Role Assignment',

    ];



    const [data, setData] = useState([])
    useEffect(() => {
        getUsersData();
    }, [])

    const getUsersData = async () => {
        const response = await API.get(USERS_URL);
        if (response.status === 200) {
            setData(response.data.filter(i => i.status !== false).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())))

        }
    }

    const viewAssignPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [], groupEhs: [], report: [], other: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            getUserLocationRole(id)
            setMdShow(true)

        }
    }

    const viewCountryPermission = async (id, email, name) => {
        const response = await API.get(USERS_URL_WITH_ID(id))
        if (response.status === 200) {

            if (response.data.customRoles)
                setSelectedRoles(response.data.customRoles)
            else
                setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [], groupEhs: [], report: [], other: [] })
            setSelectedUserId({ id: id, email: email, name: name })
            getUserLocationRole(id)
            setCountryAssignShow(true)

        }
    }

    useEffect(() => {
        if (allLocationRoles && countryAssignShow) {
            // Only set initial roles when modal first opens, don't overwrite user selections
            setSelectedRoles((prev) => {
                // If country roles are empty (initial state), populate with existing roles
                if (prev.country.length === 0) {
                    return { ...prev, country: allLocationRoles.reduce((acc, roleObject) => acc.concat(roleObject.roles), []) }
                }
                // Otherwise, preserve existing selections
                return prev;
            })
        }
    }, [allLocationRoles, countryAssignShow])

    const handleRoleChange = (e, category) => {
        const roleId = e.target.value;
        console.log(roleId)
        setIndividualSelectedRole((prevRoles) => {
            if (e.target.checked) {
                // Add the role to the selected roles
                return { ...prevRoles, roles: [...prevRoles.roles, roleId] };
            } else {
                // Remove the role from the selected roles
                return { ...prevRoles, roles: prevRoles.roles.filter((id) => id !== roleId) };
            }
        });
        setSelectedRoles((prevRoles) => {
            const categoryRoles = prevRoles[category] || [];

            console.log(category, prevRoles, categoryRoles, 'check')
            if (e.target.checked) {
                // Add the role to the selected roles
                return {
                    ...prevRoles,
                    [category]: [...categoryRoles, roleId],
                };
            } else {
                // Remove the role from the selected roles
                return {
                    ...prevRoles,
                    [category]: categoryRoles.filter((id) => id !== roleId),
                };
            }
        });
    };

    const handleAssignSubmit = async () => {
        const id = selectedUserId.id;
        let flag = false;
        const response = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: id, roles: individualSelectedRole.roles, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } })
        if (response.status === 200) {
            flag = true;
        }
        const response2 = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response2.status === 204 && flag) {
            // Clear all form states after successful assignment
            clearFormStates();

            // Refresh existing assignments data to show the newly assigned permissions
            await getUserLocationRole(id);

            cogoToast.info('Assigned', { position: 'top-right' })
        }
    }


    const handleCountrySubmit = async () => {
        const id = selectedUserId.id;
        let flag = false;
        const response = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: id, roles: individualSelectedRole.roles, locations: { locationOne: "", locationTwo: "", locationThree: "", locationFour: "" } })
        if (response.status === 200) {
            flag = true;
        }
        const response2 = await API.patch(USERS_URL_WITH_ID(id), { email: selectedUserId.email, customRoles: selectedRoles })
        if (response2.status === 204 && flag) {
            // Clear all form states after successful assignment
            clearFormStates();

            // Refresh existing assignments data to show the newly assigned permissions
            await getUserLocationRole(id);
            setCountryAssignShow(false)
            cogoToast.info('Assigned', { position: 'top-right' })
        }
    }

    const handleAssignClose = () => {
        // Clear all form states when closing modal
        clearFormStates();

        // Reset modal states
        setSelectedRoles({ country: [], ehs: [], eptw: [], incident: [], inspection: [], audit: [], plant: [], groupEhs: [], report: [], other: [] })
        setSelectedUserId({ id: "", email: "", name: "" })
        setCountryAssignShow(false)
        setMdShow(false)
    }
    const createUserHandler = async () => {
        // @ts-ignore
        setIsLoading(true)

        const response = await API.post(EXTERNAL_USERS_URL, {
            firstName: uName.current.value,
            email: uEmail.current.value,
            password: uPassword.current.value,


        })
        if (response.status === 200) {

            cogoToast.info('Created!', { position: 'top-right' })
            $('#dataTable').DataTable().ajax.reload();
            customSwal2.fire(
                'User Created!',
                '',
                'success'
            )
        } else {
            customSwal2.fire(
                'Please Try Again!',
                '',
                'error'
            )
            setIsLoading(false)
        }



        uName.current.value = '';
        uEmail.current.value = '';
        uPassword.current.value = '';
        setUserShow(false)
        setIsLoading(false)
    }

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'grading',
            tooltip: 'Role Assignment',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewAssignPermission(rowData.id, rowData.email, rowData.firstName)
            }
        },
        {
            icon: 'map',
            tooltip: 'Country Admin Assignment',
            onClick: (event, rowData) => {
                // Do save operation
                // console.log(rowData)
                viewCountryPermission(rowData.id, rowData.email, rowData.firstName)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'Role Assignment'
        }
    };

    const [selectedLocationOne, setSelectedLocationOne] = useState('');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('');
    const [selectedLocationThree, setSelectedLocationThree] = useState('');
    const [selectedLocationFour, setSelectedLocationFour] = useState('');

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

        setSelectedLocationOne(locationOneId)
        setSelectedLocationTwo(locationTwoId)
        setSelectedLocationThree(locationThreeId)
        setSelectedLocationFour(locationFourId)
    };

    const [individualSelectedRole, setIndividualSelectedRole] = useState({ roles: [], disabledRoles: [] })
    const [previewRoles, setPreviewRoles] = useState([])
    const [expandedAssignments, setExpandedAssignments] = useState({})
    const [filterKey, setFilterKey] = useState(0) // Key to force AllFilterLocation component reset

    // Bulk upload states
    const [excelShow, setExcelShow] = useState(false);
    const [files, setFiles] = useState([]);
    const [progress, setProgress] = useState(0);
    const [jsonShow, setJsonShow] = useState(false);
    const [generatedJsonData, setGeneratedJsonData] = useState(null);
    const [showDownloadJson, setShowDownloadJson] = useState(false);

    const handleFileChange = (file) => {
        setFiles(file)
    }

    // Show JSON format example
    const showJsonFormat = () => {
        setJsonShow(true);
    };

    // Download generated JSON file
    const downloadGeneratedJson = () => {
        if (generatedJsonData) {
            const blob = new Blob([JSON.stringify(generatedJsonData, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `bulk_user_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }
    };

    // Download template function
    const downloadTemplate = () => {
        // Create template data structure based on all available modules and roles
        const templateData = [];

        // Define module structure with their roles
        const moduleStructure = [
            {
                name: 'Group EHS',
                key: 'groupEhs',
                roles: allRoles.groupEhs || []
            },
            {
                name: 'EHS Observation',
                key: 'ehs',
                roles: allRoles.ehs || []
            },
            {
                name: 'ePermit to Work (PTW)',
                key: 'eptw',
                roles: allRoles.eptw || []
            },
            {
                name: 'Incident Reporting',
                key: 'incident',
                roles: allRoles.incident || []
            },
            {
                name: 'Inspection',
                key: 'inspection',
                roles: allRoles.inspection || []
            },
            {
                name: 'Audit',
                key: 'audit',
                roles: allRoles.audit || []
            },
            {
                name: 'Plant and Equipment',
                key: 'plant',
                roles: allRoles.plant || []
            },
            {
                name: 'Report',
                key: 'report',
                roles: allRoles.report || []
            },
            {
                name: 'Other',
                key: 'other',
                roles: allRoles.other || []
            }
        ];

        // Build header rows dynamically
        const headerRow1 = ['', '', '', '', '']; // Location columns (Email, Country, City, Business Unit, Project Name)
        const headerRow2 = ['Email', 'Country', 'City', 'Business Unit', 'Project Name'];
        const merges = [];
        let currentCol = 5;

        // Add module headers and role columns
        moduleStructure.forEach(module => {
            if (module.roles.length > 0) {
                const startCol = currentCol;

                // Add module name to first header row
                headerRow1.push(module.name);
                // Fill remaining columns for this module with empty strings
                for (let i = 1; i < module.roles.length; i++) {
                    headerRow1.push('');
                }

                // Add role names to second header row
                module.roles.forEach(role => {
                    headerRow2.push(role.name);
                });

                // Add merge range for module header
                if (module.roles.length > 1) {
                    merges.push({
                        s: { r: 0, c: startCol },
                        e: { r: 0, c: startCol + module.roles.length - 1 }
                    });
                }

                currentCol += module.roles.length;
            }
        });

        // Instructions row
        const instructionsRow = ['<EMAIL>', 'Philippines', 'Manila', 'Construction Projects', 'Project Delivery'];
        // Add example "yes" values for some roles
        moduleStructure.forEach(module => {
            module.roles.forEach((role, index) => {
                // Add "yes" for first role of each module as example
                instructionsRow.push(index === 0 ? 'yes' : '');
            });
        });

        // Add sample data rows
        const sampleRows = [];
        const sampleUsers = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        sampleUsers.forEach((email, userIndex) => {
            const cities = ['Manila', 'Cebu', 'Davao', 'Quezon City'];
            const row = [email, 'Philippines', cities[userIndex], 'Construction Projects', `Project ${String.fromCharCode(65 + userIndex)}`];
            // Add empty cells for all role columns
            moduleStructure.forEach(module => {
                module.roles.forEach(() => {
                    row.push('');
                });
            });
            sampleRows.push(row);
        });

        // Add empty rows for user input
        for (let i = 0; i < 5; i++) {
            const emptyRow = ['', '', '', '', ''];
            moduleStructure.forEach(module => {
                module.roles.forEach(() => {
                    emptyRow.push('');
                });
            });
            sampleRows.push(emptyRow);
        }

        // Combine all data
        templateData.push(headerRow1);
        templateData.push(headerRow2);
        templateData.push(instructionsRow);
        templateData.push(...sampleRows);

        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(templateData);

        // Set column widths dynamically
        const colWidths = [
            { wch: 25 }, // Email
            { wch: 15 }, // Country
            { wch: 15 }, // City
            { wch: 20 }, // Business Unit
            { wch: 20 }  // Project Name
        ];

        // Add widths for role columns
        moduleStructure.forEach(module => {
            module.roles.forEach(role => {
                colWidths.push({ wch: Math.max(15, role.name.length + 2) });
            });
        });

        ws['!cols'] = colWidths;

        // Set merges for module headers
        ws['!merges'] = merges;

        // Create workbook and add worksheet
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Master User Data Template');

        // Download file
        XLSX.writeFile(wb, 'Master_User_Data_Template.xlsx');
    };

    // Helper function to find user by email
    const findUserByEmail = async (email) => {
        try {
            const response = await API.get(USERS_URL);
            if (response.status === 200) {
                const user = response.data.find(u => u.email.toLowerCase() === email.toLowerCase());
                return user || null;
            }
        } catch (error) {
            console.error('Error finding user by email:', error);
            return null;
        }
        return null;
    };

    // Helper function to find location ID by name
    const findLocationIdByName = (locationName, locationArray) => {
        if (!locationName || locationName.trim() === '') return '';
        const location = locationArray.find(loc =>
            loc.name.toLowerCase() === locationName.toLowerCase()
        );
        return location ? location.id : '';
    };

    // Bulk upload function
    const uploadBulkUserData = async () => {
        setExcelShow(false);
        if (files && files[0]) {
            setIsLoading(true);
            setProgress(0);
            const bulkOperationPayload = {
                operation: "bulk_user_role_assignment",
                timestamp: new Date().toISOString(),
                users: [],
                summary: {
                    totalUsers: 0,
                    processedUsers: 0,
                    successfulUpdates: 0,
                    errors: []
                }
            };

            const reader = new FileReader();
            reader.onload = async (evt) => {
                // Parse data
                const bstr = evt.target.result;
                const wb = XLSX.read(bstr, { type: 'binary' });
                // Get first worksheet
                const wsname = wb.SheetNames[0];
                const ws = wb.Sheets[wsname];
                // Convert array of arrays
                const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
                // Remove the first two rows (headers)
                data.shift(); // Remove first header row
                data.shift(); // Remove second header row

                console.log('=== BULK UPLOAD STARTING ===');
                console.log('Processing bulk user role assignments...');

                // Filter valid rows
                const validRows = data.filter(row => row.length > 0 && row[0]);
                bulkOperationPayload.summary.totalUsers = validRows.length;

                // Process each row and actually update user roles
                for (let index = 0; index < validRows.length; index++) {
                    const row = validRows[index];
                    const email = row[0];
                    console.log(`Processing row ${index + 1}/${validRows.length}: ${email}`);

                    try {
                        const userResult = await processBulkUserData(row, index, validRows.length, true);
                        bulkOperationPayload.users.push(userResult);
                        bulkOperationPayload.summary.processedUsers++;

                        if (userResult.status === 'success') {
                            bulkOperationPayload.summary.successfulUpdates++;
                            console.log(`✅ Successfully updated ${email}`);
                        } else {
                            console.log(`⚠️ Partial failure for ${email}`);
                        }
                    } catch (error) {
                        console.error(`❌ Failed to update ${email}:`, error.message);

                        const errorInfo = {
                            row: index + 1,
                            email: email,
                            error: error.message
                        };
                        bulkOperationPayload.summary.errors.push(errorInfo);

                        // Add failed user to results
                        bulkOperationPayload.users.push({
                            email: email,
                            status: 'failed',
                            error: error.message,
                            processedAt: new Date().toISOString()
                        });
                        bulkOperationPayload.summary.processedUsers++;
                    }

                    // Update progress
                    const progressPercent = ((index + 1) / validRows.length) * 100;
                    setProgress(progressPercent);

                    // Small delay to prevent overwhelming the API
                    if (index < validRows.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                }

                // Store the generated JSON data
                setGeneratedJsonData(bulkOperationPayload);
                setShowDownloadJson(true);

                console.log('=== BULK UPLOAD COMPLETE ===');
                console.log(`Total: ${bulkOperationPayload.summary.totalUsers}`);
                console.log(`Successful: ${bulkOperationPayload.summary.successfulUpdates}`);
                console.log(`Errors: ${bulkOperationPayload.summary.errors.length}`);
                console.log('=====================================');

                // Show completion message
                const { successfulUpdates, errors } = bulkOperationPayload.summary;
                if (errors.length === 0) {
                    cogoToast.success(`Successfully updated ${successfulUpdates} users!`);
                } else if (successfulUpdates > 0) {
                    cogoToast.warn(`Updated ${successfulUpdates} users with ${errors.length} errors. Check JSON download for details.`);
                } else {
                    cogoToast.error(`Failed to update users. ${errors.length} errors occurred. Check JSON download for details.`);
                }

                setIsLoading(false);
                setProgress(0);
            };
            reader.readAsBinaryString(files[0]);
        } else {
            setIsLoading(false);
        }
        getUsersData();
    };

    const processBulkUserData = async (row, index, total, actuallyUpdate = false) => {
        // Extract basic info from the row (now includes City)
        const [email, country, city, businessUnit, projectName, ...roleValues] = row;

        if (!email || email.trim() === '') {
            console.log('Skipping row with no email');
            throw new Error('Email is required');
        }

        // Build role assignments dynamically based on the template structure
        const roleAssignments = {};
        let roleIndex = 0;

        // Define the same module structure as in downloadTemplate
        const moduleStructure = [
            { name: 'Group EHS', key: 'groupEhs', roles: allRoles.groupEhs || [] },
            { name: 'EHS Observation', key: 'ehs', roles: allRoles.ehs || [] },
            { name: 'ePermit to Work (PTW)', key: 'eptw', roles: allRoles.eptw || [] },
            { name: 'Incident Reporting', key: 'incident', roles: allRoles.incident || [] },
            { name: 'Inspection', key: 'inspection', roles: allRoles.inspection || [] },
            { name: 'Audit', key: 'audit', roles: allRoles.audit || [] },
            { name: 'Plant and Equipment', key: 'plant', roles: allRoles.plant || [] },
            { name: 'Report', key: 'report', roles: allRoles.report || [] },
            { name: 'Other', key: 'other', roles: allRoles.other || [] }
        ];

        // Map role values to assignments
        moduleStructure.forEach(module => {
            if (module.roles.length > 0) {
                roleAssignments[module.key] = {};
                module.roles.forEach(role => {
                    const value = roleValues[roleIndex];
                    roleAssignments[module.key][role.id] = value?.toLowerCase() === 'yes';
                    roleIndex++;
                });
            }
        });

        console.log('Processing user data:', {
            email, country, city, businessUnit, projectName,
            roleAssignments
        });

        try {
            if (actuallyUpdate) {
                // Actually update the user roles via API
                const userResult = await updateUserRoleAssignment(email, roleAssignments, {
                    country, city, businessUnit, projectName
                });
                return userResult;
            } else {
                // Generate JSON data for this user (simulation mode)
                const userJsonData = await simulateUserRoleAssignment(email, roleAssignments, {
                    country, city, businessUnit, projectName
                });
                return userJsonData;
            }

        } catch (error) {
            console.error('Error processing user data for', email, ':', error);
            if (actuallyUpdate) {
                // Don't show toast for each error during bulk update
                console.error(`Error updating ${email}: ${error.message}`);
            } else {
                cogoToast.error(`Error processing ${email}: ${error.message}`);
            }
            throw error;
        }
    };

    const simulateUserRoleAssignment = async (email, roleAssignments, location) => {
        // Build the JSON payload that would be sent to the API
        const assignedRoleIds = [];
        const customRoles = {
            country: [],
            ehs: [],
            eptw: [],
            incident: [],
            inspection: [],
            audit: [],
            plant: [],
            groupEhs: [],
            report: [],
            other: []
        };

        // Extract assigned role IDs from roleAssignments
        Object.keys(roleAssignments).forEach(moduleKey => {
            const moduleRoles = roleAssignments[moduleKey];
            Object.keys(moduleRoles).forEach(roleId => {
                if (moduleRoles[roleId] === true) {
                    assignedRoleIds.push(roleId);
                    if (customRoles[moduleKey]) {
                        customRoles[moduleKey].push(roleId);
                    }
                }
            });
        });

        // JSON payload for user location roles API
        const userLocationRolePayload = {
            userId: "USER_ID_TO_BE_FOUND", // Would be found by email lookup
            roles: assignedRoleIds,
            locations: {
                locationOne: "LOCATION_ONE_ID", // Would be found by location name lookup
                locationTwo: "LOCATION_TWO_ID", // Would be found by location name lookup
                locationThree: "LOCATION_THREE_ID", // Would be found by location name lookup
                locationFour: "LOCATION_FOUR_ID" // Would be found by location name lookup
            }
        };

        // JSON payload for user custom roles API
        const userCustomRolesPayload = {
            email: email,
            customRoles: customRoles
        };

        // Complete user data structure
        const userJsonData = {
            email: email,
            location: {
                country: location.country,
                city: location.city,
                businessUnit: location.businessUnit,
                projectName: location.projectName
            },
            roleAssignments: roleAssignments,
            apiPayloads: {
                userLocationRoles: userLocationRolePayload,
                userCustomRoles: userCustomRolesPayload
            },
            assignedRoleIds: assignedRoleIds,
            processedAt: new Date().toISOString(),
            status: "processed"
        };

        // Log the JSON payloads that would be sent to APIs
        console.log(`JSON Payload for ${email}:`);
        console.log('1. User Location Role Assignment (POST to INDIVIDUAL_USER_LOCATION_ROLE_URL):');
        console.log(JSON.stringify(userLocationRolePayload, null, 2));
        console.log('2. User Custom Roles Assignment (PATCH to USERS_URL_WITH_ID):');
        console.log(JSON.stringify(userCustomRolesPayload, null, 2));
        console.log('3. Location Mapping:');
        console.log(JSON.stringify({
            country: location.country,
            city: location.city,
            businessUnit: location.businessUnit,
            projectName: location.projectName,
            note: "These would be mapped to actual location IDs via API lookups"
        }, null, 2));
        console.log('---');

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 100));

        return userJsonData;
    };

    const updateUserRoleAssignment = async (email, roleAssignments, location) => {
        // Find user by email
        const user = await findUserByEmail(email);
        if (!user) {
            throw new Error(`User not found with email: ${email}`);
        }

        // Find location IDs by names
        const locationOneId = findLocationIdByName(location.country, country);
        const locationTwoId = findLocationIdByName(location.city, locationTwo);
        const locationThreeId = findLocationIdByName(location.businessUnit, locationThree);
        const locationFourId = findLocationIdByName(location.projectName, locationFour);

        // Build the role assignments
        const assignedRoleIds = [];
        const customRoles = {
            country: [],
            ehs: [],
            eptw: [],
            incident: [],
            inspection: [],
            audit: [],
            plant: [],
            groupEhs: [],
            report: [],
            other: []
        };

        // Extract assigned role IDs from roleAssignments
        Object.keys(roleAssignments).forEach(moduleKey => {
            const moduleRoles = roleAssignments[moduleKey];
            Object.keys(moduleRoles).forEach(roleId => {
                if (moduleRoles[roleId] === true) {
                    assignedRoleIds.push(roleId);
                    if (customRoles[moduleKey]) {
                        customRoles[moduleKey].push(roleId);
                    }
                }
            });
        });

        console.log(`Updating user ${email} (ID: ${user.id}) with roles:`, assignedRoleIds);

        try {
            // Make the same API calls as individual assignment
            let locationRoleSuccess = false;
            let customRoleSuccess = false;

            // 1. Update location roles
            const locationRolePayload = {
                userId: user.id,
                roles: assignedRoleIds,
                locations: {
                    locationOne: locationOneId,
                    locationTwo: locationTwoId,
                    locationThree: locationThreeId,
                    locationFour: locationFourId
                }
            };

            const response1 = await API.post(INDIVIDUAL_USER_LOCATION_ROLE_URL, locationRolePayload);
            if (response1.status === 200) {
                locationRoleSuccess = true;
                console.log(`✓ Location roles updated for ${email}`);
            }

            // 2. Update custom roles
            const customRolePayload = {
                email: email,
                customRoles: customRoles
            };

            const response2 = await API.patch(USERS_URL_WITH_ID(user.id), customRolePayload);
            if (response2.status === 204) {
                customRoleSuccess = true;
                console.log(`✓ Custom roles updated for ${email}`);
            }

            // Build result object
            const result = {
                email: email,
                userId: user.id,
                location: {
                    country: location.country,
                    city: location.city,
                    businessUnit: location.businessUnit,
                    projectName: location.projectName
                },
                locationIds: {
                    locationOne: locationOneId,
                    locationTwo: locationTwoId,
                    locationThree: locationThreeId,
                    locationFour: locationFourId
                },
                roleAssignments: roleAssignments,
                assignedRoleIds: assignedRoleIds,
                customRoles: customRoles,
                apiResults: {
                    locationRoles: {
                        success: locationRoleSuccess,
                        status: response1.status,
                        payload: locationRolePayload
                    },
                    customRoles: {
                        success: customRoleSuccess,
                        status: response2.status,
                        payload: customRolePayload
                    }
                },
                processedAt: new Date().toISOString(),
                status: (locationRoleSuccess && customRoleSuccess) ? 'success' : 'partial_failure'
            };

            if (!locationRoleSuccess || !customRoleSuccess) {
                throw new Error(`Failed to update all roles for ${email}. Location roles: ${locationRoleSuccess}, Custom roles: ${customRoleSuccess}`);
            }

            return result;

        } catch (error) {
            console.error(`Error updating roles for ${email}:`, error);
            throw new Error(`Failed to update roles for ${email}: ${error.message}`);
        }
    };

    const getRoleNameById = useCallback((roleId) => {
        // Search each category in allRoles for the roleId
        let roleName = 'Unknown Role'; // Default if not found
        Object.values(allRoles).forEach(category => {
            const role = category.find(role => role.id === roleId);
            if (role) {
                roleName = role.name;
                return;
            }
        });
        return roleName;
    }, [allRoles]);

    const getLocationNames = useCallback(() => {
        let locationNames = []

        const getLocationName = (id, locationArray, allText) => {
            if (id === "") return ""
            if (id && id.endsWith("-all")) return allText
            const location = locationArray.find(location => location.id === id)
            return location ? location.name : 'Unknown'
        }

        locationNames.push(getLocationName(selectedLocationOne, country, 'All Country'))
        locationNames.push(getLocationName(selectedLocationTwo, locationTwo, 'All City'))
        locationNames.push(getLocationName(selectedLocationThree, locationThree, 'All Business Unit'))
        locationNames.push(getLocationName(selectedLocationFour, locationFour, 'All Projects / DC'))

        return locationNames.filter(name => name && name !== 'Unknown').join(' > ')
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, country, locationTwo, locationThree, locationFour])

    const groupRolesByModule = useCallback((roleIds) => {
        const modules = {
            'Group EHS': [],
            'EHS Observation': [],
            'ePermit to Work': [],
            'Incident Reporting': [],
            'Inspection': [],
            'Audit': [],
            'Plant and Equipment': [],
            'Report': [],
            'Other': []
        }

        roleIds.forEach(roleId => {
            // Find which module this role belongs to
            if (allRoles.groupEhs.find(role => role.id === roleId)) {
                modules['Group EHS'].push(getRoleNameById(roleId))
            } else if (allRoles.ehs.find(role => role.id === roleId)) {
                modules['EHS Observation'].push(getRoleNameById(roleId))
            } else if (allRoles.eptw.find(role => role.id === roleId)) {
                modules['ePermit to Work'].push(getRoleNameById(roleId))
            } else if (allRoles.incident.find(role => role.id === roleId)) {
                modules['Incident Reporting'].push(getRoleNameById(roleId))
            } else if (allRoles.inspection.find(role => role.id === roleId)) {
                modules['Inspection'].push(getRoleNameById(roleId))
            } else if (allRoles.audit.find(role => role.id === roleId)) {
                modules['Audit'].push(getRoleNameById(roleId))
            } else if (allRoles.plant.find(role => role.id === roleId)) {
                modules['Plant and Equipment'].push(getRoleNameById(roleId))
            } else if (allRoles.report.find(role => role.id === roleId)) {
                modules['Report'].push(getRoleNameById(roleId))
            } else if (allRoles.other.find(role => role.id === roleId)) {
                modules['Other'].push(getRoleNameById(roleId))
            }
        })

        // Filter out empty modules
        return Object.fromEntries(Object.entries(modules).filter(([key, value]) => value.length > 0))
    }, [allRoles, getRoleNameById])

    const updatePreviewRoles = useCallback(() => {
        if (!individualSelectedRole.roles || individualSelectedRole.roles.length === 0) {
            setPreviewRoles([])
            return
        }

        const locationNames = getLocationNames()
        const rolesByModule = groupRolesByModule(individualSelectedRole.roles)

        setPreviewRoles([{
            locationPath: locationNames,
            rolesByModule: rolesByModule
        }])
    }, [individualSelectedRole.roles, getLocationNames, groupRolesByModule])

    const getIndividualRoles = useCallback(async () => {
        const response = await API.post(GET_INDIVIDUAL_USER_LOCATION_ROLE_URL, { userId: selectedUserId.id, locations: { locationOne: selectedLocationOne, locationTwo: selectedLocationTwo, locationThree: selectedLocationThree, locationFour: selectedLocationFour } });
        if (response.status === 200) {
            if (response.data && response.data.length > 0)
                setIndividualSelectedRole(response.data[0])
            else
                setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }, [selectedUserId.id, selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour])

    const toggleAssignmentAccordion = (index) => {
        setExpandedAssignments(prev => ({
            ...prev,
            [index]: !prev[index]
        }))
    }

    const clearFormStates = () => {
        // Clear role selection states
        setIndividualSelectedRole({ roles: [], disabledRoles: [] });
        setPreviewRoles([]);

        // Reset location filter selections
        setSelectedLocationOne('');
        setSelectedLocationTwo('');
        setSelectedLocationThree('');
        setSelectedLocationFour('');

        // Reset accordion states
        setExpandedAssignments({});

        // Force AllFilterLocation component to reset by changing key
        setFilterKey(prev => prev + 1);
    }

    useEffect(() => {
        if (selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour)
            getIndividualRoles()
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, getIndividualRoles])

    // Real-time preview update when roles change
    useEffect(() => {
        updatePreviewRoles()
    }, [updatePreviewRoles])

    const resetUserAssignment = async (id) => {
        const response = await API.delete(USER_LOCATION_ROLE_WITH_ID_URL(id));
        if (response.status === 204) {
            cogoToast.info('User Assignment Reset Success!', { position: 'top-right' })
            setIndividualSelectedRole({ roles: [], disabledRoles: [] })
        }
    }

    const updateUserData = async (column, id, value, email) => {

        const response = await API.patch(USERS_URL_WITH_ID(id), { [column]: value })



        if (response.status === 204) {
            cogoToast.success('Updated!')
        }
    }

    return (
        <CardOverlay>
            {progress > 0 && progress < 100 && <LinearProgress className="mb-3" variant="determinate" value={progress} />}

            <div className="mb-3">
                {/* <button className='btn btn-primary me-2' onClick={() => setUserShow(true)}>Create User</button> */}
                <button className='btn btn-success me-2' onClick={downloadTemplate}>
                    <i className="mdi mdi-download me-2"></i>
                    Download Template
                </button>
                <button className='btn btn-danger me-2' onClick={() => setExcelShow(true)}>
                    <i className="mdi mdi-upload me-2"></i>
                    Bulk Update User Roles
                </button>

                {showDownloadJson && (
                    <button className='btn btn-warning' onClick={downloadGeneratedJson}>
                        <i className="mdi mdi-download me-2"></i>
                        Download Results JSON
                    </button>
                )}
            </div>

            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={userColumns}
                    data={data}
                    title="Master User Data"
                    style={tableStyle}
                    actions={tableActions}
                    options={tableOptions}
                    localization={localization}
                    editable={{
                        onRowUpdate: (newData, oldData) =>
                            new Promise((resolve, reject) => {
                                // Find the index of the row in the data array
                                const dataUpdate = [...data];
                                const index = oldData.tableData.id;
                                dataUpdate[index] = newData;

                                // If type of data has changed update the company
                                if (newData.type !== oldData.type) {
                                    updateUserData('company', oldData.id, newData.type, oldData.email);
                                }

                                // Loop over each field in the newData object to update the user data
                                Object.keys(newData).forEach((field) => {
                                    if (newData[field] !== oldData[field]) {
                                        updateUserData(field, oldData.id, newData[field], oldData.email);
                                    }
                                });

                                // On successful update, change the local state
                                setData(dataUpdate);

                                resolve();
                            }),
                    }}
                />
            </ThemeProvider>
            <Modal
                show={mdShow}
                size="lg"
                onHide={() => setMdShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
                className='extra-large role-assignment-modal'
                backdrop="static" // Prevents closing on outside click
                keyboard={false} // Prevents closing on Escape key press
            >
                <Modal.Header className="role-assignment-header">
                    <div className='w-100 d-flex align-items-center justify-content-between'>
                        <div className="header-info">
                            <h4 className='mb-1 modal-title'>Role Assignment</h4>
                            <div className="user-info">
                                <span className="user-name">{selectedUserId.name}</span>
                                <span className="user-email text-muted">({selectedUserId.email})</span>
                            </div>
                        </div>
                        <div className="header-actions">
                            <Button variant="outline-secondary" size="sm" onClick={(e) => resetUserAssignment(selectedUserId.id)}>
                                <i className="mdi mdi-refresh me-1"></i>Reset Assignment
                            </Button>
                            <Button variant="outline-secondary" size="sm" onClick={handleAssignClose} className="ms-2">
                                <i className="mdi mdi-close"></i>
                            </Button>
                        </div>
                    </div>
                </Modal.Header>

                <Modal.Body className="assign-permissions-modal p-0">
                    <div className="modal-content-wrapper">
                        <div className='row g-0 h-100'>
                            <div className='col-lg-7 permissions-form-section'>
                                <div className="permissions-form-container">
                                    <div className="section-header">
                                        <h5 className="section-title">
                                            <i className="mdi mdi-map-marker-outline me-2 text-primary"></i>
                                            Select Location & Permissions
                                        </h5>
                                        <p className="section-subtitle text-muted">Choose location hierarchy and assign appropriate roles</p>
                                    </div>

                                    <div className="location-filter-section">
                                        <AllFilterLocation key={filterKey} handleFilter={handleFilter} disableAll={true} period={false} countryRoles={selectedRoles.country || []} />
                                    </div>

                                    {((selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && individualSelectedRole.roles) && (
                                        <div className="roles-section">
                                            <div className="roles-header mb-3">
                                                <h6 className="roles-title">
                                                    <i className="mdi mdi-account-key me-2 text-success"></i>
                                                    Available Roles
                                                </h6>
                                                <small className="text-muted">Select roles to assign for the chosen location</small>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-shield-account me-2"></i>
                                                        Group EHS
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.groupEhs.slice()
                                                            .sort((a, b) => {
                                                                const nameA = a.name.toLowerCase();
                                                                const nameB = b.name.toLowerCase();
                                                                if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                                    return -1;
                                                                }
                                                                if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                                    return 1;
                                                                }
                                                                return 0;
                                                            }).map((i, k) => {
                                                                return (
                                                                    <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                        <label className="role-checkbox-label">
                                                                            <input
                                                                                value={i.id}
                                                                                checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                onChange={(e) => handleRoleChange(e, 'groupEhs')}
                                                                                type='checkbox'
                                                                                className="role-checkbox"
                                                                            />
                                                                            <span className="checkmark"></span>
                                                                            <span className="role-name">{i.name}</span>
                                                                            {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                                <span className="role-status">Already assigned</span>
                                                                            )}
                                                                        </label>
                                                                    </div>
                                                                )
                                                            })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-eye me-2"></i>
                                                        EHS Observation
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.ehs.slice()
                                                            .sort((a, b) => {
                                                                const nameA = a.name.toLowerCase();
                                                                const nameB = b.name.toLowerCase();
                                                                if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                                    return -1;
                                                                }
                                                                if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                                    return 1;
                                                                }
                                                                return 0;
                                                            }).map((i, k) => {
                                                                return (
                                                                    <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                        <label className="role-checkbox-label">
                                                                            <input
                                                                                value={i.id}
                                                                                checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                onChange={(e) => handleRoleChange(e, 'ehs')}
                                                                                type='checkbox'
                                                                                className="role-checkbox"
                                                                            />
                                                                            <span className="checkmark"></span>
                                                                            <span className="role-name">{i.name}</span>
                                                                            {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                                <span className="role-status">Already assigned</span>
                                                                            )}
                                                                        </label>
                                                                    </div>
                                                                )
                                                            })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-file-document-edit me-2"></i>
                                                        ePermit to Work
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.eptw.slice()
                                                            .sort((a, b) => {
                                                                const nameA = a.name.toLowerCase();
                                                                const nameB = b.name.toLowerCase();
                                                                if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                                    return -1;
                                                                }
                                                                if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                                    return 1;
                                                                }
                                                                return 0;
                                                            }).map((i, k) => {
                                                                return (
                                                                    <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                        <label className="role-checkbox-label">
                                                                            <input
                                                                                value={i.id}
                                                                                checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                onChange={(e) => handleRoleChange(e, 'eptw')}
                                                                                type='checkbox'
                                                                                className="role-checkbox"
                                                                            />
                                                                            <span className="checkmark"></span>
                                                                            <span className="role-name">{i.name}</span>
                                                                            {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                                <span className="role-status">Already assigned</span>
                                                                            )}
                                                                        </label>
                                                                    </div>
                                                                )
                                                            })
                                                    }
                                                </div>
                                            </div>
                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-alert-circle me-2"></i>
                                                        Incident Reporting
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.incident.slice()
                                                            .sort((a, b) => {
                                                                const nameA = a.name.toLowerCase();
                                                                const nameB = b.name.toLowerCase();
                                                                if (nameA.includes('view only') && !nameB.includes('view only')) {
                                                                    return -1;
                                                                }
                                                                if (!nameA.includes('view only') && nameB.includes('view only')) {
                                                                    return 1;
                                                                }
                                                                return 0;
                                                            }).map((i, k) => {
                                                                return (
                                                                    <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                        <label className="role-checkbox-label">
                                                                            <input
                                                                                value={i.id}
                                                                                checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                                onChange={(e) => handleRoleChange(e, 'incident')}
                                                                                type='checkbox'
                                                                                className="role-checkbox"
                                                                            />
                                                                            <span className="checkmark"></span>
                                                                            <span className="role-name">{i.name}</span>
                                                                            {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                                <span className="role-status">Already assigned</span>
                                                                            )}
                                                                        </label>
                                                                    </div>
                                                                )
                                                            })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-clipboard-check me-2"></i>
                                                        Inspection
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.inspection.map((i, k) => {
                                                            return (
                                                                <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                    <label className="role-checkbox-label">
                                                                        <input
                                                                            value={i.id}
                                                                            checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            onChange={(e) => handleRoleChange(e, 'inspection')}
                                                                            type='checkbox'
                                                                            className="role-checkbox"
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="role-name">{i.name}</span>
                                                                        {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                            <span className="role-status">Already assigned</span>
                                                                        )}
                                                                    </label>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-file-check me-2"></i>
                                                        Audit
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.audit.map((i, k) => {
                                                            return (
                                                                <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                    <label className="role-checkbox-label">
                                                                        <input
                                                                            value={i.id}
                                                                            checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            onChange={(e) => handleRoleChange(e, 'audit')}
                                                                            type='checkbox'
                                                                            className="role-checkbox"
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="role-name">{i.name}</span>
                                                                        {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                            <span className="role-status">Already assigned</span>
                                                                        )}
                                                                    </label>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-factory me-2"></i>
                                                        Plant and Equipment
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.plant.map((i, k) => {
                                                            return (
                                                                <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                    <label className="role-checkbox-label">
                                                                        <input
                                                                            value={i.id}
                                                                            checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            onChange={(e) => handleRoleChange(e, 'plant')}
                                                                            type='checkbox'
                                                                            className="role-checkbox"
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="role-name">{i.name}</span>
                                                                        {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                            <span className="role-status">Already assigned</span>
                                                                        )}
                                                                    </label>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-chart-line me-2"></i>
                                                        Report
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.report.map((i, k) => {
                                                            return (
                                                                <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                    <label className="role-checkbox-label">
                                                                        <input
                                                                            value={i.id}
                                                                            checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            onChange={(e) => handleRoleChange(e, 'report')}
                                                                            type='checkbox'
                                                                            className="role-checkbox"
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="role-name">{i.name}</span>
                                                                        {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                            <span className="role-status">Already assigned</span>
                                                                        )}
                                                                    </label>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </div>
                                            </div>

                                            <div className="module-group">
                                                <div className="module-header">
                                                    <h6 className="module-title">
                                                        <i className="mdi mdi-dots-horizontal me-2"></i>
                                                        Other
                                                    </h6>
                                                </div>
                                                <div className='role-checkboxes-grid'>
                                                    {
                                                        allRoles.other.map((i, k) => {
                                                            return (
                                                                <div key={k} className={`role-checkbox-item ${individualSelectedRole.disabledRoles.includes(i.id) ? 'disabled' : ''}`}>
                                                                    <label className="role-checkbox-label">
                                                                        <input
                                                                            value={i.id}
                                                                            checked={individualSelectedRole.roles.includes(i.id) || individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            disabled={individualSelectedRole.disabledRoles.includes(i.id)}
                                                                            onChange={(e) => handleRoleChange(e, 'other')}
                                                                            type='checkbox'
                                                                            className="role-checkbox"
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="role-name">{i.name}</span>
                                                                        {individualSelectedRole.disabledRoles.includes(i.id) && (
                                                                            <span className="role-status">Already assigned</span>
                                                                        )}
                                                                    </label>
                                                                </div>
                                                            )
                                                        })
                                                    }
                                                </div>
                                            </div>


                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className='col-lg-5 preview-section'>
                                <div className='preview-container h-100'>
                                    <div className="section-header">
                                        <h5 className="section-title">
                                            <i className="mdi mdi-account-check me-2 text-success"></i>
                                            List of Assigned Roles
                                        </h5>
                                        <p className="section-subtitle text-muted">Preview and manage role assignments</p>
                                    </div>
                                    <div className="preview-content">
                                        {/* Real-time preview for current selection */}
                                        {previewRoles.length > 0 && (
                                            <div className="current-selection mb-4">
                                                <h6 className="preview-subtitle">Current Selection:</h6>
                                                {previewRoles.map((preview, index) => (
                                                    <div key={index} className="preview-item">
                                                        <div className="location-path mb-2">
                                                            <i className="mdi mdi-map-marker text-primary me-2"></i>
                                                            <strong>{preview.locationPath}</strong>
                                                        </div>
                                                        <div className="roles-by-module">
                                                            {Object.entries(preview.rolesByModule).map(([module, roles]) => (
                                                                <div key={module} className="module-preview mb-3">
                                                                    <div className="module-preview-title">
                                                                        <i className="mdi mdi-folder-outline text-info me-2"></i>
                                                                        {module}
                                                                    </div>
                                                                    <ul className="role-list">
                                                                        {roles.map((role, roleIndex) => (
                                                                            <li key={roleIndex} className="role-item">
                                                                                <i className="mdi mdi-check-circle text-success me-2"></i>
                                                                                {role}
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        {/* Separator between current and existing */}
                                        {previewRoles.length > 0 && allLocationRoles.length > 0 && (
                                            <div className="assignments-separator">
                                                <hr className="separator-line" />
                                            </div>
                                        )}

                                        {/* Existing assigned roles */}
                                        {allLocationRoles.length > 0 && (
                                            <div className="existing-assignments">
                                                <h6 className="preview-subtitle">
                                                    Existing Assignments:
                                                    <span className="assignment-count">({allLocationRoles.length})</span>
                                                    <small className="expand-hint">Click to expand</small>
                                                </h6>
                                                {allLocationRoles.map((locationRole, index) => {
                                                    let locationNames = [];
                                                    const existingRolesByModule = groupRolesByModule(locationRole.roles);

                                                    const getLocationName = (id, locationArray, allText) => {
                                                        if (id === "") return "";
                                                        if (id.endsWith("-all")) return allText;
                                                        const location = locationArray.find(location => location.id === id);
                                                        return location ? location.name : 'Unknown';
                                                    };

                                                    locationNames.push(getLocationName(locationRole.locationOneId, country, 'All Country'));
                                                    locationNames.push(getLocationName(locationRole.locationTwoId, locationTwo, 'All City'));
                                                    locationNames.push(getLocationName(locationRole.locationThreeId, locationThree, 'All Business Unit'));
                                                    locationNames.push(getLocationName(locationRole.locationFourId, locationFour, 'All Projects / DC'));

                                                    locationNames = locationNames.filter(name => name && name !== 'Unknown');

                                                    return (
                                                        <div key={index} className="existing-assignment-item mb-3">
                                                            <div
                                                                className="location-path-header"
                                                                onClick={() => toggleAssignmentAccordion(index)}
                                                                style={{ cursor: 'pointer' }}
                                                            >
                                                                <div className="location-path">
                                                                    <i className="mdi mdi-map-marker me-2"></i>
                                                                    <strong>{locationNames.join(' > ')}</strong>
                                                                </div>
                                                                <div className="accordion-toggle">
                                                                    <i className={`mdi ${expandedAssignments[index] ? 'mdi-chevron-up' : 'mdi-chevron-down'} accordion-icon`}></i>
                                                                </div>
                                                            </div>

                                                            {expandedAssignments[index] && (
                                                                <div className="roles-by-module accordion-content">
                                                                    {Object.entries(existingRolesByModule).map(([module, roles]) => (
                                                                        <div key={module} className="module-preview mb-3">
                                                                            <div className="module-preview-title">
                                                                                <i className="mdi mdi-folder-outline me-2"></i>
                                                                                {module}
                                                                            </div>
                                                                            <ul className="role-list">
                                                                                {roles.map((role, roleIndex) => (
                                                                                    <li key={roleIndex} className="role-item">
                                                                                        <i className="mdi mdi-check-circle me-2"></i>
                                                                                        {role}
                                                                                    </li>
                                                                                ))}
                                                                            </ul>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )}

                                        {/* Empty state */}
                                        {previewRoles.length === 0 && allLocationRoles.length === 0 && (
                                            <div className="empty-state text-center py-5">
                                                <i className="mdi mdi-account-plus text-muted" style={{ fontSize: '3rem' }}></i>
                                                <p className="text-muted mt-3">Select a location and roles to see the preview here</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Fixed Footer */}
                        <div className="modal-fixed-footer">
                            <div className="footer-actions">
                                <Button variant="outline-secondary" onClick={handleAssignClose}>
                                    <i className="mdi mdi-close me-1"></i>Cancel
                                </Button>
                                {selectedUserId.id && (
                                    <Button variant="primary" onClick={handleAssignSubmit} className="ms-2">
                                        <i className="mdi mdi-check me-1"></i>Assign Roles
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>

                </Modal.Body>
            </Modal>

            <Modal
                show={countryAssignShow}
                size="md"
                onHide={() => setCountryAssignShow(false)}
                aria-labelledby="example-modal-sizes-title-md"

                backdrop="static" // Prevents closing on outside click
                keyboard={false} // Prevents closing on Escape key press
            >
                <Modal.Header>
                    <div className='w-100 d-flex align-items-center justify-content-between'>
                        <h4 className='mb-0'>
                            Assign Permissions to {selectedUserId.name}
                        </h4>

                    </div>

                </Modal.Header>

                <Modal.Body>

                    <div className='row'>
                        <div className='col-sm-12'>
                            <form className="forms">

                                <h4>Country Admin</h4>
                                <div className='form-group mb-2'>

                                    {
                                        allRoles.country.map((i, k) => {
                                            return (
                                                <label className='me-3' key={k}>
                                                    <input value={i.id} checked={selectedRoles.country.includes(i.id)} onChange={(e) => handleRoleChange(e, 'country')} type='checkbox' /> {i.name}
                                                </label>
                                            )
                                        })
                                    }

                                </div>


                            </form>
                        </div>

                    </div>

                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    <>
                        <Button variant="light" onClick={handleAssignClose}>Close</Button>

                        {selectedUserId.id && <Button variant="primary" onClick={handleCountrySubmit}>Assign</Button>}

                    </>


                </Modal.Footer>
            </Modal>

            <Modal
                show={userShow}
                onHide={() => setUserShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >

                <Modal.Body>
                    <form className="forms">
                        <div className="form-group">
                            <label htmlFor="user_name" >Name</label>
                            <Form.Control type="text" ref={uName} id="user_name" placeholder="Enter User Name" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_category" >Email</label>
                            <Form.Control type="email" ref={uEmail} id="user_category" placeholder="Enter User Email" />
                        </div>

                        <div className="form-group">
                            <label htmlFor="user_description" >Temporary Password</label>
                            <Form.Control type="password" ref={uPassword} id="user_description" placeholder="Enter Password" />
                        </div>



                    </form>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setUserShow(false)}>Cancel</Button>
                                <Button variant="primary" onClick={createUserHandler}>Create</Button>
                            </>
                        )
                    }

                </Modal.Footer>
            </Modal>

            <Modal
                show={excelShow}
                onHide={() => setExcelShow(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                <Modal.Header>
                    <h4>Upload Master User Data</h4>
                </Modal.Header>
                <Modal.Body>
                    <div className="mb-4">
                        <div className="alert alert-warning">
                            <strong>⚠️ Important:</strong> This will actually update user roles in the system. Make sure your data is correct before uploading.
                        </div>
                        <h6>Instructions:</h6>
                        <ol className="small">
                            <li>Download the template below to get the correct format with all available modules and roles</li>
                            <li>Fill in user emails and location information (Country, City, Business Unit, Project Name)</li>
                            <li>Use "yes" in role columns to assign specific roles to users</li>
                            <li>Leave role columns empty or use any other text to skip role assignment</li>
                            <li>The template includes all modules: Group EHS, EHS Observation, ePermit to Work, Incident Reporting, Inspection, Audit, Plant and Equipment, and Report</li>
                            <li>Save the file and upload it back using the upload area below</li>
                            <li><strong>The upload will immediately update user roles in the database</strong></li>
                            <li><strong>After upload, you can download a JSON report with the results</strong></li>
                            <li><strong>Check browser console during upload to see detailed processing logs</strong></li>
                        </ol>
                        <Button variant="outline-primary" onClick={downloadTemplate} className="mb-3">
                            <i className="mdi mdi-download me-2"></i>
                            Download Master User Data Template
                        </Button>
                    </div>
                    <div className="mb-3">
                        <h6>Upload Completed Template:</h6>
                        <DropzoneArea
                            acceptedFiles={[
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            ]}
                            dropzoneText={"Drag and drop the completed Excel template here or click to browse"}
                            filesLimit={1}
                            onChange={handleFileChange}
                        />
                    </div>
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {
                        isLoading ? <Loader /> : (
                            <>
                                <Button variant="light" onClick={() => setExcelShow(false)}>Cancel</Button>
                                <Button variant="danger" onClick={uploadBulkUserData}>
                                    <i className="mdi mdi-upload me-2"></i>
                                    Update User Roles
                                </Button>
                            </>
                        )
                    }
                </Modal.Footer>
            </Modal>

            <Modal
                show={jsonShow}
                onHide={() => setJsonShow(false)}
                size="lg"
                aria-labelledby="json-format-modal"
            >
                <Modal.Header>
                    <h4>Bulk Upload API Information</h4>
                </Modal.Header>
                <Modal.Body>
                    <div className="alert alert-success">
                        <strong>✅ Live API Integration:</strong> The bulk upload now makes actual API calls to update user roles in real-time.
                    </div>

                    <div className="mb-4">
                        <h6>1. Individual User Location Role Assignment:</h6>
                        <p className="small text-muted">POST to: <code>INDIVIDUAL_USER_LOCATION_ROLE_URL</code></p>
                        <pre className="bg-light p-3 rounded small">
                            {`{
  "userId": "user_id_found_by_email",
  "roles": ["role_id_1", "role_id_2", "role_id_3"],
  "locations": {
    "locationOne": "country_location_id",
    "locationTwo": "city_location_id",
    "locationThree": "business_unit_location_id",
    "locationFour": "project_location_id"
  }
}`}
                        </pre>
                    </div>

                    <div className="mb-4">
                        <h6>2. User Custom Roles Assignment:</h6>
                        <p className="small text-muted">PATCH to: <code>USERS_URL_WITH_ID(userId)</code></p>
                        <pre className="bg-light p-3 rounded small">
                            {`{
  "email": "<EMAIL>",
  "customRoles": {
    "country": [],
    "ehs": ["ehs_role_id_1", "ehs_role_id_2"],
    "eptw": ["eptw_role_id_1"],
    "incident": ["incident_role_id_1"],
    "inspection": [],
    "audit": ["audit_role_id_1"],
    "plant": [],
    "groupEhs": ["group_ehs_role_id_1"],
    "report": ["report_role_id_1"]
  }
}`}
                        </pre>
                    </div>

                    <div className="mb-4">
                        <h6>3. Results JSON Structure:</h6>
                        <p className="small text-muted">Downloaded after bulk upload completion</p>
                        <pre className="bg-light p-3 rounded small">
                            {`{
  "operation": "bulk_user_role_assignment",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "users": [
    {
      "email": "<EMAIL>",
      "userId": "actual_user_id",
      "status": "success",
      "location": {...},
      "locationIds": {...},
      "assignedRoleIds": [...],
      "apiResults": {
        "locationRoles": { "success": true, "status": 200 },
        "customRoles": { "success": true, "status": 204 }
      },
      "processedAt": "2024-01-01T12:00:01.000Z"
    }
  ],
  "summary": {
    "totalUsers": 10,
    "processedUsers": 10,
    "successfulUpdates": 9,
    "errors": [...]
  }
}`}
                        </pre>
                    </div>

                    <div className="alert alert-info">
                        <h6>How It Works:</h6>
                        <ul className="small mb-0">
                            <li><strong>Real API Calls:</strong> Each user gets two actual API calls to update their roles</li>
                            <li><strong>User Lookup:</strong> Users are found by email address from the existing user database</li>
                            <li><strong>Location Mapping:</strong> Location names are mapped to IDs using the location hierarchy</li>
                            <li><strong>Role Assignment:</strong> "yes" values in Excel columns assign the corresponding roles</li>
                            <li><strong>Error Handling:</strong> Failed updates are logged with detailed error messages</li>
                            <li><strong>Progress Tracking:</strong> Real-time progress bar shows upload status</li>
                            <li><strong>Results Download:</strong> Complete results JSON available after processing</li>
                        </ul>
                    </div>
                </Modal.Body>

                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setJsonShow(false)}>Close</Button>
                </Modal.Footer>
            </Modal>
        </CardOverlay>
    )
}


export default MasterUser;
