import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

function Checklist() {
    const [data, setData] = useState([
        {
            "ID": "#230912-79",
            "Category": "Industrial Asset",
            "Description": "Nostrum Quia Eum",
            "Status": "Available"
        },
        {
            "ID": "#230912-78",
            "Category": "Technology Asset",
            "Description": "Nisi Velit Porro",
            "Status": "Unavailable"
        },
        {
            "ID": "#230912-77",
            "Category": "Lorem ipsum Asset",
            "Description": "Voluptatibus Deleniti Non",
            "Status": "Under Service"
        },
        {
            "ID": "#230912-76",
            "Category": "Draft Asset",
            "Description": "Atque Mollitia Reprehenderit",
            "Status": "Active"
        },
        {
            "ID": "#230912-75",
            "Category": "Temporary Asset",
            "Description": "Magni Suscipit Blanditiis",
            "Status": "Alerting"
        },
        {
            "ID": "#230912-79",
            "Category": "Industrial Asset",
            "Description": "Nostrum Quia Eum",
            "Status": "Rented Out"
        },
        {
            "ID": "#230912-78",
            "Category": "Technology Asset",
            "Description": "Nisi Velit Porro",
            "Status": "Unavailable"
        },
        {
            "ID": "#230912-77",
            "Category": "Lorem ipsum Asset",
            "Description": "Voluptatibus Deleniti Non",
            "Status": "Under Service"
        },
        {
            "ID": "#230912-76",
            "Category": "Draft Asset",
            "Description": "Atque Mollitia Reprehenderit",
            "Status": "Active"
        },
        {
            "ID": "#230912-75",
            "Category": "Temporary Asset",
            "Description": "Magni Suscipit Blanditiis",
            "Status": "Decomissioned/Awaiting Disposal"
        }
    ]);

    const [filters, setFilters] = useState(null);
    const [dataFilter, setDataFilter] = useState([]);

    const actionTemplate = (option) => {
        return (
            <div className="d-flex align-items-center justify-centent-between" style={{cursor:'pointer'}}>

                <a className='me-3'>Delete</a>
                <a className='me-3'>Clone</a>
                <a className='me-3'>Edit</a>
            </div>
        );
    }

    return (
        <div>
            <DataTable value={data} paginator rows={10} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found.">
                <Column field='ID' header='ID' filter showFilterMatchModes={false}></Column>
                <Column field='Category' header="Title" sortable></Column>
                <Column field='Description' header="Description" sortable ></Column>
                
                <Column field="" header="Action" body={actionTemplate} showFilterMatchModes={false}></Column>
            </DataTable>
        </div>
    );
}

export default Checklist;
