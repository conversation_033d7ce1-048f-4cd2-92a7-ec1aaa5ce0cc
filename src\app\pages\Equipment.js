import React, { useEffect, useState } from 'react'
import CardOverlay from './CardOverlay'
import SingleFilterLocation from './SingleFilterLocation'
import API from '../services/API';
import { CHECKLIST_URL, EQUIPMENT_URL, EQUIPMENT_WITH_ID_URL } from '../constants';
import cogoToast from 'cogo-toast';
import ListBox from './ListBox';
import { deletePopup, singlePopup } from '../notifications/Swal';
import AppSwitch from './AppSwitch';

const Equipment = () => {
    const [checklist, setChecklist] = useState([]);
    const [data, setData] = useState([])
    const [filterData, setFilterData] = useState([])
    const [selectedData, setSelectedData] = useState({ id: '' });
    const [documents, setDocuments] = useState([])
   
    const [location, setLocation] = useState(
        {
            locationOneId: '',
            locationTwoId: '',
            locationThreeId: '',
            locationFourId: ''
        }
    )
    useEffect(() => {

        getChecklist();
        getEquipment();
    }, [])

    const getChecklist = async () => {


        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            const checklistData = response.data;

            setChecklist(checklistData.filter(i => i.application === 'Equipment').map(i => {
                return { value: i.id, label: i.name }
            }));
        }
    }

    const getEquipment = async () => {


        const response = await API.get(EQUIPMENT_URL);
        if (response.status === 200) {
            const equipmentData = response.data.map(i => {
                i.title = i.name
                delete i.name
                return i
            });

            setData(equipmentData)
            setFilterData(equipmentData)
        }
    }

    const handleDataSelect = (id) => {
        setSelectedData(data.find(i => i.id === id))
    }

    const createData = async (value) => {
        const response = API.post(EQUIPMENT_URL, {
            name: value,
            locationOneId: location.locationOneId,
            locationTwoId: location.locationTwoId,
            locationThreeId: location.locationThreeId,
            locationFourId: location.locationFourId
        })
        if (response.status === 200) {
            const createdData = response.data;
            setData((prev) => [...prev, createData]);
            cogoToast.info('Created!', { position: 'top-right' })

        }

    }

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })
        const filteredData = data.filter(item => {
            return (
                (locationOneId === '' || item.locationOneId === locationOneId) &&
                (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
                (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
                (locationFourId === '' || item.locationFourId === locationFourId)
            );
        });

        setFilterData(filteredData);
    };

    const handleDeleteItem = async (mode, id) => {

        deletePopup.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,

            confirmButtonText: 'Delete'
        }).then(async (result) => {
            if (result.isConfirmed) {
                //   deleteChecklist(id);

                const response = await API.delete(EQUIPMENT_WITH_ID_URL(id), {
                    method: 'DELETE',
                    headers: {
                        "Content-type": "application/json; charset=UTF-8"
                    }
                })

                if (response.status === 204) {
                    switch (mode) {
                        case 'tier1':
                            setData(prev => prev.filter(i => i.id !== id))
                            setFilterData(prev => prev.filter(i => i.id !== id))
                           

                            break;


                        default: break;
                    }
                    singlePopup.fire(
                        'Deleted!',
                        '',
                        'success'
                    );
                }

            }
        })

    }

    return (
        <CardOverlay>
           
            {/* {location.locationFourId && ( */}
                <>
                    <div className="row">
                        <div className="col-md-4 p-1 ps-3">
                            <ListBox documents={documents} location={location.locationFourId} checklist={checklist} changeTitle={(id, value) => setData(prev => prev.map(i => { return i.id === id ? { ...i, name: value } : i }))} title={'Plant & Equipment'} handleDeleteItem={handleDeleteItem} onHandleCreateItem={createData} lists={filterData} handleSelect={handleDataSelect} selectedItem={selectedData} mode='tier1' />
                        </div>
                    </div>
                </>
            {/* )} */}
        </CardOverlay>
    )

}

export default Equipment
