import React, { useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

// Ensure modules load correctly on both hot reload & full refresh
const HighchartsWrapper = ({ options }) => {
  // useEffect(() => {
  //   // Dynamically import modules to prevent "is not a function" error
  //   import('highcharts/modules/exporting').then((module) => {
  //     module.default(Highcharts);
  //   });

  //   import('highcharts/modules/offline-exporting').then((module) => {
  //     module.default(Highcharts);
  //   });
  // }, []);

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default HighchartsWrapper;
