import React, { useState, useEffect } from 'react'
import * as Icon from 'feather-icons-react';
import { Dropdown } from 'react-bootstrap';
// import 'bootstrap-daterangepicker/daterangepicker.css';

function ActionLog({ data, dates, total }) {

    const [date, setDate] = useState('')
    const [count, setCount] = useState(0)

    const [startIndex, setStartIndex] = useState(0);
    const [displayedDates, setDisplayedDates] = useState([]);
    useEffect(() => {

        if (dates.length !== 0) {
            setDisplayedDates(dates.slice(0, 3))
            let cou = 0
            dates.slice(0, 3).forEach(date => (
                cou = data[date].length + cou
            ))
            setCount(cou)
        }

    }, [])



    const handleClick = () => {
        const nextIndexes = startIndex + 3;
        const nextDates = dates.slice(nextIndexes, nextIndexes + 3);
        setDisplayedDates([...displayedDates, ...nextDates]);
        setStartIndex(nextIndexes);
        let cou = 0
        dates.slice(0, nextIndexes + 3).forEach(date => (
            cou = data[date].length + cou
        ))
        setCount(cou)
    };

    const dispalyActionComments = (action) => {
        switch (action.application) {
            case 'DOC':


                break;
            case 'AIR':

                break;
            case 'RA':

                break;
        }



    }

    function getActionText(actionType) {
        const actionTextMap = {
            'action_owner': 'Reported',
            'approve': 'Actions Taken',
            'reject': 'Actions Reassinged',
            'inspect': 'Conduct Inspection',
            'dcso_approver': 'Permit Sent for Approval',
            'air_reviewer': 'Incident is Reviewed by',
            'air_medical_officer': 'Incident is Sent to Medical Report by',
            'air_cost_estimator': 'Sent to Cost Estimator',
            'air_engineer': 'Incident is Sent to Engineer Review by',
            'take_investigation_actions': 'Incident is Sent to Take Action by',
            'reject': 'Incident is Reject by',
            'ra_confirm': 'Draft RiskAssessment Released',
            'approve': 'Sent to Approver',
            'assessor': 'Review & Assess',
            'normalization': 'Normalize',
            'take_actions_control': 'Implement Control Measures',
            'take_actions_control_post': 'Post Investigation Control Measures Assigned',
            'take_actions_ra': 'Review and Update RA / SWP',
            'verify_actions': 'Verify Implementation',
            'retake_actions': 'Retake Actions',
            'reviewer': 'OBS - Verify Actions',
            'verify_investigation_actions': 'Approve Investigation',
            'audit': 'Perform Audit',
            'audit_take_actions': 'Take Actions',
            'audit_verify_actions': 'Verify Actions',
            'audit_retake_actions': 'Retake Actions',
            // Add more mappings as necessary
        };

        return actionTextMap[actionType] || '';
    }

    return (
        <div className='col-12 '>
            <div className='row mb-4'>
                <div className='col-6'>
                    <h5 className='mt-4 fw-bold actionTitle'>My Activity Log</h5>
                </div>
                {/* <div className='col-6 searchbox d-flex justify-content-end align-items-center'>
                   

                    <div class="search me-2">
                        <span class="fa fa-search"></span>
                        <input placeholder="Search Activity" className='form-control' />
                    </div>
                    <div className="nav-item nav-profile ">

                        <Dropdown>
                            <Dropdown.Toggle className="nav-link filter btn-default">
                                <Icon.Filter currentColor='#D0D5DD' />
                                <Icon.ArrowRight currentColor='#D0D5DD' />
                            </Dropdown.Toggle>
                            <Dropdown.Menu className="option-list navbar-dropdown custom-dropdown">
                                <div className=''>
                                    <h4 className='p-2 fil-h4'>Filter</h4>

                                    <div className='p-2 type-fil'>
                                        <h3 className=' fil-h3'>Action Type</h3>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Observation ' /> Observation</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='ePermit to Work ' /> ePermit to Work</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Incident ' /> Incident</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Inspection' /> Inspection</label>
                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Audit' />Audit</label>

                                        <label><input name='options[]' type="checkbox" class="option  me-2" value='Plant' /> Plant & Equipment</label>
                                    </div>
                                    <div className='p-2 type-fil searchbox'>
                                        <h3 className=' fil-h3'>View Activity between</h3>


                                    </div>
                                </div>
                            </Dropdown.Menu>
                        </Dropdown>


                    </div>
                </div> */}
            </div>
            <div className="timeline">

                {displayedDates.map(date => (
                    <div className="timeline-wrapper ">
                        <div className="timeline-badge"></div>
                        <div className="timeline-panel">
                            <div className='timeline-date mb-3 fw-bold '>
                                {date}
                            </div>
                            <div className="timeline-inbox">
                                {data[date].map(action => (
                                    <div className='row mb-2 align-items-center'>

                                        <div className='time col-1'>{action.time}</div>
                                        <div className='col-2 '>
                                            {action.application === 'INCIDENT' ?
                                                <div className='incidentbadge badge '>Incident</div> :
                                                action.application === 'Observation' ?
                                                    <div className='obsbadge badge '>Observation</div> :
                                                    action.application === 'PermitToWork' ?
                                                        <div className='badge badge '>ePTW</div> :
                                                        action.application === 'Inspection' ?
                                                            <div className='inspectionbadge badge '>Inspection</div> :
                                                            action.application === 'Audit' ?
                                                                <div className='auditbadge badge '>Audit</div> :
                                                                action.application === 'AuditFinding' ?
                                                                    <div className='auditbadge badge '>Audit Finding</div> :
                                                                    action.application === 'Plant' ?
                                                                        <div className='plantbadge badge '>Plant & Equipment</div> :
                                                                        ''
                                            }

                                        </div>
                                        <div className='desc col-3'>{action.applicationDetails ? action.applicationDetails.maskId ? action.applicationDetails.maskId : action.applicationDetails.meetid : ''}</div>
                                        <div className='desc col-6'>

                                            {dispalyActionComments(action)}
                                            {getActionText(action.actionType)}

                                        </div>

                                    </div>
                                ))}


                            </div>
                        </div>



                    </div>
                ))}


                {/* <div className="timeline-wrapper ">
                    <div className="timeline-badge"></div>
                    <div className="timeline-panel">
                        <div className='timeline-date mb-3 fw-bold '>
                            10 Jan, 2024
                        </div>
                        <div className="timeline-inbox">
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='obsbadge badge '>Observation</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='eptwbadge badge '>ePermit to Work</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='incidentbadge badge '>Incident</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='documentbadge badge '>Document</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                            <div className='row mb-2 align-items-center'>

                                <div className='time col-1'>12:10 PM</div>
                                <div className='col-2 '>
                                    <div className='riskbadge badge'>Risk</div>
                                </div>
                                <div className='desc col-9'>Document is approved by Smith Perimon</div>

                            </div>
                        </div>
                    </div>

                </div> */}
            </div>
            <div className='action-option ps-5'>
                <p>Showing {count} of {total} activities</p>
                {count === total ? '' :
                    <p className='loadmore' onClick={handleClick}>Load more</p>
                }
            </div>
        </div>

    )
}

export default ActionLog