// Utility function to extract incident level from a string and return its description
// Returns mapped description for 'Level 1' to 'Level 5' if found, otherwise null
function extractImpactLevel(text) {
  if (!text || typeof text !== 'string') return '';
  const levelDescriptions = {
    1: 'First Aid Incident (FAI)',
    2: 'Medical Treatment Incident (MTI)',
    3: 'Lost Time Incident (LTI)',
    4: 'High Severity Incident',
    5: 'Critical Incident'
  };
  const lowerText = text.toLowerCase();
  for (let i = 1; i <= 5; i++) {
    if (lowerText.includes(`level ${i}`)) {
      return levelDescriptions[i];
    }
  }
  return '';
}

export default extractImpactLevel;