import React from 'react'
import 'primereact/resources/themes/saga-blue/theme.css';  //theme
import 'primereact/resources/primereact.min.css';          //core css
import 'primeicons/primeicons.css';                        //icons
import { Container, Row, Col, Card, Nav, Tab } from 'react-bootstrap';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import WaterChart from './WaterChart';
import BarOne from './BarOne';
import BarSix from './BarSix';

function AuditTab() {
    const emissionsData = [
        { month: 'Jan', emission: 32000 },
        { month: 'Feb', emission: 16000 },
        { month: 'Mar', emission: 32000 },
        { month: 'Apr', emission: 10000 },
        { month: 'May', emission: 42000 },
        { month: 'Jun', emission: 2000 },
        // Add more months as needed
    ];

    const emissionsdueData = [
        { month: 'Jan', emission: 32 },
        { month: 'Feb', emission: 16 },
        { month: 'Mar', emission: 34 },
        { month: 'Apr', emission: 50 },
        { month: 'May', emission: 55 },
        { month: 'Jun', emission: 75 },
        // Add more months as needed
    ];

    const emissionsFData = [
        { month: 'Jan', emission: 32 },
        { month: 'Feb', emission: 80 },
        { month: 'Mar', emission: 1540 },
        { month: 'Apr', emission: 2600000 },
        { month: 'May', emission: 3750000 },
        { month: 'Jun', emission: 4850000000 },
        // Add more months as needed
    ];
    const emissionsTData = [
        { month: 'Jan', emission: 32000 },
        { month: 'Feb', emission: 16000 },
        { month: 'Mar', emission: 32000 },
        { month: 'Apr', emission: 10000 },
        { month: 'May', emission: 4200 },
        { month: 'Jun', emission: 2000 },
        // Add more months as needed
    ];

    const styles = {
        cardContainer: {
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            padding: '20px',
            height: '100%'
        },
        textContent: {
            flex: 1
        },
        emissionsValue: {
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '5px'
        },
        targetText: {
            color: 'black',
            fontWeight: 'normal',
            fontSize: '16px'
        },
        greenText: {
            color: 'green',
            fontWeight: 'bold'
        },
        redText: {
            color: 'red',
            fontWeight: 'bold'
        },
        chartContainer: {
            width: 150,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%'
        }
    };
    return (<>
        <Row className="mb-4">
            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    74 %
                                </div>
                                <div style={styles.targetText}>
                                    Audits conducted in accordance to planned schedules
                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="red" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    9.6
                                </div>
                                <div style={styles.targetText}>
                                Average no. of findings requiring action per audit
                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsdueData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="green" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    52%
                                </div>
                                <div style={styles.targetText}>
                                % of identified audit follow-up actions closed within due date

                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsTData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="red" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                1.2
                                </div>
                                <div style={styles.targetText}>
                                Average no. of risk assessments requiring updates based on audit observations and findings
                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsFData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="green" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>
        </Row>


    </>)
}

export default AuditTab