import React from 'react';
import { Line } from 'react-chartjs-2';

const options = {
    responsive: true,
    maintainAspectRatio: false,
    legend: {
        display: false // This will hide the legend
    },
    tooltips: {
        enabled: false // This will hide the tooltips
    },
    scales: {
        xAxes: [{
            display: false, // This will hide the x-axis labels and grid lines
            gridLines: {
                display: false
            },
            ticks: {
                display: false // This will hide the x-axis ticks
            }
        }],
        yAxes: [{
            display: false, // This will hide the y-axis labels and grid lines
            gridLines: {
                display: false
            },
            ticks: {
                display: false // This will hide the y-axis ticks
            }
        }]
    },
    elements: {
        line: {
            tension: 0 // Lines will be straight, no curve
        },
        point: {
            radius: 0 // This will hide the points on the line
        }
    }
};




const MiniChartCard = ({ number, title, color, data }) => {
    const chartData = {
        labels: Array(data.length).fill(''), // No labels
        datasets: [{
            data: data,
            fill: false,
            borderColor: color,
            borderWidth: 2
        }]
    };

    return (
        <div className="card mb-5">
            <div className="card-body d-flex pt-5 pb-3 border">

                {/* This will take up 75% of the container */}
                <div className="col-6">
                    <h2 className="font-weight-bold">{number}</h2>
                    <p className="text-gray">{title}</p>
                    <a href="#" className="btn btn-link p-0">Show Details</a>
                </div>

                {/* This will take up 25% of the container */}
                <div className="col-6 chart-container" style={{ position: 'relative' }}>
                    <div className='h-50'>
                        <Line data={chartData} options={options} />
                    </div>

                </div>
            </div>
        </div>


    );
};

export default MiniChartCard;
