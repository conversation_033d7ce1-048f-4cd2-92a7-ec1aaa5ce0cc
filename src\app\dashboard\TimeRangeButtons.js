import React, { useState } from 'react';


function TimeRangeButtons(props) {
    // Callback to notify parent component of a button click
    const [activeRange, setActiveRange] = useState('1Y'); // State to track the active range

    const handleButtonClick = (range) => {
        setActiveRange(range);
        if (props.onRangeSelected) {
            props.onRangeSelected(range);
        }
    };

    return (

        <div className='d-flex align-items-center custom-chart mb-3'>
           
            <div className="btn-group">
                {['3M', '6M', '1Y', '3Y', 'Max'].map(range => (
                    <button
                        key={range}
                        className={`btn ${activeRange === range ? 'btn-primary' : 'btn-outline-secondary'}`}
                        onClick={() => handleButtonClick(range)}
                    >
                        {range}
                    </button>
                ))}
            </div>
        </div>

    );
}

export default TimeRangeButtons;
