import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";

const ObservationPieChart = ({ info }) => {
    const [observationData, setObservationData] = useState({
        totalObservation: 1855,
        totalCombinations: 4,
        typeConditionCounts: {
            "Safe (Condition)": 85,
            "Safe (Act)": 74,
            "At Risk (Act)": 90,
            "At Risk (Condition)": 371,
        },
    });

    useEffect(() => {
        if (info) setObservationData(info);
    }, [info]);

    // Extract labels and values
    const labels = Object.keys(observationData.typeConditionCounts);
    const dataValues = Object.values(observationData.typeConditionCounts);

    // Highcharts Pie Chart Configuration
    const options = {
        chart: {
            type: "pie",
        },
        title: {
            text: ``,
        },
        tooltip: {
            pointFormat: "<b>{point.name}</b>: {point.y}",
        },
        accessibility: {
            point: {
                valueSuffix: " observations",
            },
        },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: "pointer",
                dataLabels: {
                    enabled: true,
                    format: "<b>{point.name}</b>: {point.y}",
                },
                showInLegend: true, // Enables legend interactivity
            },
        },
        legend: {
            enabled: true, // Enables legend interaction
            labelFormatter: function () {
                return `${this.name} (${this.y})`; // Custom legend format
            },
        },
        series: [
            {
                name: "Observations",
                colorByPoint: true,
                data: labels.map((label, index) => ({
                    name: label,
                    y: dataValues[index],
                })),
            },
        ],
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return (
        <div style={{ width: "100%", margin: "auto" }}>
            <HighchartsWrapper options={options} />
        </div>
    );
};

export default ObservationPieChart;
