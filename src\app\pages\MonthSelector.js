import React, { useEffect, useState } from 'react';
import { format, addMonths, subMonths, isBefore } from 'date-fns';
import { mdiChevronLeft, mdiChevronRight } from '@mdi/js';
import Icon from '@mdi/react';

const MonthSelector = ({ onChange }) => {
  // Set the initial date to the previous month
  const initialDate = subMonths(new Date(), 1);
  const [currentDate, setCurrentDate] = useState(initialDate);

  useEffect(() => {
    onChange(format(currentDate, 'MMMM yyyy'), format(currentDate, 'MM'), format(currentDate, 'yyyy'));
  }, [currentDate])
  const updateDate = (newDate) => {
    setCurrentDate(newDate);

  };

  const subtractMonth = () => {
    const newDate = subMonths(currentDate, 1);
    updateDate(newDate);
  };

  const addMonth = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isBefore(currentDate, today)) {
      const newDate = addMonths(currentDate, 1);
      updateDate(newDate);
    }
  };

  // Disable forward button if currentDate is not before today (it is either today or in the future)
  const isForwardButtonDisabled = !isBefore(currentDate, new Date());

  return (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '20px' }}>
      <Icon
        path={mdiChevronLeft}
        size={1}
        onClick={subtractMonth}
        style={{ cursor: 'pointer' }}
      />
      <span style={{ margin: '0 16px', width: '200px', textAlign: 'center' }}>
        {format(currentDate, 'MMMM yyyy')}
      </span>
      <Icon
        path={mdiChevronRight}
        size={1}
        onClick={addMonth}
        style={{ cursor: isForwardButtonDisabled ? 'not-allowed' : 'pointer' }}
        color={isForwardButtonDisabled ? 'grey' : 'black'}
      />
    </div>
  );
};

export default MonthSelector;
