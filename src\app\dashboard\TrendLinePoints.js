export function linearRegression(data) {
    let n = data.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    for (let i = 0; i < n; i++) {
        sumX += i;
        sumY += data[i].value;
        sumXY += i * data[i].value;
        sumXX += i * i;
    }

    let slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    let intercept = (sumY - slope * sumX) / n;

    // Return two points that define the line of best fit
    return [
        { x: 0, y: intercept },
        { x: n - 1, y: slope * (n - 1) + intercept }
    ];
}

// Function to add x and y values to the data for Recharts
export function getTrendlinePoints(data) {
    const trendlineData = linearRegression(data);

    // Return the start and end points of the line
    return trendlineData.map((point, index) => ({
        x: data[index].date,
        y: point.y
    }));
}