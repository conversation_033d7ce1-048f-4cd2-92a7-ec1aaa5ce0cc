import React from 'react';
import { Icon } from '@mdi/react';
import { mdiWheelchairAccessibility, mdiBed, mdiAccountInjury, mdiFileChart } from '@mdi/js';

const IncidentStats = ({ data }) => {
    return (
        <div className=" my-3">
            <div className="row g-3">
                {/* Serious Incidents */}
                <div className="col">
                    <div className="card shadow-sm border">
                        <div className="card-body d-flex align-items-center justify-content-between">
                            <Icon path={mdiWheelchairAccessibility} size={2} className="flex-shrink-0" />
                            <div className="card-title mb-0 text-start">
                                <h5 className="text-danger fw-bold f-20">{data.seriousIncidents}</h5>
                                <p className="card-text f-20">Serious Incidents</p>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Lost Time Incidents */}
                <div className="col">
                    <div className="card shadow-sm border">
                        <div className="card-body d-flex align-items-center justify-content-between">
                            <Icon path={mdiBed} size={2} className="flex-shrink-0" />
                            <div className="card-title mb-0 text-start">
                                <h5 className="text-danger fw-bold f-20">{data.LTI}</h5>
                                <p className="card-text f-20">Lost Time Incidents</p>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Reported Injuries */}
                <div className="col">
                    <div className="card shadow-sm border">
                        <div className="card-body d-flex align-items-center justify-content-between">
                            <Icon path={mdiAccountInjury} size={2} className="flex-shrink-0" />
                            <div className="card-title mb-0 text-start">
                                <h5 className="text-danger fw-bold f-20">{data.reportedInjuries}</h5>
                                <p className="card-text f-20">Reported Injuries</p>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Total Incidents */}
                <div className="col">
                    <div className="card shadow-sm border">
                        <div className="card-body d-flex align-items-center justify-content-between">
                            <Icon path={mdiFileChart} size={2} className="flex-shrink-0" />
                            <div className="card-title mb-0 text-start">
                                <h5 className="text-danger fw-bold f-20">{data.totalIncidents}</h5>
                                <p className="card-text f-20">Total Incidents</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

IncidentStats.defaultProps = {
    data: {
        totalIncidents: 0,
        reportedInjuries: 0,
        LTI: 0,
        seriousIncidents: 0
    }
}

export default IncidentStats;
