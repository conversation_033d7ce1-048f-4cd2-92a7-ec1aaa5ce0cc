import React, { useState, useEffect } from "react";
import { CHECKLIST_URL, INSPECTION_URL } from '../constants';
import cogoToast from 'cogo-toast';
import API from '../services/API';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import InspectionForm from "./InspectionForm";

const InspectionScheduling = ({ locationOneId, locationTwoId, locationThreeId, locationFourId }) => {
   
    const [location, setLocation] = useState(
        {
            locationOneId: '',
            locationTwoId: '',
            locationThreeId: '',
            locationFourId: ''
        }
    )
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    const years = [currentYear, currentYear + 1];
    const [selectedYear, setSelectedYear] = useState(currentYear);

    useEffect(() => {
        setLocation({ locationOneId: locationOneId.id, locationTwoId: locationTwoId.id, locationThreeId: locationThreeId.id, locationFourId: locationFourId.id })

    }, [locationOneId, locationTwoId, locationThreeId, locationFourId])


    useEffect(() => {
        getChecklist()
    }, [selectedYear])


    const [checklist, setChecklist] = useState([]);


    const getChecklist = async () => {
        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            return response.data.filter(i => i.application === 'Inspection').map(i => ({ ...i, monthlyStatus: new Array(12).fill('') }));
        }
        return [];
    }

    useEffect(() => {
        // This will fetch and update the checklist data initially and whenever the location or selectedYear changes
        const updateData = async () => {
            const fetchedChecklist = await getChecklist();
            setChecklist(fetchedChecklist);
            if (location.locationOneId && location.locationTwoId && location.locationThreeId && location.locationFourId && selectedYear) {
                getInspection();
            }
        };

        updateData();
    }, [location.locationOneId, location.locationTwoId, location.locationThreeId, location.locationFourId, selectedYear]);




    const [inspection, setInspection] = useState([]);



    const getInspection = async () => {
        const response = await API.get(INSPECTION_URL);
        if (response.status === 200) {
            
            setInspection(response.data.filter(i => i.locationOneId === location.locationOneId && i.locationTwoId === location.locationTwoId && i.locationThreeId === location.locationThreeId && i.locationFourId === location.locationFourId && parseInt(i.year) === parseInt(selectedYear)));

        }
    }

    useEffect(() => {
        // This will update the checklist with inspection data
        if (inspection.length > 0) {
            
            updateChecklistWithInspection(inspection);
        }
    }, [inspection]);

 

    const updateChecklistWithInspection = (inspectionData) => {
        setChecklist((currentChecklist) => {
            console.log(currentChecklist, 'checklist')
            const newChecklist = currentChecklist.map((checklistItem) => {
                const updatedStatus = [...checklistItem.monthlyStatus];
                // Find the inspection data for this checklist item
                inspectionData.forEach((inspection) => {
                    if (inspection.checklistId === checklistItem.id) {
                        // Convert month string to index (Jan = 0, Feb = 1, etc.)
                        const monthIndex = months.indexOf(inspection.month);
                        console.log(monthIndex)
                        if (monthIndex !== -1) {
                            // Update the status for the corresponding month
                            updatedStatus[monthIndex] = inspection.status;
                        }
                    }
                });
                return { ...checklistItem, monthlyStatus: updatedStatus };
            });

            return newChecklist;
        });
    }




    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];


    const [popupData, setPopupData] = useState({ month: '', checklist: null, location: null });
    const [showModal, setShowModal] = useState(false);
    const handleCellClick = (monthIndex, checklistId) => {

        const selectedChecklistItem = checklist.find(i => i.id === checklistId);

        setPopupData({ month: months[monthIndex], checklist: selectedChecklistItem, location: location });
        setShowModal(true);
    };

    const handleSubmit = async (formData) => {
        formData.year = `${selectedYear}`;
        formData.locationOneId = location.locationOneId;
        formData.locationTwoId = location.locationTwoId;
        formData.locationThreeId = location.locationThreeId;
        formData.locationFourId = location.locationFourId;
        formData.status = 'Initiated'

        const response = await API.post(INSPECTION_URL, formData)
        if (response.status === 200) {
            cogoToast.success('Submitted!')
            getChecklist();
            getInspection();
            setPopupData({ month: '', checklist: null, location: null })
            setShowModal(false)
        }
    }
    const renderCell = (inspection, monthIndex, monthName) => {
        const isPast = selectedYear < currentYear || (selectedYear === currentYear && monthIndex < currentMonth);
        const status = inspection.monthlyStatus[monthIndex];

        return (
            <div
                className={`cursor-pointer ${isPast ? 'disabled-cell' : ''}`}
                onClick={() => !isPast && handleCellClick(monthIndex, inspection.id)}
            >
                {status ?
                    <span className="initiated-cell">{status}</span>
                    :
                    (!isPast && <span>Initiate Inspection</span>)
                }

            </div>
        );
    };
    const headerTemplate = () => {
        return (
            <div className="row mb-3">
                <div className="col-md-4">
                    <label htmlFor="yearSelector" className="form-label">Select Year:</label>
                    <select
                        id="yearSelector"
                        className="form-select"
                        value={selectedYear}
                        onChange={(e) => setSelectedYear(e.target.value)}
                    >
                        {years.map(year => (
                            <option key={year} value={year}>
                                {year}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
        )
    }

    const rows = months.map((month, monthIndex) => {
        const row = { month };
        checklist.forEach((inspection) => {
            row[inspection.name] = renderCell(inspection, monthIndex, month);
        });
        return row;
    });
    return (
        <>
            <div className=" ">
             
                {(location.locationOneId && location.locationTwoId && location.locationThreeId && location.locationFourId) &&
                    <DataTable value={rows} header={headerTemplate}>
                        <Column field="month" header="Months" />
                        {checklist.map((inspection) => (
                            <Column key={inspection.id} field={inspection.name} header={inspection.name} />
                        ))}
                    </DataTable>
                }
            </div>

            {(showModal && popupData.month && popupData.checklist) && <InspectionForm
                show={showModal}
                onHide={() => setShowModal(false)}
                month={popupData.month}
                year={selectedYear}
                checklist={popupData.checklist}
                location={popupData.location}
                onSubmit={(formData) => {
                   
                    handleSubmit(formData)
                }}
            />}
        </>
    )
}


export default InspectionScheduling;