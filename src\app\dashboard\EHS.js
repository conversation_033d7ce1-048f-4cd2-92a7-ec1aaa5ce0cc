import React, { useState, useEffect } from "react";
import { Row, Col, Card } from "react-bootstrap";
import SafetyMetricsChart from "./SafetyMetricsChart";
import WorkHoursChart from "./WorkHoursChart";
import { infoData } from "./data";
import { getDisplayDateRange } from "./dateUtils";

import { transformApiResponse } from "../utils/transformData"; // Import transformation function
import { ALL_REPORT_DATA_URL } from "../constants"; // API endpoint
import API from "../services/API";


function EHS({ dateRange, filterCriteria }) {
    const displayDateRange = getDisplayDateRange(dateRange);

    const [rData, setRData] = useState([]);
    const [mergedData, setMergedData] = useState([]);
    const [loading, setLoading] = useState(true); // Track API loading state
    function transformApiResponse(apiResponseArray) {
        return apiResponseArray
            .filter(apiResponse => parseInt(apiResponse.yearAndMonth.split(" ")[1]) === 2025) // Only keep 2025 data
            .map(apiResponse => {
                // Extract year and month
                const [month, year] = apiResponse.yearAndMonth.split(" ");

                // Extract country from locationOne
                const country = apiResponse.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                // Determine BU based on locationThree.name
                let bu = "Other";
                if (apiResponse.locationThree?.name.includes("Construction") || apiResponse.locationThree?.name.includes("Fitouts")) {
                    bu = "Construction";
                } else if (apiResponse.locationThree?.name.includes("DC") || apiResponse.locationThree?.name.includes("Data Center") || apiResponse.locationThree?.name.includes("Data Centre")) {
                    bu = "DC";
                } else if (apiResponse.locationThree?.name.includes("Office")) {
                    bu = "Office";
                }


                // Format site name (replace spaces with underscores)
                const site = apiResponse.locationFour?.name.replace(/\s+/g, "_") || "Unknown_Site";

                return {
                    year: parseInt(year),
                    country,
                    bu,
                    month: month.toLowerCase(),
                    level: "site",
                    site,

                    // Employee data
                    averageNoOfSTTGdcEmployeesPerDay: parseFloat((apiResponse.numberOfEmployees / (apiResponse.workingDaysOfEmployee || 1)).toFixed(0)),
                    averageNoOfContractorEmployeesPerDay: parseFloat((apiResponse.numberofContractors / (apiResponse.workingDaysOfContractors || 1)).toFixed(0)),
                    totalNoOfEmployees: parseFloat((apiResponse.numberOfEmployees + apiResponse.numberofContractors).toFixed(0)),

                    // Work hours
                    monthlyHoursWorked: parseFloat(((apiResponse.dailyHoursOfEmployee || 0) +
                        (apiResponse.dailyHoursOfContractors || 0)).toFixed(0)),
                    cumulativeWorkHours: parseFloat(((apiResponse.dailyHoursOfEmployee || 0) +
                        (apiResponse.dailyHoursOfContractors || 0)).toFixed(0)),

                    // Safety and Training
                    noOfSafetyInductionsConducted: parseFloat((apiResponse.noOfSafety || 0).toFixed(0)),
                    noOfToolboxMeetingsSafetyBriefingsSafeStarts: parseFloat((apiResponse.noOfToolbox || 0).toFixed(0)),
                    noOfEHSTrainings: parseFloat((apiResponse.noOfEhsTraining || 0).toFixed(0)),
                    noOfEHSInspectionsAudits: parseFloat((apiResponse.noOfInspection || 0).toFixed(0)),
                    noOfManagementSiteWalkInspection: parseFloat((apiResponse.noOfManagmentSiteWalk || 0).toFixed(0)),
                    authorityNGOUnionVisits: parseFloat((apiResponse.noOfAuthority || 0).toFixed(0)),

                    // Observations
                    noOfSafeObservations: parseFloat((apiResponse.noOfSafeObservation || 0).toFixed(0)),
                    noOfAtRiskObservations: parseFloat((apiResponse.noOfRiskObservation || 0).toFixed(0)),
                    totalNoOfObservations: parseFloat(((apiResponse.noOfSafeObservation || 0) + (apiResponse.noOfRiskObservation || 0)).toFixed(0)),

                    // Incidents & Compliance (default to 0 if not provided)
                    noOfFatality: 0,
                    noOfDaysAwayFromWorkCasesLTICases: 0,
                    noOfRestrictedWorkCasesLightDutyJobTransfer: 0,
                    noOfLossOfConsciousnessCases: 0,
                    noOfMedicalTreatmentCases: 0,
                    noOfHealthRelatedCases: 0,
                    noOfRecordableIncidentCases: 0,
                    legalAndOtherNonCompliances: 0,
                    noticesOrStopWorkOrders: 0,
                    authorityReportableIncident: 0,
                    noOfNearMissCases: 0,
                    noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence: 0,
                    noOfFirstAidCases: 0
                };

            });
    }
    // Fetch API data
    useEffect(() => {
        const fetchReportData = async () => {
            try {
                const params = {
                    include: [
                        { relation: "locationOne" },
                        { relation: "locationTwo" },
                        { relation: "locationThree" },
                        { relation: "locationFour" },
                    ],
                };

                const response = await API.get(
                    `${ALL_REPORT_DATA_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`
                );

                if (response.status === 200) {
                    const data = response.data.filter((i) => i.type === "monthly");
                    const transformedData = transformApiResponse(data);

                    setRData(transformedData);
                }
            } catch (error) {
                console.error("Error fetching report data:", error);
            } finally {
                setLoading(false); // Mark API call as complete
            }
        };


        fetchReportData();
    }, []);

    // Merge `infoData` and `rData`
    useEffect(() => {
        if (!loading && rData.length > 0 && infoData.length > 0) {
            const combinedData = [...infoData, ...rData];




            const filteredData = combinedData.filter(item => {
                const isCountrySelected = filterCriteria.countries.some(country => country.id === item.country);
                const isBUSelected = filterCriteria.buLevels.some(bu => bu.id === item.bu);
                const isSiteSelected = item.site ? filterCriteria.sites.some(site => site.id === item.site) : true;

                return isCountrySelected && isBUSelected && isSiteSelected;
            });

            setMergedData(filteredData);
        }
    }, [rData, loading, infoData, filterCriteria]);


    return (
        <>


            <Row className="mb-12 mb-4">
                <Col md={12}>
                    <Card >
                        <Card.Body>
                            <h5 className="font-weight-bold">Safety Metrics (Monthly Report) | {displayDateRange}</h5>
                            <SafetyMetricsChart data={mergedData} dateRange={dateRange} />
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mb-12 mb-4">
                <Col md={12}>
                    <Card >
                        <Card.Body>
                            <h5 className="font-weight-bold">Daily manpower on site | {displayDateRange}</h5>
                            <WorkHoursChart data={mergedData} dateRange={dateRange} />
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </>
    );
}

export default EHS;
