import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import moment from "moment";
import GalleryPage from '../apps/Gallery';
import API from "../services/API";
import { STATIC_URL, USERS_URL } from "../constants";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';


const ObservationModal = ({ reportData, showReportModal, setShowReportModal }) => {

    useEffect(() => {
        if (reportData && reportData.actions)
            getAllActions();
    }, [reportData])
    let k = 0;
    const [actions, setActions] = useState([])
    const [users, setUsers] = useState([])
    const [lastActionOwnerObject, setlastActionOwnerObject] = useState(null)
    let conditionAct = '';
    try {
        const parsedRemarks = JSON.parse(reportData.remarks);
        conditionAct = parsedRemarks.condition_act ? `(${parsedRemarks.condition_act})` : '';
    } catch (error) {
        console.error('Error parsing remarks:', error);
    }
    useEffect(() => { getAllUsers() }, [])

    const getAllActions = async () => {
        if (reportData.actions && reportData.actions.length > 0) {
            const filteredAndFormattedData = reportData.actions
                .filter(item => item.actionType === 'reject' || item.actionType === 'approve' || item.actionType === 'reviewer')
                .map(item => ({
                    ...item,
                    evidence: item.uploads.map(upload => ({
                        src: `${STATIC_URL}/${upload}`,
                        width: 4,
                        height: 3
                    })),
                    createdDate: moment(item.createdDate).format('Do MMM YYYY hh:mm:ss A') // Including createdDate as requested
                }));

            console.log(filteredAndFormattedData, 'action')
            setActions(filteredAndFormattedData)
        }


    }



    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }

    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user?.firstName : ''
    }

    function getCompany(id) {
        const user = users.find(user => user.id === id)
        console.log(user);
        if (user.type === 'External') {
            return id ? user.company : ''
        } else {
            return id ? 'STT GDC' : ''
        }
    }


    const getReviewerDate = (item) => {

        const reviewerId = item.reviewerId ? item.reviewerId : ''
        const date = item.actions?.filter(item => item.submittedById === reviewerId)
        console.log(date)
        if (date !== undefined) {
            return moment(date.createdDate).format('DD-MM-YYYY')
        }
        else {
            return ''
        }

    }

    const getClosedDate = (item) => {

        return moment(item.createdDate).format('Do MMM YYYY, hh:mm:ss a')

    }
    const getActionReviewerComments = (item) => {


        const date = item.actions.filter(item => item.actionType === 'approve')

        return date[0].comments


    }
    const getActionReviewerDate = (item) => {


        const date = item.actions.filter(item => item.actionType === 'approve')

        return moment(date[0].createdDate).format('MMMM Do YYYY, h:mm:ss a')



    }
    // useEffect(() => {
    //     if (reportData) {
    //         lastActionOwner(reportData.actions)
    //     }


    // }, [lastActionOwnerObject])
    // const lastActionOwner = (dataArray) => {
    //     const actionOwnerObject = dataArray.find(item => item.actionType === "action_owner");


    //     setlastActionOwnerObject(actionOwnerObject)


    // }

    // const generatePdf = () => {
    //     const input = document.getElementById('pdf-content');
    //     const pdf = new jsPDF('p', 'mm', 'a4');
    //     const pdfWidth = pdf.internal.pageSize.getWidth();
    //     const pdfHeight = pdf.internal.pageSize.getHeight();
    //     const marginBottom = 10; // margin at the bottom of each page

    //     html2canvas(input).then((canvas) => {
    //         const imgData = canvas.toDataURL('image/png');
    //         const imgProps = pdf.getImageProperties(imgData);
    //         const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

    //         let heightLeft = imgHeight;
    //         let position = 0;

    //         pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //         heightLeft -= pdfHeight;

    //         while (heightLeft >= 0) {
    //             position = heightLeft - imgHeight + marginBottom;
    //             pdf.addPage();
    //             pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
    //             heightLeft -= pdfHeight;
    //         }

    //         pdf.save('permit-report.pdf');
    //     });
    // }
    return (
        <>

            <Modal
                show={showReportModal}
                size={'xl'}
                className="medium-modal"
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
            >
                {/* <Modal.Header>
                    <div className="col-3 d-flex justify-content-end align-items-center">

                        <Button type="button" 
                            onClick={generatePdf}

                        >Download Report</Button>

                    </div>
                </Modal.Header> */}

                <Modal.Body className="p-0">
                    {
                        reportData && (<>

                            <div className="observation-report">
                                <div className=" p-4 pb-0">
                                    <div className="d-flex justify-content-between">
                                        <h2 className="mb-3 obs-head-title">Observation Report</h2>




                                    </div>



                                    <div className="d-flex align-items-center mb-2">

                                        <h3 className="mb-0 me-2 obs-id">{(reportData && reportData.maskId) ? reportData.maskId : ''}</h3> <span className={`badge ${reportData.status.includes("Risk") ? 'badge-danger' : 'badge-success'}`}>
                                            {reportData.status}
                                        </span>
                                    </div>
                                    <div className="row mt-4 mb-4">
                                        {reportData.isQR && <div className="col-12">
                                            <p className="obs-title">Description</p>
                                            <p className="obs-dec obs-head-color">

                                                {reportData.describeActionTaken || reportData.describeAtRiskObservation || reportData.describeSafekObservation || "No description available"}

                                            </p>
                                        </div>}

                                        {!reportData.isQR && <div className="col-12">
                                            <p className="obs-title">Description</p>
                                            <p className="obs-dec obs-head-color">

                                                {reportData.description}

                                            </p>
                                        </div>}

                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-8">
                                            <p className="obs-title">Domain</p>
                                            <p className="obs-content">{reportData.category || ''}</p>
                                        </div>

                                        <div className="col-md-4">
                                            <p className="obs-title">Category</p>
                                            <p className="obs-content">{reportData.type + ' - ' + (reportData.isQR ? reportData.conditionAct : (conditionAct || ''))}</p>
                                        </div>


                                    </div>

                                    <div className="col-md-12">
                                        <p className="obs-title"> Images</p>
                                        {reportData.uploads && reportData.uploads.length > 0 && (

                                            <>
                                                <GalleryPage photos={reportData.uploads} />


                                            </>)}
                                    </div>
                                </div>

                                <div className="obs-section p-4">
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Reported Date</p>
                                            <p className="obs-content">{reportData && moment(reportData.created).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                        </div>
                                        {/* {reportData.type !== 'Safe' ?
                                            reportData.rectifiedStatus !== 'Yes' ?
                                                <div className="col-md-6">
                                                    <p className="obs-title">Due Date</p>
                                                    <p className="obs-content">
                                                        {reportData && reportData.dueDate
                                                            ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                            : '-'}
                                                    </p>
                                                </div> : '' : ''} */}
                                    </div>

                                    {reportData.isQR ?
                                        <div className="row">
                                            <div className="col-md-6">
                                                <p className="obs-title">Role</p>
                                                <p className="obs-content">{reportData.qrRole}</p>
                                            </div>

                                            <div className="col-md-6">
                                                <p className="obs-title">Hazard Category</p>
                                                <p className="obs-content"> {reportData.hazardCategory?.name}</p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">Hazard Description</p>
                                                <p className="obs-content"> {reportData.hazardDescription?.name}</p>
                                            </div>
                                        </div>
                                        :
                                        <div className="row">
                                            <div className="col-md-6">
                                                <p className="obs-title">Reporter</p>
                                                <p className="obs-content">{reportData.submittedId && getName(reportData.submittedId)}</p>
                                            </div>

                                            <div className="col-md-6">
                                                <p className="obs-title">Reporter Organization</p>
                                                <p className="obs-content"> {reportData.submittedId && getCompany(reportData.submittedId)}</p>
                                            </div>
                                        </div>
                                    }

                                </div>

                                <div className="obs-section p-4">
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Location</p>
                                            <p className="obs-content">{(reportData.locationOne && reportData.locationTwo) && reportData.locationTwo.name + ', ' + reportData.locationOne.name}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Business Unit</p>
                                            <p className="obs-content">{reportData.locationThree && reportData.locationThree.name}</p>
                                        </div>
                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Project/DC name</p>
                                            <p className="obs-content">{reportData.locationFour && reportData.locationFour.name}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Level</p>
                                            <p className="obs-content">{reportData.locationFive && reportData.locationFive.name}</p>
                                        </div>
                                    </div>

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Zone</p>
                                            <p className="obs-content">{reportData.locationSix && reportData.locationSix.name}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Work Activity</p>
                                            <p className="obs-content">{reportData.workActivity?.name || ''}</p>
                                        </div>
                                    </div>
                                    {!reportData.isQR &&
                                        <div className="row">
                                            <div className="col-md-6">
                                                <p className="obs-title">GMS Standard</p>
                                                <p className="obs-content">{reportData.ghsOne?.name || ''}</p>
                                            </div>

                                            <div className="col-md-6">
                                                <p className="obs-title">Sub GMS</p>
                                                <p className="obs-content">{reportData.ghsTwo?.name || ''}</p>
                                            </div>
                                        </div>
                                    }
                                </div>



                                {
                                    reportData.type !== 'Safe' ?
                                        <div className="obs-section p-4">


                                            {reportData.rectifiedStatus !== 'Yes' &&

                                                <>
                                                    {reportData.actions &&

                                                        reportData.actions.map((action, i) => {


                                                            if (action.actionType === 'action_owner') {
                                                                const actionOwnerObject = reportData.actions.slice().reverse().find(item => item.actionType === "action_owner");
                                                                console.log(actionOwnerObject)

                                                                k = k + 1;

                                                                return (
                                                                    <div className="obs-section p-4">
                                                                        <div className="row mb-3">
                                                                            <div className="col-md-12">
                                                                                <div className="row">

                                                                                    {k === 1 ?
                                                                                        <>
                                                                                            <div className="col-6">
                                                                                                <p className="obs-title"> Assigned Action {reportData.maskId} - A{k} </p>
                                                                                                <p className="obs-content">{action.actionToBeTaken}</p>
                                                                                            </div>
                                                                                            <div className="col-md-6">
                                                                                                <p className="obs-title">Due Date</p>
                                                                                                <p className="obs-content">
                                                                                                    {action
                                                                                                        ? moment(action.dueDate, [
                                                                                                            "DD-MM-YYYY",
                                                                                                            "DD/MM/YYYY",
                                                                                                            moment.ISO_8601,
                                                                                                        ]).isValid()
                                                                                                            ? moment(action.dueDate, [
                                                                                                                "DD-MM-YYYY",
                                                                                                                "DD/MM/YYYY",
                                                                                                                moment.ISO_8601,
                                                                                                            ]).format("Do MMM YYYY")
                                                                                                            : "-"
                                                                                                        : "-"}
                                                                                                </p>
                                                                                            </div>
                                                                                            <div className="col-6">
                                                                                                {action.status === 'open' && <>

                                                                                                    <p className="obs-title"> Action Assignee</p>
                                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                                        getName(action.assignedToId
                                                                                                        )}</p>

                                                                                                </>}
                                                                                            </div>


                                                                                        </>
                                                                                        :
                                                                                        <>
                                                                                            <div className="col-md-6">
                                                                                                <p className="obs-title"> Action Verifier Comments & Reassigned Action {reportData.maskId} - A{k} </p>

                                                                                                {/* <p className="obs-content">{action.comments}</p> */}
                                                                                            </div>
                                                                                            <div className="col-md-6">
                                                                                                <p className="obs-title">Due Date</p>
                                                                                                <p className="obs-content">
                                                                                                    {action
                                                                                                        ? moment(action.dueDate, [
                                                                                                            "DD-MM-YYYY",
                                                                                                            "DD/MM/YYYY",
                                                                                                            moment.ISO_8601,
                                                                                                        ]).isValid()
                                                                                                            ? moment(action.dueDate, [
                                                                                                                "DD-MM-YYYY",
                                                                                                                "DD/MM/YYYY",
                                                                                                                moment.ISO_8601,
                                                                                                            ]).format("Do MMM YYYY")
                                                                                                            : "-"
                                                                                                        : "-"}
                                                                                                </p>
                                                                                            </div>

                                                                                            <p className="obs-title">Action To Be Taken</p>
                                                                                            <p className="obs-content">{action.actionToBeTaken}</p>
                                                                                            <div className="col-6">
                                                                                                {action.status === 'open' && <>

                                                                                                    <p className="obs-title"> Action Assignee</p>
                                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                                        getName(action.assignedToId
                                                                                                        )}</p>

                                                                                                </>}
                                                                                            </div>


                                                                                        </>
                                                                                    }

                                                                                    {/* {actionOwnerObject ?
                                                                                        action.id === actionOwnerObject.id &&

                                                                                        <div className="col-md-6">
                                                                                            <p className="obs-title">Due Date</p>
                                                                                            <p className="obs-content">
                                                                                                {reportData && reportData.dueDate
                                                                                                    ? moment(reportData.dueDate, ["DD-MM-YYYY", "DD/MM/YYYY"]).format('Do MMM YYYY')
                                                                                                    : '-'}
                                                                                            </p>
                                                                                        </div>
                                                                                        : ''
                                                                                    } */}
                                                                                </div>
                                                                            </div>
                                                                        </div>


                                                                        {action.status === 'submitted' && <>
                                                                            <div className="row mb-3">
                                                                                <div className="col-md-12">
                                                                                    <p className="obs-title">Action Taken </p>
                                                                                    <p className="obs-content">{action.actionTaken}</p>
                                                                                </div>
                                                                            </div>

                                                                            <div className="row mb-3">
                                                                                <div className="col-md-6">
                                                                                    <p className="obs-title">Action Taken By</p>
                                                                                    <p className="obs-content">{action.assignedToId &&
                                                                                        getName(action.assignedToId
                                                                                        )}</p>
                                                                                </div>
                                                                                <div className="col-md-6">
                                                                                    <p className="obs-title">Date</p>
                                                                                    <p className="obs-content">{getClosedDate(action)}</p>
                                                                                </div>
                                                                            </div>

                                                                            <div className="col-md-12">
                                                                                {action.uploads && <>
                                                                                    <p className="obs-title">Evidence</p>
                                                                                    <div className="d-flex">
                                                                                        {action.uploads.map((item, index) => {
                                                                                            const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(item); // Check if the file is an image
                                                                                            return isImage ? (
                                                                                                <GalleryPage
                                                                                                    key={index}
                                                                                                    photos={[
                                                                                                        {
                                                                                                            src: `${STATIC_URL}/${item}`,
                                                                                                            width: 4,
                                                                                                            height: 3,
                                                                                                        },
                                                                                                    ]}
                                                                                                />
                                                                                            ) : (
                                                                                                <a
                                                                                                    key={index}
                                                                                                    href={`${STATIC_URL}/${item}`}
                                                                                                    target="_blank"
                                                                                                    rel="noopener noreferrer"
                                                                                                    className="file-link"
                                                                                                >
                                                                                                    View File
                                                                                                </a>
                                                                                            );
                                                                                        })}
                                                                                    </div>

                                                                                </>
                                                                                }

                                                                            </div>
                                                                        </>
                                                                        }

                                                                    </div>
                                                                )


                                                            } else if (action.actionType === 'reviewer') {

                                                                return (
                                                                    <div className="obs-section p-4">

                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verifier - A{k}</p>
                                                                                <p className="obs-content">{getName(action.assignedToId)}</p>
                                                                            </div>
                                                                            {/* <div className="col-md-6">
                                                                                    <p className="obs-title">Date</p>
                                                                                    <p className="obs-content">{moment(action.createdDate).format('MMMM Do YYYY, h:mm:ss a')}</p>
                                                                                </div> */}
                                                                        </div>

                                                                    </div>
                                                                )
                                                            } else if (action.actionType === 'reject' && action.status === 'submitted') {

                                                                return (
                                                                    <div className="obs-section p-4">

                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verifier - A{k}</p>
                                                                                <p className="obs-content">{getName(action.assignedToId)}</p>
                                                                            </div>
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Date</p>
                                                                                <p className="obs-content">{moment(action.createdDate).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                                                            </div>

                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Comments</p>
                                                                                <p className="obs-content">{action.comments}</p>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                )
                                                            } else if (action.actionType === 'approve' && action.status === 'submitted') {
                                                                return (
                                                                    <div className="obs-section p-4">


                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verified By</p>
                                                                                <p className="obs-content">{reportData.reviewerId && getName(reportData.reviewerId)}</p>
                                                                            </div>
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Date</p>
                                                                                <p className="obs-content">{moment(action.createdDate).format('Do MMM YYYY, hh:mm:ss a')}</p>
                                                                            </div>
                                                                        </div>
                                                                        <div className="row mb-3">
                                                                            <div className="col-md-6">
                                                                                <p className="obs-title">Action Verifier Comments</p>
                                                                                <p className="obs-content">{action.comments}</p>
                                                                            </div>
                                                                        </div>

                                                                        <span className={`badge ${reportData.status.includes("Risk") ? 'badge-danger' : 'badge-success'}`}>
                                                                            {reportData.status}
                                                                        </span>






                                                                    </div>
                                                                )
                                                            }

                                                        })}




                                                </>}





                                        </div>
                                        : ''}
                                {reportData.rectifiedStatus === 'Yes' && (<>
                                    <div className="obs-section p-4">
                                        <div className="col-md-12">



                                            <div className="row mb-3">
                                                <div className="col-md-12">
                                                    <p className="obs-title">Action Taken </p>
                                                    <p className="obs-content">{reportData.actionTaken}</p>
                                                </div>
                                            </div>
                                            <p className="obs-title">Evidence</p>
                                            <div className="row">
                                                <div className="col-12">

                                                    <GalleryPage photos={reportData.evidence} />
                                                </div>
                                            </div>



                                        </div>
                                    </div>
                                </>)
                                }
                            </div>





                        </>)
                    }
                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {

                        <>
                            <Button variant="light" onClick={() => setShowReportModal(false)}>Close</Button>

                        </>

                    }

                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ObservationModal;