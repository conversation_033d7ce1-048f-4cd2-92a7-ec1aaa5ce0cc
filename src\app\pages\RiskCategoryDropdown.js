import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    RISK_CATEGORIES_URL,

} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const RiskCategoryDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [data, setData] = useState([]);


    const [selected, setSelected] = useState(incidentData.riskCategory ? incidentData.riskCategory.id ?? '' : '');


    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(RISK_CATEGORIES_URL)
            .then(response => {
                setData(response.data);
            })
            .catch(error => {
                console.error('Error fetching risk categories', error);
            });
    }, []);


    return (
        <div className=''>

            <div className='form-group'>
                <label className=''> Risk Categories (GMS) <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" disabled={readOnly} value={selected} onChange={(e) => { setSelected(e.target.value); setIncidentData((prev) => ({ ...prev, riskCategoryId: e.target.value })) }}>
                    <option value="">Select</option>
                    {data.map(i => (
                        <option key={i.id} value={i.id}>{i.name}</option>
                    ))}
                </select>
            </div>






        </div>
    );
};

RiskCategoryDropdown.defaultProps = {
    readOnly: false
}
export default RiskCategoryDropdown;
