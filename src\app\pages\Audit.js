import React, { useState, useMemo, useEffect } from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import API from '../services/API';
import ActionCard from './Action/AuditAction';
import AuditList from './AuditList';
import AuditTable from './AuditTable';
import { CHECKLIST_URL, AUDIT_URL, ACTION_URL, MY_AUDIT_URL, AUDIT_FINDINGS_URL, AUDIT_LIST } from '../constants';
import AppSwitch from './AppSwitch'
const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: "flex",
    alignItems: 'center',
    justifyContent: 'center'
};

function CustomTabPanel(props) {
    const { children, value, tabValue, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== tabValue}
            id={`incident-tabpanel-${tabValue}`}
            aria-labelledby={`incident-tab-${tabValue}`}
            {...other}
        >
            {value === tabValue && (
                <Box>{children}</Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    tabValue: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
};

const Audit = () => {
    const [value, setValue] = useState("ACTION");
    const me = useSelector((state) => state.login.user);
    const [auditData, setAuditData] = useState([]);
    const [auditList, setAuditList] = useState([]);
    const [auditReport, setAuditReport] = useState([]);
    const [actionData, setActionData] = useState([]);
    const [rendered, setRendered] = useState(0);

    useEffect(() => {
        getAuditData();
        getAuditList();
        getAuditReport();
        getActionData();
    }, [rendered]);

    const getAuditData = async () => {
        const params = {
            include: [
                { relation: "assignedTo" },
                { relation: "locationOne" },
                { relation: "locationTwo" },
                { relation: "locationThree" },
                { relation: "locationFour" },
                { relation: "checklist" }
            ]
        };
        const response = await API.get(`${MY_AUDIT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            setAuditData(response.data.filter(item => item.status !== 'Submitted' && item.status !== 'Cancelled'));
        }
    };

    const getAuditList = async () => {
        const params = {
            include: [
                { relation: "assignedTo" },
                { relation: "locationOne" },
                { relation: "locationTwo" },
                { relation: "locationThree" },
                { relation: "locationFour" },
                { relation: "checklist" }
            ]
        };
        const response = await API.get(`${AUDIT_LIST}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            const transformedData = response.data
                .filter(item => item.status !== 'Submitted' && item.status !== 'Cancelled')
                .map(item => {
                    if (item.status === 'Initiated') {
                        return { ...item, status: 'Scheduled' };
                    } else if (item.status === 'Draft') {
                        return { ...item, status: 'Under Process' };
                    }
                    return item;
                })
                .reverse();
            setAuditList(transformedData);
        }
    };

    const getAuditReport = async () => {
        const params = {
            include: [
                { relation: "assignedTo" },
                { relation: "locationOne" },
                { relation: "locationTwo" },
                { relation: "locationThree" },
                { relation: "locationFour" },
                { relation: "checklist" }
            ]
        };
        const response = await API.get(`${AUDIT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            const submittedData = response.data.filter(item => item.status === 'Submitted').reverse();
            setAuditReport(submittedData);
        }
    };

    const getActionData = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {
            const act = response.data.filter(action =>
                action.application === 'AuditFinding' && ['open'].includes(action.status)
            );
            setActionData(act);
        }
    };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const combinedActionsCount = [...auditData, ...actionData].length;
    const combinedActions = [...auditData, ...actionData];

    return (
        <div className='m-4'>

            <AppSwitch value={{ label: 'Audit', value: 'audit' }} />
            <div className="row">
                <div className="col-12">
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <Tabs value={value} onChange={handleChange} aria-label="incident report table">
                            <Tab label={
                                <Typography variant="body1" style={customFontStyle}>
                                    My Actions <span className='headerCount'>{combinedActionsCount}</span>
                                </Typography>
                            } value="ACTION" />
                            <Tab label={
                                <Typography variant="body1" style={customFontStyle}>
                                    Audit Schedules <span className='headerCount'>{auditList.length}</span>
                                </Typography>
                            } value="ALL" />
                            <Tab label={
                                <Typography variant="body1" style={customFontStyle}>
                                    Audit Reports <span className='headerCount'>{auditReport.length}</span>
                                </Typography>
                            } value="UNDER_INVESTIGATION" />
                        </Tabs>
                    </Box>
                    <CustomTabPanel value={value} tabValue="ACTION">
                        <Box sx={{ width: '100%' }}>
                            <ActionCard action={combinedActions} applicationType="AuditFinding" setRendered={setRendered} />
                        </Box>
                    </CustomTabPanel>
                    <CustomTabPanel value={value} tabValue="ALL">
                        <AuditList data={auditList} setRendered={setRendered} />
                    </CustomTabPanel>
                    <CustomTabPanel value={value} tabValue="UNDER_INVESTIGATION">
                        <AuditTable data={auditReport} setRendered={setRendered} />
                    </CustomTabPanel>
                </div>
            </div>
        </div>
    );
};

export default Audit;
