import React, { useState, useEffect, useRef } from 'react';
import { Mo<PERSON>, But<PERSON>, Container, Row, Col, Card } from 'react-bootstrap';
import useForceUpdate from 'use-force-update';

const MultiWidget = ({ title = 'This is default title', mandatory = false, data = [{ type: 'add' }], getData, allowed = ['image', 'document'], noOfFiles = 5, maxLimitForPerFile = 1000000, maxLimitForTotalFiles = 5000000 }) => {
    const [items, setItems] = useState(data);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showImgOptionModal, setShowImgOptionModal] = useState(false);
    const [cameraModal, setCameraModal] = useState(false)
    const fileInputRef = useRef(null);
    const [cameraStream, setCameraStream] = useState(null);
    const [devices, setDevices] = useState([]);
    const [selectedDeviceId, setSelectedDeviceId] = useState('');
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const forceUpdate = useForceUpdate()

    useEffect(() => {

        if (!cameraModal && cameraStream !== null) {
            stopCamera()
        }
    }, [cameraModal])

    useEffect(() => {
        getData(items.filter((i) => { return i.type !== 'add' }))
        if (items.filter((i) => { return i.type !== 'add' }).length === noOfFiles) {

            items.pop()
        } else {

            if (items.filter((i) => { return i.type === 'add' }).length === 0) {

                items.push({ type: 'add' })
            }
        }
        forceUpdate()
    }, [items])

    const handleCameraSwitch = async () => {
        try {
            if (devices.length < 2) {

                return;
            }

            const currentIndex = devices.findIndex(device => device.deviceId === selectedDeviceId);
            const nextIndex = (currentIndex + 1) % devices.length;
            const nextDeviceId = devices[nextIndex].deviceId;
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
            }
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { deviceId: { exact: nextDeviceId } },
            });

            setCameraStream(stream);
            setSelectedDeviceId(nextDeviceId);
            videoRef.current.srcObject = stream;
        } catch (error) {
            console.error('Error switching camera:', error);
        }
    };
    const handleAdd = (type) => {
        if (type === 'image' && showAddModal) {
            fileInputRef.current.accept = 'image/*';
            setShowAddModal(false);
            setShowImgOptionModal(true);
        } else if (type === 'camera') {
            setShowImgOptionModal(false);
            setCameraModal(true);
            startCamera();
        } else if (type === 'image' && !showAddModal) {
            fileInputRef.current.accept = 'image/*';
            setShowAddModal(false);
            setShowImgOptionModal(false);
            fileInputRef.current.click();
        } else if (type === 'document') {
            fileInputRef.current.accept = ''; // This allows all file types
            fileInputRef.current.click();
        }
        forceUpdate();
    };

    const startCamera = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } });
            setCameraStream(stream);
            videoRef.current.srcObject = stream;

            const availableDevices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = availableDevices.filter(device => device.kind === 'videoinput');
            setDevices(videoDevices);

            if (videoDevices.length > 1) {

                setSelectedDeviceId(videoDevices[1].deviceId);
            }
        } catch (error) {
            console.error('Error starting camera:', error);
        }
    };
    const deleteItem = (index) => {
        let item_ = JSON.parse(JSON.stringify(items))
        item_.splice(index, 1)
        setItems(item_)
        forceUpdate()
    }
    const stopCamera = () => {
        if (cameraStream) {

            cameraStream.getTracks().forEach(track => track.stop());
            setCameraStream(null);
        }
    };
    const getTotalSize = () => {
        return items.filter((i) => { return i.type !== 'add' }).reduce((a, b) => {
            if (typeof b.size === 'number') {
                return a + b.size;
            }
            return a + 0;
        }, 0);
    }
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    const handleCapturePhoto = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
            const dataURL = canvas.toDataURL('image/jpeg');
            if ((maxLimitForTotalFiles >= (getTotalSize() + dataURL.length)) && (maxLimitForPerFile >= dataURL.length)) {

                const newItem = {
                    type: 'image',
                    url: dataURL,
                    file: dataURItoFile(dataURL, 'captured-image.jpg'),
                    name: 'captured-image.jpg',
                    size: dataURL.length,
                };

                setItems([...items.slice(0, -1), newItem, { type: 'add' }]);

            }

            setCameraModal(false);
            stopCamera();
        }
    };

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];

       
            const newItem = {
                type: selectedFile.type.includes('image') ? 'image' : 'document',
                url: URL.createObjectURL(selectedFile),
                file: e.target.files[0],
                name: selectedFile.name,
                size: selectedFile.size,
            };

            setItems([...items.slice(0, -1), newItem, { type: 'add' }]);
            setShowAddModal(false);
            setShowImgOptionModal(false);
            fileInputRef.current.value = '';
       
    };

    return (
        <div className='p-0'>
            <Card className='p-3' style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                />
                <Col className='d-flex justify-content-start text-start mb-3'>
                    {title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}
                </Col>
                <Row>
                    {items.map((item, index) => (
                        <Col xs={4} sm={4} md={4} className="mb-3 square-quarter-screen" key={index}>
                            {item.type === 'add' ? (
                                <Container
                                    className='d-flex justify-content-center align-items-center'
                                    variant="outline-primary"
                                    onClick={() => { !allowed.includes('document') ? setShowImgOptionModal(true) : setShowAddModal(true) }}
                                    style={{ width: '100%', height: '25vw', color: '#005284', border: '2px dashed #005284', fontSize: 30, borderRadius: 10 }}
                                >
                                    +
                                </Container>
                            ) : (
                                <Card className="square-card">
                                    {item.type === 'image' ? (
                                        <Card.Img variant="top" src={item.url} className="square-img" />
                                    ) : (
                                        <div className="d-flex justify-content-center align-items-center" style={{ height: '100%' }}>
                                            {/* Show a document icon */}
                                            <i className="pi pi-file" style={{ fontSize: '50px', color: '#005284' }}></i>
                                        </div>
                                    )}

                                    {/* Display file name */}
                                    <Card.Body className="text-center">
                                        <small>{item.name}</small>
                                    </Card.Body>

                                    <Col style={{ position: 'absolute', right: -1, top: -1 }}>
                                        <i className='material-icons' style={{ color: 'white', padding: 2, background: '#D62828' }} onClick={() => { deleteItem(index) }}>close</i>
                                    </Col>
                                </Card>
                            )}
                        </Col>
                    ))}
                </Row>


                <Row className='font-bold' style={{ color: '#005284' }} >
                    <Col xs={4} sm={4} md={4} className='d-flex justify-content-start'>
                        {/* {items.filter((i) => { return i.type !== 'add' }).length} / Max. {noOfFiles} */}
                    </Col>
                    <Col xs={8} sm={8} md={8} className='d-flex justify-content-end'>
                        100 MB
                    </Col>
                </Row>
            </Card>
            <Modal show={showAddModal} onHide={() => setShowAddModal(false)}>
                <Modal.Header closeButton>
                    <Modal.Title>Select File Type</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Container>
                        <Row className='justify-content-center align-items-center'>

                            {allowed.includes('image') && <Col xs={4} className="mb-4"  >
                                <Card className='p-2 align-items-center' onClick={() => handleAdd('image')}>
                                    <i

                                        className='material-icons'

                                        style={{ fontSize: 30, color: '#005284' }}
                                    >
                                        photo_camera
                                    </i>
                                    Image
                                </Card>
                            </Col>}
                            {allowed.includes('document') && <Col xs={4} className="mb-4">
                                <Card className='p-2 align-items-center' onClick={() => handleAdd('document')}>
                                    <i

                                        className='material-icons'

                                        style={{ fontSize: 30, color: '#005284' }}
                                    >
                                        description
                                    </i>
                                    File
                                </Card>
                            </Col>
                            }
                        </Row>
                    </Container>


                </Modal.Body>
            </Modal>
            <Modal show={showImgOptionModal} onHide={() => setShowImgOptionModal(false)}>
                <Modal.Header closeButton>

                </Modal.Header>
                <Modal.Body>
                    <Container>
                        <Row className='justify-content-center align-items-center'>
                            {/* 
                            <Col xs={5} className="mb-4">
                                <Card className='p-2 align-items-center' onClick={() => handleAdd('camera')}>
                                    <i

                                        className='material-icons'

                                        style={{ fontSize: 30, color: '#005284' }}
                                    >
                                        camera
                                    </i>
                                    Take Photo
                                </Card>
                            </Col> */}
                            <Col xs={5} className="mb-4">
                                <Card className='p-2 align-items-center' onClick={() => handleAdd('image')}>
                                    <i

                                        className='material-icons'

                                        style={{ fontSize: 30, color: '#005284' }}
                                    >
                                        image
                                    </i>
                                    Device
                                </Card>
                            </Col>
                        </Row>
                    </Container>


                </Modal.Body>
            </Modal>
            <Modal show={cameraModal} onHide={() => { setCameraModal(false) }}>
                <Modal.Header closeButton>

                </Modal.Header>
                <Modal.Body>
                    <Container>
                        <Row>
                            <Col>

                                <div>
                                    <video ref={videoRef} autoPlay playsInline width={'100%'} />
                                    {/* {devices.length > 1 && (
                                        <Button variant="primary" onClick={handleCameraSwitch}>
                                            Switch Camera
                                        </Button>
                                    )}
                                    <Button variant="primary" onClick={handleCapturePhoto}>
                                        Capture Photo
                                    </Button>
                                    <Button variant="primary" onClick={() => { setCameraModal(false) }}>
                                        Cancel
                                    </Button> */}
                                </div>

                                <canvas ref={canvasRef} style={{ display: 'none' }} />
                            </Col>
                        </Row>
                    </Container>


                </Modal.Body>
                <Modal.Footer>
                    <Row className='d-flex' style={{ width: '100%' }}>
                        <Col></Col>
                        <Col className='text-center'>
                            {/* <img src={require('../assets/images/camera_685655.png')} style={{ width: 50 }} alt='test' onClick={handleCapturePhoto} /> */}
                        </Col>
                        <Col className='text-center'>
                        </Col>
                    </Row>

                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default MultiWidget;
