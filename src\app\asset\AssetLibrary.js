import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

function AssetLibrary() {
    const [data, setData] = useState([
        {
            "ID": "#230912-79",
            "Category": "Industrial Asset",
            "Description": "Nostrum Quia Eum",
            "Status": "Available"
        },
        {
            "ID": "#230912-78",
            "Category": "Technology Asset",
            "Description": "Nisi Velit Porro",
            "Status": "Unavailable"
        },
        {
            "ID": "#230912-77",
            "Category": "Lorem ipsum Asset",
            "Description": "Voluptatibus Deleniti Non",
            "Status": "Under Service"
        },
        {
            "ID": "#230912-76",
            "Category": "Draft Asset",
            "Description": "Atque Mollitia Reprehenderit",
            "Status": "Active"
        },
        {
            "ID": "#230912-75",
            "Category": "Temporary Asset",
            "Description": "Magni Suscipit Blanditiis",
            "Status": "Alerting"
        },
        {
            "ID": "#230912-79",
            "Category": "Industrial Asset",
            "Description": "Nostrum Quia Eum",
            "Status": "Rented Out"
        },
        {
            "ID": "#230912-78",
            "Category": "Technology Asset",
            "Description": "Nisi Velit Porro",
            "Status": "Unavailable"
        },
        {
            "ID": "#230912-77",
            "Category": "Lorem ipsum Asset",
            "Description": "Voluptatibus Deleniti Non",
            "Status": "Under Service"
        },
        {
            "ID": "#230912-76",
            "Category": "Draft Asset",
            "Description": "Atque Mollitia Reprehenderit",
            "Status": "Active"
        },
        {
            "ID": "#230912-75",
            "Category": "Temporary Asset",
            "Description": "Magni Suscipit Blanditiis",
            "Status": "Decomissioned/Awaiting Disposal"
        }
    ]);

    const getStatusStyle = (status) => {
        switch (status) {
            case 'Available':
                return { backgroundColor: '#d4edda', color: '#155724' };
            case 'Unavailable':
                return { backgroundColor: '#f8d7da', color: '#721c24' };
            case 'Under Service':
                return { backgroundColor: '#fff3cd', color: '#856404' };
            case 'Active':
                return { backgroundColor: '#d1ecf1', color: '#0c5460' };
            case 'Alerting':
                return { backgroundColor: '#f8d7da', color: '#721c24' };
            case 'Rented Out':
                return { backgroundColor: '#cce5ff', color: '#004085' };
            case 'Decomissioned/Awaiting Disposal':
                return { backgroundColor: '#f8d7da', color: '#721c24' };
            default:
                return {};
        }
    };

    const statusBodyTemplate = (rowData) => {
        return (
            <span style={getStatusStyle(rowData.Status)}>
                {rowData.Status}
            </span>
        );
    };

    return (
        <div>
            <DataTable value={data} paginator rows={10} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found.">
                <Column field='ID' header='AssetID' filter showFilterMatchModes={false}></Column>
                <Column field='Category' header="Asset" sortable></Column>
                <Column field='Description' header="Make/ Model" sortable></Column>
                <Column field="Status" header="Status" filter showFilterMatchModes={false} body={statusBodyTemplate}></Column>
            </DataTable>
        </div>
    );
}

export default AssetLibrary;
