import { useEffect } from 'react';
import React from 'react';
import { Composed<PERSON>hart, ReferenceLine, BarChart, Bar, Label, LineChart, Line, XAxis, YAxis, LabelList, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

export default function BarLineChart({ doubleAxis, barColor, data, barKey, lineKey, statsFrom2022, barLegend, lineLegend }) {
    // Your chart data
    const CustomLabel = ({ y }) => {
        const customY = y + 400; // Adjusting Y position as necessary
        return (
            <text transform={`rotate(270, ${50}, ${customY})`}
                textAnchor="middle" // Centers the text horizontally around the x coordinate
                dominantBaseline="middle" // Centers the text vertically around the y coordinate
                x={50} y={customY} fill="gray">
                <tspan x={50} dy="0">2022 Avg</tspan>
                {/* Moves to the next line by adjusting dy, "em" units roughly correspond to the current font size */}
                <tspan x={50} dy="1.2em">(1.14)</tspan>
            </text>
        );
    };


    return (
        <div style={{ width: '100%', height: 500 }} className='mb-5'>

            <ResponsiveContainer>
                <ComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" tickFormatter={(tickItem) => tickItem.charAt(0).toUpperCase() + tickItem.slice(1)} />

                    {
                        doubleAxis && <>
                            <YAxis yAxisId="left" orientation='left' >
                                <Label value={lineLegend} offset={0} position="insideLeft" angle={-90} style={{ textAnchor: 'middle' }} />

                            </YAxis>
                            <YAxis yAxisId="right" allowDecimals={false} orientation='right' >
                                <Label value={barLegend} offset={50} position="insideLeft" angle={-90} style={{ textAnchor: 'middle' }} />

                            </YAxis>

                        </>
                    }

                    {
                        !doubleAxis && <>

                            <YAxis >
                                <Label value={barLegend} offset={0} position="insideLeft" angle={-90} style={{ textAnchor: 'middle' }} />

                            </YAxis>

                        </>
                    }
                    <Tooltip />
                    <Legend />

                    {
                        doubleAxis && <>
                            <Bar yAxisId="right" name={barLegend} dataKey={barKey} barSize={20} fill={barColor}>
                                <LabelList dataKey={barKey} position="top" />
                            </Bar>
                            <Line yAxisId="left" type="monotone" name={lineLegend} dataKey={lineKey} stroke="#b9491e" label={{ position: 'top' }} />
                        </>
                    }

                    {
                        !doubleAxis && <>
                            <Bar name={barLegend} dataKey={barKey} barSize={20} fill={barColor}>
                                <LabelList dataKey={barKey} position="top" />
                            </Bar>
                            <Line type="monotone" name={lineLegend} dataKey={lineKey} stroke="#b9491e" label={{ position: 'top' }} />

                        </>
                    }

                    {statsFrom2022 &&
                        <ReferenceLine
                            y={statsFrom2022}
                            stroke="gray"
                            label={<CustomLabel x={100} y={statsFrom2022} value="Custom Label" />}
                        />
                    }
                </ComposedChart>
            </ResponsiveContainer>
        </div>
    );
}
