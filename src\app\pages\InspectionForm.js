import React, { useEffect, useState } from "react";
import moment from 'moment';
import { Modal } from 'react-bootstrap';
import API from "../services/API";
import { GET_USERS_BY_ROLE, INSPECTOR_USERS_URL } from "../constants";
import ReactDatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";


const InspectionForm = ({ show, onHide, month, year, checklist, location, onSubmit }) => {
    // Local state for form inputs

    useEffect(() => {console.log(location, 'check')}, [location])
    const [formState, setFormState] = useState({
        month: month,
        checklistId: checklist.id,
        assignedToId: '',
        name: '',
        dateTime: '',
        created: moment(new Date()).format('DD/MM/YYYY')
    });
    const startDate = moment(`${month} ${year}`, 'MMM YYYY').startOf('month').toDate();
    const endDate = moment(`${month} ${year}`, 'MMM YYYY').endOf('month').toDate();

    const [inspector, setInspector] = useState([])
    useEffect(() => {
        getInspector()
    }, [])
    const handleDateChange = (date) => {
        setFormState(prev => ({ ...prev, dateTime: moment(date).format('DD/MM/YYYY') }));
    };
    const getInspector = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, {
            locationOneId: location.locationOneId,
            locationTwoId: location.locationTwoId,
            locationThreeId: location.locationThreeId,
            locationFourId: location.locationFourId,
            mode: 'inspector'

        });
        if (response.status === 200) {
            setInspector(response.data)
        }
    }
    const handleSubmit = () => {
        // You might want to validate the formState here before calling onSubmit
        setFormState((prev) => { return { ...prev, month: month, checklistId: checklist.id } })
        onSubmit(formState);
        onHide(); // Hide the modal after submit
    };

    return (
        <Modal show={show} onHide={onHide}>
            <Modal.Header closeButton>
                <Modal.Title>Schedule Inspection - {checklist.name}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {/* Your form inputs go here */}

                <form>
                    {/* Assume you have a form state setup to handle these inputs */}
                    <div className="form-group">
                        <label>Type of Inspection </label>
                        <p>{checklist.name}</p>
                    </div>
                    <div className="form-group">
                        <label>Schedule Inspection</label>
                        <br />
                        <ReactDatePicker
                            className="form-control"
                            onChange={handleDateChange}
                            minDate={startDate}
                            maxDate={endDate}
                            dateFormat="dd/MM/yyyy"
                            value={formState.dateTime}
                        />
                    </div>
                    <div className="form-group">
                        <label>Assign User</label>
                        <select onChange={(e) => setFormState(prev => { return { ...prev, assignedToId: e.target.value } })} className="form-select">
                            <option value="">Assign User</option>
                            {
                                inspector.map(i => {
                                    return <option key={i.id} value={i.id}>{i.firstName}</option>
                                })
                            }
                        </select>
                    </div>

                    {/* Add other inputs here */}
                </form>
            </Modal.Body>
            <Modal.Footer>
                <button className="btn btn-light" onClick={onHide}>Close</button>
                <button className="btn btn-primary" onClick={handleSubmit}>Submit</button>
            </Modal.Footer>
        </Modal>
    );
};


export default InspectionForm;