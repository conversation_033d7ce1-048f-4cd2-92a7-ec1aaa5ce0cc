// @ts-nocheck
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { Modal, Button, Form } from 'react-bootstrap';
import cogoToast from 'cogo-toast';
import Loader from '../shared/Loader';
import Swal from 'sweetalert2';
import { useHistory } from "react-router-dom";
import { EDIT_TIER_URL, EHS_ROLE_URL, EPTW_ROLE_URL, GROUP_EHS_ROLE_URL, REPORT_ROLE_URL, EXTERNAL_USERS_URL, GET_USER_LOCATION_ROLE_URL, INCIDENT_ROLE_URL, INSPECTION_ROLE_URL, INTERNAL_USERS_URL, LOCATION1_URL, PLANT_ROLE_URL, UPDATE_BULK_USER_URL, USERS_URL, USERS_URL_WITH_ID, USER_LOCATION_ROLE_URL, USER_LOCATION_URL } from '../constants';
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import UserSelection from './UserSelection';
import FilterLocation from './FilterLocation';
import UserSelect from './UserSelect';
import Select from 'react-select';
import UserTable from './UserTable'
import AllFilterLocation from './AllFilterLocation';
import { useSelector } from 'react-redux'
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;



const customSwal = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-light'
    },
    buttonsStyling: false
})

const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: 'btn btn-primary',

    },
    buttonsStyling: false
})

const RoleUser = () => {

    const [allUsers, setAllUsers] = useState([]);
    const [selectedUsersList, setSelectedUsersList] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [selectedRoles, setSelectedRoles] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedOption, setSelectedOption] = useState(null);
    const me = useSelector(state => state.login.user);

    const requestInProgress = useRef(false);
    const abortController = useRef(null);

    const [allRoles, setAllRoles] = useState({ country: [], ehs: [], eptw: [], incident: [], inspection: [], plant: [], groupEhs: [], report: [] })


    function handleRoleSelect(selectedOptions) {
        setSelectedRoles(selectedOptions);
    }

    const roleOptions = useMemo(() => {
        return Object.entries(allRoles)
            .filter(([key]) => key !== 'country')
            .map(([key, options]) => ({
                label: key === 'ehs' ? 'Observation' : key,
                options: options.map(option => ({
                    value: option.id,
                    label: option.name
                }))
            }));
    }, [allRoles]);

    useEffect(() => {
        getEhsRole();
        getEptwRole();
        getIncidentRole();
        getInspectionRole();
        getPlantRole();
        getGroupEhsRole();
        getReportRole();

        getAllUsers();

        return () => {
            if (abortController.current) {
                abortController.current.abort();
            }
        };

    }, [])




    const getAllUsers = async () => {
        const response = await API.get(USERS_URL)

        if (response.status === 200) {

            setAllUsers(response.data.filter(i => i.status !== false).sort((a, b) => a.firstName.toLowerCase().localeCompare(b.firstName.toLowerCase())))

        }
    }

    const getEhsRole = async () => {
        const response = await API.get(EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, ehs: response.data } })
        }
    }

    const getEptwRole = async () => {
        const response = await API.get(EPTW_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, eptw: response.data } })
        }
    }

    const getIncidentRole = async () => {
        const response = await API.get(INCIDENT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, incident: response.data } })
        }
    }

    const getInspectionRole = async () => {
        const response = await API.get(INSPECTION_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, inspection: response.data } })
        }
    }

    const getPlantRole = async () => {
        const response = await API.get(PLANT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, plant: response.data } })
        }
    }


    const getGroupEhsRole = async () => {
        const response = await API.get(GROUP_EHS_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, groupEhs: response.data } })
        }
    }

    const getReportRole = async () => {
        const response = await API.get(REPORT_ROLE_URL)

        if (response.status === 200) {

            setAllRoles(p => { return { ...p, report: response.data } })
        }
    }



    const [selectedLocationOne, setSelectedLocationOne] = useState('')
    const [selectedLocationTwo, setSelectedLocationTwo] = useState('')
    const [selectedLocationThree, setSelectedLocationThree] = useState('')
    const [selectedLocationFour, setSelectedLocationFour] = useState('')


    const getSelectedUsers = useCallback(async () => {
        if (!selectedLocationOne || !selectedRoles || selectedRoles.length === 0) {
            setSelectedUsersList([]);
            setIsLoading(false);
            return;
        }

        // Prevent multiple simultaneous requests
        if (requestInProgress.current) {
            if (abortController.current) {
                abortController.current.abort();
            }
        }

        requestInProgress.current = true;
        abortController.current = new AbortController();
        setIsLoading(true);

        try {
            const locations = {
                locationOne: selectedLocationOne,
                locationTwo: selectedLocationTwo,
                locationThree: selectedLocationThree,
                locationFour: selectedLocationFour
            }
            const modifiedSelectedRoles = selectedRoles.map(i => i.value);
            const response = await API.post(GET_USER_LOCATION_ROLE_URL, {
                roles: modifiedSelectedRoles,
                locations: locations,
                condition: 'AND' // Use AND condition to get users with all selected roles
            }, {
                signal: abortController.current.signal
            });

            if (response.status === 200 && !abortController.current.signal.aborted) {
                setSelectedUsersList(response.data);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error fetching selected users:', error);
                setSelectedUsersList([]);
            }
        } finally {
            requestInProgress.current = false;
            setIsLoading(false);
        }
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedRoles]);


    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (selectedLocationOne && selectedRoles && selectedRoles.length > 0) {
                getSelectedUsers();
            } else {
                setSelectedUsersList([]);
                setIsLoading(false);
            }
        }, 500); // Increased debounce to 500ms for better stability

        return () => clearTimeout(timeoutId);
    }, [selectedLocationOne, selectedLocationTwo, selectedLocationThree, selectedLocationFour, selectedRoles, getSelectedUsers]);

    const handleFilter = useCallback((locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        setSelectedLocationOne(locationOneId);
        setSelectedLocationTwo(locationTwoId);
        setSelectedLocationThree(locationThreeId);
        setSelectedLocationFour(locationFourId);
    }, []);


    const [locationOneTitle, setLocationOneTitle] = useState('')

    const getLocationOneDetail = useCallback(async () => {
        const response = await API.get(EDIT_TIER_URL('country', selectedLocationOne))
        if (response.status === 200) {
            setLocationOneTitle(response.data.name)
        }
    }, [selectedLocationOne]);

    useEffect(() => {
        if (selectedLocationOne && selectedLocationOne !== 'tier1-all')
            getLocationOneDetail()
        else
            setLocationOneTitle('All')
    }, [selectedLocationOne, getLocationOneDetail])

    const [locationTwoTitle, setLocationTwoTitle] = useState('')

    const getLocationTwoDetail = useCallback(async () => {
        const response = await API.get(EDIT_TIER_URL('tier1', selectedLocationTwo))
        if (response.status === 200) {
            setLocationTwoTitle(response.data.name)
        }
    }, [selectedLocationTwo]);

    useEffect(() => {
        if (selectedLocationTwo && selectedLocationTwo !== 'tier2-all')
            getLocationTwoDetail()
        else
            setLocationTwoTitle('All')
    }, [selectedLocationTwo, getLocationTwoDetail])

    const [locationThreeTitle, setLocationThreeTitle] = useState('')

    const getLocationThreeDetail = useCallback(async () => {
        const response = await API.get(EDIT_TIER_URL('tier2', selectedLocationThree))
        if (response.status === 200) {
            setLocationThreeTitle(response.data.name)
        }
    }, [selectedLocationThree]);

    useEffect(() => {
        if (selectedLocationThree && selectedLocationThree !== 'tier3-all')
            getLocationThreeDetail()
        else
            setLocationThreeTitle('All')
    }, [selectedLocationThree, getLocationThreeDetail])

    const [locationFourTitle, setLocationFourTitle] = useState('')

    const getLocationFourDetail = useCallback(async () => {
        const response = await API.get(EDIT_TIER_URL('tier3', selectedLocationFour))
        if (response.status === 200) {
            setLocationFourTitle(response.data.name)
        }
    }, [selectedLocationFour]);

    useEffect(() => {
        if (selectedLocationFour && selectedLocationFour !== 'tier4-all')
            getLocationFourDetail()
        else
            setLocationFourTitle('All')
    }, [selectedLocationFour, getLocationFourDetail])


    const handleSelectedUsers = async (users, deselectedUsers) => {
        if (selectedLocationOne && selectedLocationTwo && selectedLocationThree && selectedLocationFour) {

            const userIds = users.map(i => { return i.id });
            const locations = {
                locationOne: selectedLocationOne,
                locationTwo: selectedLocationTwo,
                locationThree: selectedLocationThree,
                locationFour: selectedLocationFour
            }
            const modifiedSelectedRoles = selectedRoles.map(i => i.value)
            const response = await API.post(USER_LOCATION_ROLE_URL, { userIds: userIds, deselectUserIds: deselectedUsers, roles: modifiedSelectedRoles, locations: locations })
            // console.log(response)
            if (response.status === 200) {
                cogoToast.success('Assigned')
            }
        } else {
            cogoToast.error('Please select any one projects to continue!')
        }
        // console.log(users)
    }
    return (
        <>
            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-body">

                                <h4 className="card-title">User Report Export</h4>
                                <div className="row">
                                    <div className="col-12">

                                        <div className='mb-4'>
                                            <AllFilterLocation handleFilter={handleFilter} countryRoles={me?.validationRoles} disableAll={true} period={false} />
                                        </div>

                                        {(selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && <div className='mb-4'>
                                            <label className='mb-2'>Choose Role</label>

                                            <Select
                                                className="w-25"
                                                value={selectedRoles}
                                                onChange={handleRoleSelect}
                                                options={roleOptions}
                                                isMulti
                                                placeholder="Select Roles"
                                                isClearable
                                                closeMenuOnSelect={false}
                                            />
                                        </div>}

                                        <div className='row'>
                                            {(selectedLocationOne === 'tier1-all' || selectedLocationTwo === 'tier2-all' || selectedLocationThree === 'tier3-all' || selectedLocationFour) && <div className='col-6'>
                                                {isLoading ? (
                                                    <div className="text-center">
                                                        <Loader />
                                                        <p>Loading users...</p>
                                                    </div>
                                                ) : (
                                                    <UserTable
                                                        data={selectedUsersList}
                                                        customTitle={`Location ${locationOneTitle} > ${locationTwoTitle} > ${locationThreeTitle} > ${locationFourTitle} |  Role: ${selectedRoles?.map(role => role.label).join(' AND ') || 'None'}`}
                                                    />
                                                )}
                                            </div>}
                                            {/* <div className='col-6'>

                                                <h3>Selected Users:</h3>
                                                <ul>
                                                    {Object.entries(roleUsersMap).map(([role, users]) => (
                                                        <li key={role}>
                                                            <h4>{findSelectedRole(role)?.name}:</h4>
                                                            <ul>
                                                                {users.map((user) => (
                                                                    <li key={user.id}>
                                                                        {user.firstName} ({user.email})
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div> */}
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



        </>
    )
}


export default RoleUser;
