import React, { Component } from 'react'
import { ProgressBar } from 'react-bootstrap';

const BasicTable = (props) => {

  return (
    <div>
      <div className="row">
        <div className="col-lg-12 grid-margin stretch-card">
          <div className="card">
            <div className="card-body">
              <h4 className="card-title">{props.title}</h4>


              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>

                      {
                        props.columns.map((i, k) => {
                          return (
                            <th key={k}>
                              {i}
                            </th>
                          )
                        })
                      }

                    </tr>
                  </thead>
                  <tbody>

                    {
                      props.data.map((i, k) => {
                        return (
                          <tr key={k}>
                            {
                              i.map((j, k2) => {

                               return <td key={k2}>{j}</td>


                              })
                            }


                          </tr>
                        )
                      })
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>




      </div>
    </div>
  )

}

export default BasicTable
