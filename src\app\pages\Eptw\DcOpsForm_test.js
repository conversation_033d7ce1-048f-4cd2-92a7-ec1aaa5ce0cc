import React, { useState, useEffect, useRef } from "react";
import { Container, Row, Col, Card, Form, InputGroup, Modal, Button, Image } from "react-bootstrap";
import { DYNAMIC_TITLES_URL, LOCATION1_URL, LOCATION_TWO, LOCATION_THREE, LOCATION_FOUR, DCSO_HIGN_RISK_ASSESSOR, LOCATION_FIVE, LOCATION_SIX, DCSO_APPROVER_LIST, PERMIT_REPORT, DCSO_HIGH_RISK_APPROVER, FILE_URL, EPTW_CHECKLIST } from '../../constants'
import ReactDatePicker from "react-datepicker";
import API from "../../services/API";
import "react-datepicker/dist/react-datepicker.css";
import useForceUpdate from "use-force-update";
// import { useNavigate } from "react-router-dom";
import SelectCardComponent from "./component/SelectCardComponent";
import MultiWidget from "./component/MultiWidget";
import <PERSON>wal from "sweetalert2";
import YesNo from "./component/YesNo";
import SignatureCan<PERSON> from 'react-signature-canvas'
import axios from "axios";
import { useSelector } from "react-redux";
import moment from "moment";
import FullLoader from "./component/FullLoader";
import AllFilterLocation from "../AllFilterLocation";
import {
    addHours,
    isSameDay,
    setHours,
    setMinutes,
    getHours,
    getMinutes,
    isAfter,
    isEqual,
    isBefore,
} from 'date-fns';

const NewEPTWDCO = ({ show, handleClose }) => {
    const user = useSelector((state) => state.login.user);
    console.log(user)
    const sign = useRef()
    // const permitChecklist = require('./component/sttPtwData.json')
    const [permitChecklist, setPermitChecklist] = useState([])
    const [locationOne, setLocationOne] = useState([])
    const [title, setTitle] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [locationFive, setLocationFive] = useState([])
    const [locationSix, setLocationSix] = useState([])
    const [locationSearchOne, setLocationSearchOne] = useState([])
    const [locationSearchTwo, setLocationSearchTwo] = useState([])
    const [locationSearchThree, setLocationSearchThree] = useState([])
    const [locationSearchFour, setLocationSearchFour] = useState([])
    const [locationSearchFive, setLocationSearchFive] = useState([])
    const [locationSearchSix, setLocationSearchSix] = useState([])

    const [locOne, setLocOne] = useState('')
    const [locTwo, setLocTwo] = useState('')
    const [locThree, setLocThree] = useState('')
    const [locFour, setLocFour] = useState('')
    const [locFive, setLocFive] = useState('')
    const [locSix, setLocSix] = useState('')

    const [signs, setSign] = useState('')
    const [isDcEnergy, setIsDcEnergy] = useState('')
    const [isWah, setisWah] = useState('')
    const [isConfined, setisConfined] = useState('')
    const [isLifting, setisLifting] = useState('')
    const [searchModal, setSearchModal] = useState(false)
    const [systemSecurity, setSystemSecurity] = useState('')
    const [fireAlarm, setFireAlarm] = useState('')
    const [highRisk, setHighRisk] = useState('')
    const [checklist, setChecklist] = useState({})
    const [MOPTitle, setMOPTitle] = useState('')
    const [ticketNo, setTicketNo] = useState('')
    const [applicationContactNo, setApplicantContactNo] = useState('')
    const [companyContactNo, setCompanyContactNo] = useState('')
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [workDescription, setWorkDescription] = useState('')
    const [attachment, setAttachDocument] = useState([])
    const [ref, setRef] = useState([])
    const [highriskAssessor, setHighRiskAssesser] = useState([])
    const [refValue, setRefValue] = useState('')
    const [highriskAssessorValue, setHighRiskAssesserValue] = useState('')
    const [permit, setPermit] = useState([{ id: 1, label: 'Work at Height', checked: '' }, { id: 2, label: 'Confined Space', checked: '' }, { id: 3, label: 'Energised System', checked: '' }, { id: 4, label: 'Hot Works', checked: '' },
    { id: 5, label: 'Lifting Operation', checked: '' }, { id: 6, label: 'Use of Suspended PVAE', checked: '' }, { id: 7, label: 'Demolition', checked: '' }, { id: 8, label: 'Ground Disturbance', checked: '' },
    { id: 9, label: 'Hoist/Mast Climber', checked: '' }, { id: 10, label: 'Penetrations, Shafts, Risers & Voids', checked: '' }, { id: 11, label: 'Piling Works', checked: '' }],)

    const [energySysLocations, setEnergySysLocations] = useState([{ systems: [{ label: 'Electrical', checked: 0 }, { label: 'Mechanical', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}, systemName: [] }])
    const [energySysLocList, setEnergySysLocLost] = useState([[]])

    const [fireSysMore, setFireSysMore] = useState([
        { label: 'Risk of fire impairment is adequately controlled in accordance with the Hierarchy of Risk Control', checked: false, attachment: '', uploads: [] },
        { label: 'Site specific emergency response procedure is in place', checked: false, attachment: 'Attach the procedure for verification', uploads: [] },
        { label: 'Trained and competent Fire Watchman is on standby and aware of the nearest firefighting equipment located onsite.', checked: false, attachment: 'Attach certificate for verification / company letter of appointment', uploads: [] },
        { label: 'Fire doors are to be always kept close, unless it is listed in Section 6 - Security System Isolation', checked: '', attachment: false, uploads: [] }
    ])

    const [listbk, setListBK] = useState([{ name: 'India' }, { name: 'Singapore' }, { name: 'Srilanka' }])
    const [list, setList] = useState([{ name: 'India' }, { name: 'Singapore' }, { name: 'Srilanka' }])
    //
    const [fs, setFS] = useState([{ systems: [{ label: 'Pre-Action system', checked: 0 }, { label: 'Vesda', checked: 0 }, { label: 'Smoke Detector', checked: 0 }, { label: 'Gas Suppression', checked: 0 }, { label: 'Water Mist', checked: 0 }, { label: 'Wet-Sprinklers', checked: 0 }, { label: 'Fire Pumps', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: '', loc6: '' }])
    //
    // const [fs, setFS] = useState([{ systems: [{ label: 'Pre-Action system', checked: 0 }, { label: 'Vesda', checked: 0 }, { label: 'Smoke Detector', checked: 0 }, { label: 'Gas Suppression', checked: 0 }, { label: 'Water Mist', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: '', loc6: '' }])
    const [ss, setSS] = useState([{
        systems: [{ label: 'Door Alarm', checked: 0 }, { label: 'Door Access', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}
    }])
    const [requiredCheck, setRequiredCheck] = useState(true)
    const [loader, setLoader] = useState(false)
    const forceUpdate = useForceUpdate()

    const [zone, setZone] = useState([{ locationFive: {}, locationSix: {} }])

    const now = new Date();
    const maxStartDate = addHours(now, 24);

    function getStartDateMinTime() {
        if (isSameDay(now, startDate || now)) {
            return now;
        } else {
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getStartDateMaxTime() {
        if (isSameDay(maxStartDate, startDate || now)) {
            return maxStartDate;
        } else {
            return setHours(setMinutes(new Date(), 59), 23);
        }
    }

    function getEndDateMinTime() {
        if (!startDate) return setHours(setMinutes(new Date(), 0), 0);
        const start = startDate;
        const end = endDate || startDate;
        if (isSameDay(start, end)) {
            return start;
        } else {
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getEndDateMaxTime() {
        if (!startDate) return setHours(setMinutes(new Date(), 59), 23);
        const maxDateTime = addHours(startDate, 12);
        const end = endDate || startDate;
        if (isSameDay(maxDateTime, end)) {
            return maxDateTime;
        } else {
            return setHours(setMinutes(new Date(), getMinutes(maxDateTime)), getHours(maxDateTime));
        }
    }

    useEffect(() => {

        getTitle();
        getLocationOne();
        // getRef()
        // getHighRisk()
        getPermitChecklist()



    }, [])
    const getHighRisk = async () => {
        const response = await API.get(DCSO_HIGN_RISK_ASSESSOR);
        if (response.status === 200) {
            setHighRiskAssesser(response.data)
        }

    }
    const getPermitChecklist = async () => {
        const response = await API.get(EPTW_CHECKLIST);
        if (response.status === 200) {
            setPermitChecklist(response.data)
        }
    }
    // const getRef = async () => {
    //     const response = await API.get(DCSO_ASSESSOR_LIST);
    //     if (response.status === 200) {
    //         setRef(response.data)
    //     }

    // }
    const getTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        if (response.status === 200) {
            setTitle(response.data)
        }

    }
    const getLocationOne = async () => {
        const response = await API.get(LOCATION1_URL);
        if (response.status === 200) {
            setLocationOne(response.data)
            setLocationSearchOne(response.data)
        }

    }
    const onCloseOBS = () => {
        Swal.fire({
            title: '',
            text: "Are you sure you wish to close this session? All data entered here will be reset.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes'
        }).then((result) => {
            if (result.isConfirmed) {
                // navigate(-1)
                handleClose(false)

            }
        })
    }
    const buildReqData = (extraParams = {}) => {
        return {
            locationOneId: locOne.id,
            locationTwoId: locTwo.id,
            locationThreeId: locThree.id,
            locationFourId: locFour.id,
            ...extraParams
        };
    };

    const handleApiResponse = async (apiUrl, reqData, setDataFunction, idToFilter) => {
        try {
            const response = await API.post(apiUrl, reqData);
            if (response.status === 200) {
                const filteredData = response.data
                    .filter(i => i.id !== idToFilter) // Filter by specific ID
                    .map(i => ({ ...i, title: i.firstName })); // Add title based on firstName

                setDataFunction(filteredData);
            }
        } catch (error) {
            console.error(`Error fetching data from ${apiUrl}`, error);
        }
    };

    const getApproverAndRep = async () => {
        const idToFilter = user?.id;

        // Fetch DCSO High Risk Approver Data
        const reqDataApprover = buildReqData();
        await handleApiResponse(DCSO_HIGH_RISK_APPROVER, reqDataApprover, setRef, idToFilter);

        // Fetch DCSO High Risk Assessor Data
        const reqDataAssessor = buildReqData({
            isWah: isWah === 'Yes',
            isConfinedSpace: isConfined === 'Yes',
            isLifting: isLifting === 'Yes'
        });
        await handleApiResponse(DCSO_HIGN_RISK_ASSESSOR, reqDataAssessor, setHighRiskAssesser, idToFilter);
    };

    const selectCardComponent = (mandatory, title) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <Container onClick={() => { setSearchModal(true) }} fluid className="p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#00000060' }} > Select</Container>
                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>
        )
    }
    const selectComponent = (mandatory, title) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>

                <Row >
                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                        <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                    </Col>
                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                        <Container onClick={() => { setSearchModal(true) }} fluid className="p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#00000060' }} > Select</Container>
                    </Col>
                </Row>


            </Col>
        )
    }
    const tagComponent = (item) => {
        return (
            <div className="m-1" >
                <div onClick={() => { item.checked = item.checked === 0 ? 1 : 0; forceUpdate() }} className=" d-flex p-2" style={{ border: '1px solid #00000050', textWrap: 'nowrap', position: 'relative', borderRadius: '20px', width: 'fit-content', cursor: 'pointer' }}>
                    <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked === 1 ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                    {item.checked === 1 &&
                        <i className="material-icons" style={{ position: 'absolute', color: 'white' }} >check</i>

                    }
                    <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                </div>
                {item.label === 'Others' && item.checked === 1 &&
                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                        <InputGroup className="mb-3">
                            <Form.Control
                                placeholder=""
                                type="text"
                                onChange={(e) => item.others = e.target.value}
                            />

                        </InputGroup>
                    </Col>
                }
            </div>
        )
    }
    const textAreaCardComponent = (mandatory, title) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <InputGroup>

                                    <Form.Control style={{ height: 100 }} as="textarea" aria-label="With textarea" />
                                </InputGroup>
                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>
        )
    }
    const updateSelected = async (item, type) => {
        if (type === 'one') {
            setLocOne(item)
            setLocTwo('')
            setLocThree('')
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_TWO(item.id));
            if (response.status === 200) {
                setLocationTwo(response.data)
                setLocationSearchTwo(response.data)

            }
        } else if (type == 'two') {
            setLocTwo(item)
            setLocThree('')
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_THREE(item.id));
            if (response.status === 200) {
                console.log(response.data)
                console.log(item)
                if (item.name === 'Singapore') {
                    const filteredData = response.data.filter(item => item.name !== "Construction Projects");
                    setLocationThree(filteredData);
                    setLocationSearchThree(response.data)
                } else {
                    const filteredData = response.data.filter(item => item.name === "Data Center Operations");
                    setLocationThree(filteredData);
                    setLocationSearchThree(response.data)
                }


            }
        }
        else if (type == 'three') {
            setLocThree(item)
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_FOUR(item.id));
            if (response.status === 200) {


                setLocationFour(response.data)
                setLocationSearchFour(response.data)

            }
        }
        else if (type == 'four') {
            setLocFour(item)
            setLocFive('')

            setLocSix('')
            const response = await API.get(LOCATION_FIVE(item.id));
            if (response.status === 200) {
                setLocationFive(response.data)
                setLocationSearchFive(response.data)

            }
        }
        else if (type == 'five') {
            setLocFive(item)
            setLocSix('')
            const response = await API.get(LOCATION_SIX(item.id));
            if (response.status === 200) {
                setLocationSix(response.data)
                setLocationSearchSix(response.data)

            }
        }
        else if (type == 'six') {
            setLocSix(item)

        }


    }
    const searchList = (e) => {
        console.log(e.target.value)
        setList(listbk.filter((i) => { return i.name.toLowerCase().includes(e.target.value.toLowerCase()) }))
    }
    const addNewFS = () => {
        let locfs = JSON.parse(JSON.stringify(fs))
        locfs.push({ systems: [{ label: 'Pre-Action system', checked: 0 }, { label: 'Vesda', checked: 0 }, { label: 'Smoke Detector', checked: 0 }, { label: 'Gas Suppression', checked: 0 }, { label: 'Water Mist', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: '', loc6: '' })
        setFS(locfs)
    }
    const addNewZone = () => {
        let locfs = JSON.parse(JSON.stringify(zone))
        console.log(locfs)
        locfs.push({ locationFive: {}, locationSix: {} })
        setZone(locfs)
    }
    const deleteZone = (index) => {
        let locfs = JSON.parse(JSON.stringify(zone))
        locfs.splice(index, 1)
        setZone(locfs)
    }
    const deleteFS = (index) => {
        let locfs = JSON.parse(JSON.stringify(fs))
        locfs.splice(index, 1)
        setFS(locfs)
    }
    const addNewSS = () => {
        let locss = JSON.parse(JSON.stringify(ss))
        locss.push({
            systems: [{ label: 'Door Alarm', checked: 0 }, { label: 'Door Access', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}
        })
        setSS(locss)
    }
    const removeEnergySysLocation = (i) => {
        var tData = [...energySysLocations]
        tData.splice(i, 1)
        var t2Data = [...energySysLocList]
        t2Data.splice(i, 1)
        setEnergySysLocations(tData)
        setEnergySysLocLost(t2Data)
    }

    const addEnergySysLocation = () => {
        setEnergySysLocations([...energySysLocations, { systems: [{ label: 'Electrical', checked: 0 }, { label: 'Mechanical', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}, systemName: [] }])
        setEnergySysLocLost([...energySysLocList, []])
    }

    const removeEnergySysName = (index, i) => {
        var tData = [...energySysLocations]
        let sysNames = tData[index].systemName
        sysNames.splice(i, 1)
        tData[index].systemName = sysNames
        setEnergySysLocations(tData)
    }
    const deleteSS = (index) => {
        let locss = JSON.parse(JSON.stringify(ss))
        locss.splice(index, 1)
        setSS(locss)
    }

    const handleFireSysMoreChange = (index) => {
        let updatedFireSysMore = JSON.parse(JSON.stringify(fireSysMore))
        updatedFireSysMore[index].checked = !updatedFireSysMore[index].checked
        setFireSysMore(updatedFireSysMore)
        forceUpdate()
    }

    const handleFireSysMoreAttachment = (index, files) => {
        let updatedFireSysMore = JSON.parse(JSON.stringify(fireSysMore))
        updatedFireSysMore[index].uploads = files
        setFireSysMore(updatedFireSysMore)
        forceUpdate()
    }

    const checkPointsRemarksAction = (id1, id2, text) => {
        var obj = checklist
        obj[id1][id2].remarks = text
        setChecklist(obj)
    }


    const checkPointsUserAction = (id1, id2, text) => {
        var obj = checklist
        obj[id1][id2].personnel = text
        setChecklist(obj)
    }
    const checkPointsAction = (id1, id2, i) => {
        // let index = index1+''+1
        var obj = checklist
        const checkpointObj1 = [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }]
        console.log('obj1', id2, obj)
        if (obj[id1] != undefined && obj[id1][id2] != undefined) {
            obj[id1][id2].options.forEach((data, ind) => {
                if (ind == i) {
                    data.checked = 1

                } else {
                    data.checked = 0

                }
            })
        } else {
            // obj[id1][id2] = Object.assign([], checkpointObj1)
            if (!obj[id1]) {
                obj[id1] = {};
            }
            var newObj = {}
            newObj['options'] = Object.assign([], checkpointObj1)
            newObj['remarks'] = ''
            newObj['attachments'] = []
            newObj['personnel'] = ''

            obj[id1][id2] = newObj

            // obj[id1][id2] = Object.assign([], checkpointObj1);

            console.log('obj2', obj)
            obj[id1][id2].options.forEach((data, ind) => {
                if (ind == i) {
                    data.checked = 1

                } else {
                    data.checked = 0

                }
            })
            console.log('obj3', obj)
        }
        // item[`checkpoint`] = { type: 'checkpoint', label: label, values: obj[index], remarks: ''}
        setChecklist(obj)
        forceUpdate()
    }
    const searchValue = (name, type) => {
        if (type === 'one') {
            const one = locationSearchOne
            setLocationOne(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'two') {
            const one = locationSearchTwo
            setLocationTwo(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'three') {
            const one = locationSearchThree
            setLocationThree(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'four') {
            const one = locationSearchFour
            setLocationFour(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'five') {
            const one = locationSearchFive
            setLocationFive(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'six') {
            const one = locationSearchSix
            setLocationSix(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }



    }

    const setHotWorks = (value, type, index, ii) => {


        setPermit(prev => prev.map(i => { return i.id === index ? { ...i, checked: value, options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' } : i }))
        if (ii == 0) setisWah(value)
        if (ii == 1) setisConfined(value)
        if (ii == 4) setisLifting(value)





        // const c = checklist

        // let check = {options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' }
        // c.push(check)

        // setChecklist(c)


    }
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };


    const checkRequiredFields = () => {
        let required = true

        const requiredFields = [
            locOne, locTwo, locThree, locFour
        ];

        let isAllFireSysOk = false
        if (fireAlarm == 'Yes') {
            const fireValidated = fs.every((dt) => (dt.loc5.id != undefined && dt.loc6.id != undefined && dt.systems.some(item => item.checked == 1)))
            const fireOtherValidated = fs.every((dt) => dt.systems.find(item => item.label === 'Others')?.checked === 1 ? dt.systems.find(item => item.label === 'Others')?.others !== '' : true)
            const fireSysMoreValidated = fireSysMore.every(item => item.checked === true)
            const fireSysMoreAttachmentsValidated = fireSysMore.every(item => {
                // If the item is checked and has an attachment requirement, it must have at least one upload
                return !(item.checked && item.attachment && (!item.uploads || item.uploads.length === 0));
            })
            isAllFireSysOk = !(fireValidated && fireOtherValidated && fireSysMoreValidated && fireSysMoreAttachmentsValidated);

            isAllFireSysOk = !(fireValidated && fireOtherValidated);
        } else isAllFireSysOk = false
        let isAllSecuritySysOk = false
        if (systemSecurity == 'Yes') {
            const securityValidated = ss.every((dt) => (dt.loc5.id != undefined && dt.loc6.id != undefined && dt.systems.some(item => item.checked == 1)))
            const securityOtherValidated = ss.every((dt) => dt.systems.find(item => item.label === 'Others')?.checked === 1 ? dt.systems.find(item => item.label === 'Others')?.others !== '' : true)
            isAllSecuritySysOk = !(securityValidated && securityOtherValidated);

        } else isAllSecuritySysOk = false

// //
        let isAllEnergySysOk = false
        if (isDcEnergy === 'Yes') {
            console.log('energySys')
            const energyValidated = energySysLocations.every((dt) => (dt.loc5.id !== undefined && dt.loc6.id !== undefined && dt.systems.some(item => item.checked === 1)))
            const energyOtherValidated = energySysLocations.every((dt) => dt.systems.find(item => item.label === 'Others')?.checked === 1 ? dt.systems.find(item => item.label === 'Others')?.others !== '' : true)

            const filterFireMore = fireSysMore.filter(item => item.checked && item.attachment != '')
            const fireMoreValidated = filterFireMore.every(item => item.uploads.length != 0);
            const isFireMoreAllSelected = fireSysMore.every(item => item.checked)
            if (energyValidated && energyOtherValidated && isFireMoreAllSelected && fireMoreValidated) isAllEnergySysOk = false
            else isAllEnergySysOk = true


        } else isAllEnergySysOk = false
        console.log('isAllEnergySysOk', isAllEnergySysOk)
//


        console.log(isAllSecuritySysOk, isAllFireSysOk)

        try {
            if (requiredFields.some(field => !field.id)) {
                setRequiredCheck(true); required = true
            } else if (!workDescription || !MOPTitle || !applicationContactNo || !companyContactNo || startDate == null || endDate == null || !highRisk || !signs) {
                console.log('1')
                setRequiredCheck(true);
                required = true
            }

            else if (isAllFireSysOk) {
                setRequiredCheck(true);
                required = true
            }
            else if (isAllSecuritySysOk) {
                setRequiredCheck(true);
                required = true
            }
            //
            else if (isAllEnergySysOk) {
                setRequiredCheck(true);
                required = true
            }
            //
            else if (highRisk === 'No' && !refValue.id) {
                console.log('7')
                setRequiredCheck(true);
                required = true
            }

            else if (highRisk == 'Yes') {
                console.log('8')
                // Function to check if one of the items in checklists is checked as 1
                const isChecklistChecked = (permitChecklistId, permitId) => {
                    const checklist1 = checklist[permitChecklistId][permitId];
                    // return checklist?.options?.some((item) => item.checked === 1);
                    return checklist1?.options?.some((item) => item.label == 'Yes' && item.checked === 1) || checklist?.options?.some((x) => x.label != 'Yes' && x.checked === 1 && checklist.remarks)
                };

                // Function to check if all applicable items are checked for the permit
                const checkApplicable = (permit) => {
                    return permitChecklist.filter((item) => {
                        return item.applicable.includes(permit.id - 1) && permit.checked === 'Yes' && !isChecklistChecked(item.id, permit.id);
                    }).length === 0;
                };

                // Call the checkApplicable function for each permit
                const checkedPermits = permit.map((permit) => {
                    return {
                        ...permit,
                        allApplicableItemsChecked: checkApplicable(permit),
                    };
                });

                const allApplicablePermits = checkedPermits.filter(dt => dt.checked == 'Yes')
                console.log('checkedPermits', allApplicablePermits)
                const isAllPermitsChecked = allApplicablePermits.every(dt2 => dt2.allApplicableItemsChecked)
                console.log('Is all checkedPermits marked', allApplicablePermits.every(dt2 => dt2.allApplicableItemsChecked))

                if (!isAllPermitsChecked) { setRequiredCheck(true); required = true; }
                else { setRequiredCheck(false); required = false }

            }

            else if (highRisk === 'Yes' && !highriskAssessorValue.id) {
                setRequiredCheck(true);
                required = true
            }

            else { setRequiredCheck(false); required = false; }

        } catch (e) {
            setRequiredCheck(false);
            required = false
        }

        console.log(required)

        return required
    }
    const uploadChecklistFiles = async (checklistItems) => {
        const updatedChecklist = await Promise.all(
            checklistItems.map(async (item) => {
                if (item.uploads && item.uploads.length > 0) {
                    const formData = new FormData();
                    item.uploads.forEach((upload) => {
                        if (upload.file instanceof File) {
                            formData.append("file", upload.file);
                        }
                    });

                    try {
                        const config = {
                            headers: {
                                "Content-Type": "multipart/form-data",
                            },
                        };

                        const fileResponse = await axios.post(FILE_URL, formData, config);

                        if (fileResponse.status === 200 && fileResponse.data.files) {
                            const uploadedFiles = fileResponse.data.files;

                            // Replace each upload with the file and its URL
                            const updatedUploads = uploadedFiles.map((file, index) => ({
                                ...item.uploads[index],
                                url: file.originalname,
                            }));

                            return {
                                ...item,
                                uploads: updatedUploads,
                            };
                        } else {
                            return {
                                ...item,
                                uploads: item.uploads.map(upload => ({ ...upload, url: "" })),
                            };
                        }
                    } catch (error) {
                        console.error("File upload failed", error);
                        return {
                            ...item,
                            uploads: item.uploads.map(upload => ({ ...upload, url: "" })),
                        };
                    }
                } else {
                    return item;
                }
            })
        );

        return updatedChecklist;
    };
    const onsubmit = async () => {

        // if (fireAlarm == 'No' && systemSecurity == 'No' && highRisk == 'No') {
        //     setLoader(false)
        //     Swal.fire('This form must contain either isolation or high risk activity to proceed.')
        // }
        // else {

        setLoader(true)

        setTimeout(() => {
            if (checkRequiredFields()) {
                setLoader(false)
                setTimeout(() => {
                    Swal.fire('Please fill all the required fields')
                }, 200)
            } else {
                if (attachment.length == 0) {
                    setLoader(false)
                    Swal.fire({
                        title: '',
                        text: "There is no attachment added to this permit. Are you sure you want to proceed with submission?",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            submitDcOps()

                        }
                    })

                }
                else {
                    submitDcOps()
                }
            }
            console.log('required check', requiredCheck)
        }, 2000)

        // }



    }
    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    };
    const submitDcOps = async () => {
        let signUrl = ''

        try {

            const formData = new FormData();

            formData.append('file', dataURItoFile(signs, 'sign.png'));

            const token = localStorage.getItem('access_token');
            const fileResponse = await API.post(FILE_URL, formData, config)
            if (fileResponse.status === 200) {
                signUrl = fileResponse.data.files[0].originalname;

            }
        }
        catch (e) {
            console.log(e)
        }


        let arr1 = []


        // Upload main attachments
        const formData = new FormData();
        attachment.forEach((file, index) => {
            formData.append('file', file.file);
        });

        try {
            const fileResponse = await API.post(FILE_URL, formData, config);
            if (fileResponse.status === 200) {
                arr1 = fileResponse.data.files.map(file => file.originalname);
            }
        } catch (e) {
            console.error('Error uploading attachment files:', e);
        }

        console.log(fireSysMore)
        // Add fireSysMore attachments if any
        let fireSysMoreFiles = [];

        //////////////////////////////
        if (fireAlarm === 'Yes') {
           

            const result = await uploadChecklistFiles(fireSysMore);
            setFireSysMore(result)
        }



        console.log(fireSysMore)

        console.log(signUrl)
        console.log(arr1)

        var dcOpsData = {}
        dcOpsData['highRisk'] = highRisk
        dcOpsData['mopTitle'] = MOPTitle
        dcOpsData['ticketNo'] = ticketNo
        dcOpsData['contactNo'] = applicationContactNo
        dcOpsData['companyContactNo'] = companyContactNo
        dcOpsData['isDcFire'] = fireAlarm
        dcOpsData['isDcSecurity'] = systemSecurity
        dcOpsData['isDcEnergy'] = isDcEnergy
        if (fireAlarm) {
            // dcOpsData['fireSystems'] = fireSystems.filter(item => item.checked === 1).map(item => (item.label === 'Others' ? item.others : item.label)).map(i => ({ label: i, isolated: 0 }));
            // dcOpsData['fireSysLocations'] = fireSysLocations
            dcOpsData['fireSystems'] = fs.map((dt) => ({ loc5: dt.loc5, loc6: dt.loc6, systems: dt.systems.filter(item => item.label === 'Others' ? (item.checked == 1 && item.others) : item.checked == 1).map(item => (item.label === 'Others' ? item.others : item.label)).map(i => ({ label: i, isolated: 0 })) }))
           //
            dcOpsData['fireSysMore'] = fireSysMore
        //
        }
        if (systemSecurity) {
            dcOpsData['securitySystems'] = ss.map((dt) => ({ loc5: dt.loc5, loc6: dt.loc6, systems: dt.systems.filter(item => item.label === 'Others' ? (item.checked == 1 && item.others) : item.checked == 1).map(item => (item.label === 'Others' ? item.others : item.label)).map(i => ({ label: i, isolated: 0 })) }))
            // dcOpsData['securitySystems'] = securitySystems.filter(item => item.checked === 1).map(item => (item.label === 'Others' ? item.others : item.label)).map(i => ({ label: i, isolated: 0 }));
            // dcOpsData['securitySysLocations'] = securitySysLocations
        }
//
        if (isDcEnergy === 'Yes') {
            dcOpsData['energySystems'] = energySysLocations.map((dt) => ({ loc5: dt.loc5, loc6: dt.loc6, systemName: dt.systemName, systems: dt.systems.filter(item => item.label === 'Others' ? (item.checked === 1 && item.others) : item.checked === 1).map(item => (item.label === 'Others' ? item.others : item.label)).map(i => ({ label: i, isolated: 0 })) }))

        }
        //
        dcOpsData['applicantSign'] = signUrl
        dcOpsData['applicantSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
        dcOpsData['applicantName'] = user.firstName
        dcOpsData['applicantCompany'] = user.company || user.type == 'Internal' && 'STTGDC'
        dcOpsData['dcsoName'] = ""
        dcOpsData['approverName'] = refValue?.title

        dcOpsData['location1'] = locOne.name
        dcOpsData['location2'] = locTwo.name
        dcOpsData['location3'] = locThree.name
        dcOpsData['location4'] = locFour.name
        dcOpsData['location5'] = locFive.name
        dcOpsData['location6'] = locSix.name

        console.log(dcOpsData)

        var reqData = {}
        reqData['description'] = workDescription
        reqData['uploads'] = arr1
        reqData["created"] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
        reqData['locationOneId'] = locOne.id
        reqData['locationTwoId'] = locTwo.id
        reqData['locationThreeId'] = locThree.id
        reqData['locationFourId'] = locFour.id
        reqData['locationFiveId'] = ''//locFive.id
        reqData['locationSixId'] = ''//locSix.id
        reqData['permitStartDate'] = moment(startDate).format('DD-MM-YYYY hh:mm A')
        reqData['permitEndDate'] = moment(endDate).format('DD-MM-YYYY hh:mm A')
        reqData['permitType'] = 'DC'
        reqData['dcop'] = JSON.stringify(dcOpsData)
        // reqData['dcsoApproverId'] = refValue.id

        reqData['zonesAndLevels'] = JSON.stringify(zone)
        if (highRisk == 'Yes') {
            const allApplicablePermits = permit.filter(dt => dt.checked == 'Yes')
            var highRiskData = {}
            highRiskData['selectedPermits'] = allApplicablePermits
            highRiskData['checklists'] = checklist
            highRiskData['assessorName'] = highriskAssessorValue.title || ''
            reqData['high_risk'] = JSON.stringify(highRiskData)
            reqData['assessorId'] = highriskAssessorValue.id
        } else {
            reqData['high_risk'] = ""
            reqData['approverId'] = refValue.id
        }


        console.log(reqData)
        const response = await API.post(PERMIT_REPORT,
            JSON.stringify(reqData)
        );
        if (response.status === 200) {
            setLoader(false)
            Swal.fire({
                title: '',
                text: "This Permit has been submitted.",
                icon: 'success',

            }).then((result) => {
                if (result.isConfirmed) {
                    // navigate(-1)
                    handleClose(false)

                }
            })
        } else {
            setLoader(false)
            Swal.fire({
                title: '',
                text: "Please Try Again.",
                icon: 'warning',

            }).then((result) => {
                if (result.isConfirmed) {
                    // navigate(-1)
                    handleClose(false)

                }
            })
        }
        setLoader(false)

    }

    const setEnergyName = (text, index, i) => {
        const tData = [...energySysLocations];  // Create a shallow copy of the array

        // Check if sysNames exists and is an array, if not, initialize it as an empty array
        let sysNames = tData[index].systemName || [];

        // If index i doesn't exist in the sysNames array, initialize it
        if (sysNames[i] === undefined) {
            sysNames[i] = '';  // Ensure sysNames[i] is initialized
        }

        // Update the sysNames array with the new value at index i
        sysNames[i] = text;  // Concatenate text to the existing value

        // Update the copied array with the modified sysNames
        tData[index].systemName = sysNames;

        // Set the updated state
        setEnergySysLocations(tData);
    };

    const addEnergySysName = (index) => {
        var tData = [...energySysLocations]
        let sysNames = tData[index].systemName
        if (sysNames.length === 0) {
            sysNames.push('')
            sysNames.push('')
        } else sysNames.push('')
        tData[index].systemName = sysNames
        setEnergySysLocations(tData)
    }

    return (
        <>

            <Modal
                show={show}
                onHide={() => { handleClose(false) }}
                size="lg"
            >
                <Modal.Header closeButton>
                    <Modal.Title >
                        Data Center Operations - ePTW
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>


                    <Row >

                        {/* <AllFilterLocation handleFilter={handleFilter}/> */}
                        <Row>
                            <Col>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[0].altTitle : ''} data={locationOne} selectedValue={locOne.id} label={locOne === '' ? 'Select' : locOne.name} updateListSelected={updateSelected} type={'one'} searchList={searchValue} box={true} />
                            </Col>
                            <Col>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[1].altTitle : ''} data={locationTwo} selectedValue={locTwo.id} label={locTwo === '' ? 'Select' : locTwo.name} updateListSelected={updateSelected} type={'two'} searchList={searchValue} box={true} />
                            </Col>
                            <Col>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[2].altTitle : ''} data={locationThree} selectedValue={locThree.id} label={locThree === '' ? 'Select' : locThree.name} updateListSelected={updateSelected} type={'three'} searchList={searchValue} box={true} />
                            </Col>
                        </Row>

                        <Row>
                            <Col md={'4'}>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[3].altTitle : ''} data={locationFour} selectedValue={locFour.id} label={locFour === '' ? 'Select' : locFour.name} updateListSelected={updateSelected} type={'four'} searchList={searchValue} box={true} />
                            </Col>
                            {/* <Col>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={locFive.id} label={locFive === '' ? 'Select' : locFive.name} updateListSelected={updateSelected} type={'five'} searchList={searchValue} box={true} />
                            </Col>
                            <Col>
                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={locSix.id} label={locSix === '' ? 'Select' : locSix.name} updateListSelected={updateSelected} type={'six'} searchList={searchValue} box={true} />
                            </Col> */}
                        </Row>
                        {zone.map((item, index) => {
                            return (
                                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                        {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteZone(index) }} >close</i>}
                                        <Card.Body>
                                            <Row>
                                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.locationFive ? item.locationFive.id : ''} label={item.locationFive.name || 'Select'} updateListSelected={data => {
                                                    console.log(data)
                                                    var locations = zone
                                                    locations[index].locationFive = data
                                                    setZone(locations)
                                                    updateSelected(data, 'five')
                                                    forceUpdate()
                                                }} type={'five'} searchList={searchValue} box={false} />
                                                <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.locationSix ? item.locationSix.id : ''} label={item.locationSix.name || 'Select'
                                                } updateListSelected={data => {
                                                    var locations = zone
                                                    locations[index].locationSix = data
                                                    setZone(locations)
                                                    forceUpdate()
                                                }} type={'six'} searchList={searchValue} box={false} />
                                                {index === zone.length - 1 &&
                                                    <Container className='d-flex justify-content-center'>
                                                        <Button style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', color: 'black', background: 'white' }} onClick={() => { addNewZone() }}>+ Add More</Button>
                                                    </Container>
                                                }

                                            </Row>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            )
                        })}
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row >
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                            <label>{'Reference MOP Title'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                            <InputGroup className="mb-3">
                                                <Form.Control
                                                    placeholder=""
                                                    type="text"
                                                    onChange={(e) => setMOPTitle(e.target.value)}
                                                />

                                            </InputGroup>
                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row >
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                            <label>{'Change Ticket No'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                            <InputGroup className="mb-3">
                                                <Form.Control
                                                    placeholder=""
                                                    type="text"
                                                    onChange={(e) => setTicketNo(e.target.value)}
                                                />

                                            </InputGroup>
                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row >
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                            <label>{'Applicant Contact Number'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                            <InputGroup className="mb-3">
                                                <Form.Control
                                                    placeholder=""
                                                    type="text"
                                                    onChange={(e) => setApplicantContactNo(e.target.value)}
                                                    onKeyPress={(e) => {
                                                        if (!/^[0-9]*$/.test(e.key)) {
                                                            e.preventDefault();
                                                        }
                                                    }}
                                                />
                                            </InputGroup>
                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row >
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                            <label>{'Company Contact Number'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                            <InputGroup className="mb-3">
                                                <Form.Control
                                                    placeholder=""
                                                    type="number"
                                                    onChange={(e) => setCompanyContactNo(e.target.value)}
                                                    onKeyPress={(e) => {
                                                        if (!/^[0-9]*$/.test(e.key)) {
                                                            e.preventDefault();
                                                        }
                                                    }}
                                                />

                                            </InputGroup>
                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <Row>
                                        <Col>
                                            <label>
                                                {`Work Start Date & Time`} <span style={{ color: '#D62828' }}>*</span>
                                            </label>
                                        </Col>
                                        <Col>
                                            <ReactDatePicker
                                                showIcon
                                                className="form-control"
                                                showTimeSelect
                                                timeFormat="h:mm aa"
                                                timeIntervals={15}
                                                timeCaption="time"
                                                dateFormat="dd/MM/yyyy h:mm aa"
                                                selected={startDate}
                                                onChange={(date) => {
                                                    setStartDate(date);
                                                    setEndDate(null); // Reset endDate when startDate changes
                                                }}
                                                minDate={now}
                                                maxDate={maxStartDate}
                                                minTime={getStartDateMinTime()}
                                                maxTime={getStartDateMaxTime()}
                                                filterTime={(time) => {
                                                    const selectedDate = startDate || now;
                                                    const currentTime = new Date(time);
                                                    const minTime = getStartDateMinTime();
                                                    const maxTime = getStartDateMaxTime();
                                                    return (
                                                        (isAfter(currentTime, minTime) || isEqual(currentTime, minTime)) &&
                                                        (isBefore(currentTime, maxTime) || isEqual(currentTime, maxTime))
                                                    );
                                                }}
                                                filterDate={(date) => {
                                                    return (
                                                        (isAfter(date, now) || isEqual(date, now)) &&
                                                        (isBefore(date, maxStartDate) || isEqual(date, maxStartDate))
                                                    );
                                                }}
                                            />
                                        </Col>
                                        <Col>
                                            <label>
                                                {`Work End Date & Time`} <span style={{ color: '#D62828' }}>*</span>
                                            </label>
                                        </Col>
                                        <Col>
                                            <ReactDatePicker
                                                showIcon
                                                className="form-control"
                                                showTimeSelect
                                                timeFormat="h:mm aa"
                                                timeIntervals={15}
                                                timeCaption="time"
                                                dateFormat="dd/MM/yyyy h:mm aa"
                                                selected={endDate}
                                                onChange={(date) => setEndDate(date)}
                                                minDate={startDate || now}
                                                maxDate={startDate ? addHours(startDate, 12) : null}
                                                minTime={
                                                    startDate
                                                        ? new Date(startDate).setHours(
                                                            startDate.getHours(),
                                                            startDate.getMinutes(),
                                                            0,
                                                            0
                                                        )
                                                        : getEndDateMinTime()
                                                }
                                                maxTime={
                                                    startDate
                                                        ? new Date(startDate).setHours(23, 59, 59, 999) // End time on the same date
                                                        : getEndDateMaxTime()
                                                }
                                                filterTime={(time) => {
                                                    if (!startDate) return true;
                                                    const currentTime = new Date(time);
                                                    const minTime = new Date(startDate).setHours(
                                                        startDate.getHours(),
                                                        startDate.getMinutes(),
                                                        0,
                                                        0
                                                    );
                                                    const maxTime = new Date(startDate).setHours(23, 59, 59, 999);
                                                    return (
                                                        (isAfter(currentTime, minTime) || isEqual(currentTime, minTime)) &&
                                                        (isBefore(currentTime, maxTime) || isEqual(currentTime, maxTime))
                                                    );
                                                }}
                                                disabled={!startDate}
                                            />
                                        </Col>
                                    </Row>

                                </Card.Body>
                            </Card>
                        </Col>

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row >
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                            <label>Description of work <span style={{ color: '#D62828' }}>*</span></label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                            <InputGroup>

                                                <Form.Control style={{ height: 100 }} as="textarea" aria-label="With textarea"
                                                    onChange={(e) => setWorkDescription(e.target.value)}
                                                />
                                            </InputGroup>
                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <MultiWidget title={'Attach approved MOP,MOS,RA,SWp Where applicable & Approved Workers List'} mandatory data={[]} allowed={['image', 'document']} getData={(data) => { setAttachDocument(data); console.log(data) }} noOfFiles={8} maxLimitForPerFile={1000000} maxLimitForTotalFiles={50000000} />
                        </Col>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row  >
                                        <Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                                <label>Is fire alarm isolation needed ? <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                        </Row>
                                        <Row className="col-4 mt-2 mb-2 m-auto p-0 " style={{ borderRadius: '20px', background: '#e8e8e8' }} >
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setFireAlarm('Yes')} style={{ color: fireAlarm === 'Yes' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: fireAlarm === 'Yes' ? '#005284' : '#e8e8e8' }}>
                                                Yes
                                            </Col>
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setFireAlarm('No')} style={{ color: fireAlarm === 'No' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: fireAlarm === 'No' ? '#005284' : '#e8e8e8' }}>
                                                No
                                            </Col>



                                        </Row>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        {fireAlarm === 'Yes' ?
                            fs.map((item, index) => {
                                console.log(item)
                                return (
                                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                            {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteFS(index) }} >close</i>}
                                            <Card.Body >
                                                <Row  >
                                                    <Row>
                                                        <Col xs={12} sm={12} md={12} className="d-flex ">
                                                            <label>Fire system(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                        </Col>
                                                    </Row>
                                                    <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                        <Container className="d-flex flex-wrap">
                                                            {item.systems && item.systems.map((tag) => {
                                                                return (tagComponent(tag))
                                                            })
                                                            }
                                                        </Container>
                                                    </Row>
                                                    <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                        <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                                    </Col>
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                        console.log(data)
                                                        var locations = fs
                                                        locations[index].loc5 = data
                                                        setFS(locations)

                                                        forceUpdate()
                                                    }} type={'five'} searchList={searchValue} box={false} />
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                                    } updateListSelected={data => {
                                                        var locations = fs
                                                        locations[index].loc6 = data
                                                        setFS(locations)

                                                        forceUpdate()

                                                    }} type={'six'} searchList={searchValue} box={false} />
                                                    {index === fs.length - 1 &&
                                                        <Container className='d-flex justify-content-center'>
                                                            <Button style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', color: 'black', background: 'white' }} onClick={() => { addNewFS() }}>+ Add More</Button>
                                                        </Container>
                                                    }

                                                </Row>



                                            </Card.Body>

                                        </Card>
                                    </Col>
                                )

                            }) : ''}

                            {/* ............... */}
                        {fireAlarm === 'Yes' && (
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                    <Card.Body>
                                        <Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex">
                                                <label>Fire System Safety Checklist <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                            <Col xs={12} sm={12} md={12}>
                                                {fireSysMore.map((item, index) => (<>
                                                    <div key={index} className="d-flex align-items-center mb-2">
                                                        <div onClick={() => handleFireSysMoreChange(index)} className="d-flex align-items-center mt-4 mb-4" style={{ cursor: 'pointer' }}>
                                                            <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                                                            {item.checked &&
                                                                <i className="material-icons" style={{ position: 'absolute', color: 'white' }}>check</i>
                                                            }
                                                            <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                                                        </div>

                                                    </div>

                                                    {
                                                        item.checked && item.attachment && (
                                                            <div className="ml-3 mb-4">
                                                                <MultiWidget
                                                                    title={item.attachment}
                                                                    mandatory
                                                                    data={item.uploads || []}
                                                                    allowed={['image', 'document']}
                                                                    getData={(data) => handleFireSysMoreAttachment(index, data)}
                                                                    noOfFiles={3}
                                                                    maxLimitForPerFile={1000000}
                                                                    maxLimitForTotalFiles={5000000}
                                                                />
                                                            </div>
                                                        )
                                                    }
                                                </>))}
                                            </Col>
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        )}
                        {/* ............. */}
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row  >
                                        <Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                                <label>Is security system isolation needed ? <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                        </Row>
                                        <Row className="col-4 mt-2 mb-2 m-auto p-0 " style={{ borderRadius: '20px', background: '#e8e8e8' }} >
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setSystemSecurity('Yes')} style={{ color: systemSecurity === 'Yes' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: systemSecurity === 'Yes' ? '#005284' : '#e8e8e8' }}>
                                                Yes
                                            </Col>
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setSystemSecurity('No')} style={{ color: systemSecurity === 'No' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: systemSecurity === 'No' ? '#005284' : '#e8e8e8' }}>
                                                No
                                            </Col>



                                        </Row>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        {systemSecurity === 'Yes' ?
                            ss.map((item, index) => {
                                return (
                                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                            {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteSS(index) }} >close</i>}
                                            <Card.Body >
                                                <Row  >
                                                    <Row>
                                                        <Col xs={12} sm={12} md={12} className="d-flex ">
                                                            <label>Security System(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                        </Col>
                                                    </Row>
                                                    <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                        <Container className="d-flex flex-wrap">
                                                            {console.log(item.systems)}
                                                            {item.systems && item.systems.map((tag) => {
                                                                return (tagComponent(tag))
                                                            })

                                                            }

                                                        </Container>

                                                    </Row>
                                                    <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                        <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                                    </Col>
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                        console.log(data)
                                                        var locations = ss
                                                        locations[index].loc5 = data
                                                        setSS(locations)

                                                        forceUpdate()
                                                    }} type={'five'} searchList={searchValue} box={false} />
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                                    } updateListSelected={data => {
                                                        var locations = ss
                                                        locations[index].loc6 = data
                                                        setSS(locations)

                                                        forceUpdate()

                                                    }} type={'six'} searchList={searchValue} box={false} />
                                                    {index === ss.length - 1 &&
                                                        <Container className='d-flex justify-content-center'>
                                                            <Button style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', color: 'black', background: 'white' }} onClick={() => { addNewSS() }}>+ Add More</Button>
                                                        </Container>
                                                    }

                                                </Row>



                                            </Card.Body>

                                        </Card>
                                    </Col>
                                )

                            }) : ''}

{/* ............. */}
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row  >
                                        <Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                                <label>Is energy system isolation needed ? <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                        </Row>
                                        <Row className="col-4 mt-2 mb-2 m-auto p-0 " style={{ borderRadius: '20px', background: '#e8e8e8' }} >
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setIsDcEnergy('Yes')} style={{ color: isDcEnergy === 'Yes' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: isDcEnergy === 'Yes' ? '#005284' : '#e8e8e8' }}>
                                                Yes
                                            </Col>
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setIsDcEnergy('No')} style={{ color: isDcEnergy === 'No' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: isDcEnergy === 'No' ? '#005284' : '#e8e8e8' }}>
                                                No
                                            </Col>



                                        </Row>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>

                        {isDcEnergy === 'Yes' ?
                            energySysLocations.map((item, index) => {
                                return (
                                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                            {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { removeEnergySysLocation(index) }} >close</i>}
                                            <Card.Body >
                                                <Row  >
                                                    <Row>
                                                        <Col xs={12} sm={12} md={12} className="d-flex ">
                                                            <label>Energy System(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                        </Col>
                                                    </Row>
                                                    <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                        <Container className="d-flex flex-wrap">
                                                            {console.log(item.systems)}
                                                            {item.systems && item.systems.map((tag) => {
                                                                return (tagComponent(tag))
                                                            })

                                                            }

                                                        </Container>

                                                    </Row>
                                                    <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                        <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                                    </Col>
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                        console.log(data)
                                                        var locations = energySysLocations
                                                        locations[index].loc5 = data
                                                        setEnergySysLocations(locations)



                                                        forceUpdate()
                                                    }} type={'five'} searchList={searchValue} box={false} />
                                                    <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                                    } updateListSelected={data => {
                                                        var locations = energySysLocations
                                                        locations[index].loc6 = data
                                                        setEnergySysLocations(locations)

                                                        forceUpdate()

                                                    }} type={'six'} searchList={searchValue} box={false} />
                                                    {index === energySysLocations.length - 1 &&
                                                        <Container className='d-flex justify-content-center'>
                                                            <Button style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', color: 'black', background: 'white' }} onClick={() => { addEnergySysLocation() }}>+ Add More</Button>
                                                        </Container>
                                                    }




                                                    {(item?.systemName.length > 0 ? item?.systemName : ['']).map((tag, i) => {
                                                        return (< div className="m-auto mb-3" xs={12} sm={12} md={12} style={{ position: 'relative' }}>

                                                            <label>Name/ID of the system(s) / device(s)</label>

                                                            <input type="text" className="form-control" value={tag} onChange={(e) => setEnergyName(e.target.value, index, i)} />
                                                            {i !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { removeEnergySysName(index, i) }} >close</i>}
                                                        </div>

                                                        )
                                                    }
                                                    )}

                                                    <Button style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', color: 'black', background: 'white' }} onClick={() => { addEnergySysName(index) }}>+ Add More</Button>

                                                </Row>



                                            </Card.Body>

                                        </Card>
                                    </Col>
                                )

                            }) : ''}
{/* .................. */}
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row  >
                                        <Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                                <label>Does this work involve High Risk Activity ? <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                        </Row>
                                        <Row className="col-4 mt-2 mb-2 m-auto p-0 " style={{ borderRadius: '20px', background: '#e8e8e8' }} >
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setHighRisk('Yes')} style={{ color: highRisk === 'Yes' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: highRisk === 'Yes' ? '#005284' : '#e8e8e8' }}>
                                                Yes
                                            </Col>
                                            <Col xs={6} sm={6} md={6} className="p-1" onClick={() => setHighRisk('No')} style={{ color: highRisk === 'No' ? '#fff' : '#000', textAlign: 'center', borderRadius: 20, background: highRisk === 'No' ? '#005284' : '#e8e8e8' }}>
                                                No
                                            </Col>



                                        </Row>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        {highRisk === 'Yes' ? <>


                            {permit.map((item, i) => {
                                return (
                                    <YesNo title={`Does this work involve ${item.label}  `} value={item.checked} index={item.id} i={i} checkYesNo={setHotWorks} type={item.label} />
                                )
                            })}


                        </> : ''}
                        {permit.some(data => data.checked === 'Yes') && <>

                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', background: 'red', textAlign: 'center', color: '#fff', padding: 10 }}>
                                    <h5>Safety Checklist</h5>
                                    <p>Physical Verification of Compliance Required</p>
                                </Card>
                            </Col>

                            {permitChecklist.map((item, index) => {
                                const hasMatch = permit.some(data => item.applicable.includes((data.id - 1)) && data.checked === 'Yes');
                                if (hasMatch) {

                                    return (
                                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                                <Card.Body >
                                                    <Row  >
                                                        <Row>
                                                            <Col xs={12} sm={12} md={12} className="d-flex ">
                                                                <label>{index + 1}.{item.label} <span style={{ color: '#D62828' }}>*</span></label>
                                                            </Col>
                                                        </Row>


                                                    </Row>

                                                    {permit.map((data, i) => {
                                                        if (item.applicable.includes(i) && data.checked == 'Yes') {
                                                            var checklists = checklist[item.id] != undefined && checklist[item.id][data.id] != undefined ? checklist[item.id][data.id] : { options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' }


                                                            return (<>
                                                                <Row className="p-1">
                                                                    <Col xs={12} sm={12} md={12} className="d-flex ">
                                                                        <label>Confirm {data.label} Activity <span style={{ color: '#D62828' }}>*</span></label>



                                                                    </Col>
                                                                </Row>
                                                                <Row className="col-12 mt-2 mb-2 m-auto p-0 " >
                                                                    {checklists.options.map((dt, ind) => {

                                                                        return (
                                                                            <Col xs={4} sm={4} md={4} className="p-1" style={{ color: '#000', textAlign: 'center', border: '1px solid', background: dt.checked === 1 ? ind === 0 ? '#1bbc9b' : ind === 1 ? '#D62828' : '#ffa500' : '#fff' }} onClick={() => checkPointsAction(item.id, data.id, ind)}>
                                                                                {dt.label}
                                                                            </Col>
                                                                        )
                                                                    })}



                                                                </Row>

                                                                {(checklist[item.id] != undefined && checklist[item.id][data.id] != undefined)
                                                                    ? checklist[item.id][data.id].options.some(option => option.label === 'Yes' && option.checked === 1) ?
                                                                        item.attachment ? null

                                                                            : item.user &&

                                                                            <Row >
                                                                                <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                                                                    <label>{item.userText} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                                                                </Col>
                                                                                <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                                                                    <InputGroup className="mb-3">
                                                                                        <Form.Control
                                                                                            placeholder=""
                                                                                            type="text"
                                                                                            onChange={(e) => checkPointsUserAction(item.id, data.id, e.target.value)}
                                                                                        />

                                                                                    </InputGroup>
                                                                                </Col>
                                                                            </Row>


                                                                        :

                                                                        <Row >
                                                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                                                                <label>{'Remarks (if any)...'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                                                            </Col>
                                                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                                                                <InputGroup className="mb-3">
                                                                                    <Form.Control
                                                                                        placeholder=""
                                                                                        type="text"
                                                                                        onChange={(e) => checkPointsRemarksAction(item.id, data.id, e.target.value)}
                                                                                    />

                                                                                </InputGroup>
                                                                            </Col>
                                                                        </Row>

                                                                    : null

                                                                }


                                                            </>)

                                                        }
                                                    })}




                                                </Card.Body>

                                            </Card>
                                        </Col>
                                    )

                                }

                            })}</>}

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row>

                                        <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                            <label style={{ textAlign: 'justify' }}>I confirm that I am the supervisor in charge of the activity(ies) and am suitably competent to carry out the Person-In-Charge function. I have read and fully understand the SWMS/SWP and all the safety precautions to be taken under current legislations and STT GDC’s Group Minimum Standards. I have prepared the work area(s) to be safe for the task(s) and have visually confirmed all SWMS/SWP & Safety Checklist conditions are complied with. To the best of my knowledge this activity is safe to proceed.</label>
                                        </Col>

                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true); getApproverAndRep() }}>
                                            <span class="material-icons" style={{ fontSize: 60 }}>
                                                draw
                                            </span>


                                        </Col>

                                        <div className="d-flex justify-content-center">
                                            {signs !== '' &&
                                                <img src={signs} height={100} style={{ minWidth: 150 }} />
                                            }
                                        </div>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>

                        {highRisk === 'Yes' ?

                            // {permit.some(data => data.checked === 'Yes') &&

                            <SelectCardComponent mandatory={true} title={'Select High Risk Assessor'} data={highriskAssessor} selectedValue={highriskAssessorValue.id} label={highriskAssessorValue === '' ? 'Select' : highriskAssessorValue.title} updateListSelected={(data, type) => {
                                setHighRiskAssesserValue(data)
                            }} type={'owner'} searchList={searchValue} box={true} />
                            // }
                            :

                            <SelectCardComponent mandatory={true} title={'Select High Risk Approver'} data={ref} selectedValue={refValue.id} label={refValue === '' ? 'Select' : refValue.title} updateListSelected={(data, type) => {
                                setRefValue(data)

                            }} type={'owner'} searchList={searchValue} box={true} />
                        }
                    </Row>

                    <Row style={{ height: 50, borderRadius: 10, background: '#D62828', color: 'white' }} onClick={() => onsubmit()} className="align-items-center m-auto"  >
                        <label >Submit </label>
                    </Row>
                    {loader && <FullLoader />}

                </Modal.Body>
            </Modal>
            <Modal
                show={searchModal}
                onHide={() => { setSearchModal(false) }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                backdrop={'static'}
            >
                <Modal.Header closeButton={false}>
                    <Modal.Title id="contained-modal-title-vcenter">
                        Sign
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>
                    <SignatureCanvas
                        ref={sign}
                        penColor='green'
                        backgroundColor="white"
                        canvasProps={{
                            className: "sigCanvas",
                            style: {
                                width: '100%', // Ensures the canvas takes up the full width
                                background: '#fff',
                                boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                height: '100px'
                            },
                        }}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => { setSign(sign.current.getTrimmedCanvas().toDataURL("image/png")); setSearchModal(false) }}>confirm</Button>
                    <Button onClick={() => sign.current.clear()}>Clear</Button>
                    <Button onClick={() => { setSearchModal(false) }}>Close</Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default NewEPTWDCO