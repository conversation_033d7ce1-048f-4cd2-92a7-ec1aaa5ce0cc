import React, { useEffect, useState } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { API_URL, INCIDENT_REVIWERER_URL, OBSERVATION_REPORT_URL, REPORT_INCIDENT_INVESTIGATE_URL, REPORT_INCIDENT_LEAD_INVESTIGATOR_URL, REPORT_INCIDENT_URL, REPORT_INCIDENT_URL_WITH_ID } from '../constants';
import { Modal, Button, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";

import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';

import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import FilterLocation from './FilterLocation';
import AllFilterLocation from './AllLocationFilter';
import IncidentInvestigationViewModal from './IncidentInvestigationViewModal';
import moment from 'moment';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { Button as Button1 } from 'primereact/button';
import extractImpactLevel from '../utils/impactLevel';
// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const UnderIncidentInvestigation = ({ incident, getIncidentData }) => {
  const history = useHistory();
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)

  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    maskId: { value: null, matchMode: FilterMatchMode.IN },
    IncidentCategory: { value: null, matchMode: FilterMatchMode.IN },
    status: { value: null, matchMode: FilterMatchMode.IN },
    type: { value: null, matchMode: FilterMatchMode.IN },
    'user.firstName': { value: null, matchMode: FilterMatchMode.IN },
    'incidentOwner.firstName': { value: null, matchMode: FilterMatchMode.IN },
    'investigator.firstName': { value: null, matchMode: FilterMatchMode.IN },
    color: { value: null, matchMode: FilterMatchMode.EQUALS },
    created: { value: null, matchMode: FilterMatchMode.CUSTOM },
  });
  const defaultMaterialTheme = createTheme();
  const tableOptions = {
    actionsColumnIndex: -1,
    actionsCellStyle: {
      padding: '1.125rem 1.375rem',
    },
    pageSize: 20,
    headerStyle: {

      padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    },
    rowStyle: {
      // padding: '1.125rem 1.375rem',
      fontSize: '0.812rem'
    }
  }
  const incidentColumns = [
    {
      title: "Incident Date",
      field: "incidentDate",

      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      },
      render: rowData => {
        return `${moment(rowData.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A')}`
      }
    },
    {
      title: "#ID",
      field: "maskId",
      defaultSort: 'desc',
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Title",
      field: "title",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      },
      render: rowData => (
        <div style={{
          whiteSpace: 'pre-line',
          wordWrap: 'anywhere'
        }}>
          {rowData.title}
        </div>
      )
    },
    {
      title: "Lead Investigator",
      field: "investigator.firstName",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },

    {
      title: "Status",
      field: "status",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '10%',
        maxWidth: '10%'
      }
    },
    {
      title: "Incident Category",
      field: "IncidentCategory",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '30%',
        maxWidth: '30%'
      }
    },
    {
      title: "Incident Owner",
      field: "incidentOwner.firstName",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '10%',
        maxWidth: '10%'
      }
    },
    {
      title: "Reported By",
      field: "user.firstName",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '10%',
        maxWidth: '10%'
      }
    },
    {
      title: "Impact Classification",
      field: "actualImpact",
      cellStyle: {
        padding: '1.125rem 1.375rem',
        width: '20%',
        maxWidth: '20%'
      }, 
      render: rowData => {
        return `${rowData.actualImpact} ${rowData.potentitalImpact && `(${extractImpactLevel(rowData.actualImpact)})`}`
      }
    }

  ]

  const tableActions = [
    {
      icon: 'visibility',
      tooltip: 'View',
      onClick: (event, rowData) => {
        // Do save operation

        viewIncident(rowData.id)
      }
    }
  ]

  const tableStyle = {
    borderRadius: '0',
    boxShadow: 'none',
  };

  const [filterData, setFilterData] = useState([]);
  const [data, setData] = useState([]);
  const [user, setUser] = useState([])
  const [owner, setOwner] = useState([])
  const [inves, setInves] = useState([])

  useEffect(() => {
    // getIncidentData();
    const obs = incident.map(item => {
      return { name: item.user.firstName, value: item.user.firstName }
    })
    setUser(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const obs1 = incident.map(item => {
      return { name: item.incidentOwner?.firstName, value: item.incidentOwner?.firstName }
    })
    setOwner(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    const obs2 = incident.map(item => {
      return { name: item.investigator.firstName, value: item.investigator.firstName }
    })
    setInves(obs2.filter((ele, ind) => ind === obs2.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

    setData(incident)
    setFilterData(incident)
  }, [incident])

  // const getIncidentData = async () => {

  //   const params = {
  //     "include": [{ "relation": "investigator" }, { "relation": "incidentOwner" }, { "relation": "user" }]

  //   };


  //   const response = await API.get(`${REPORT_INCIDENT_INVESTIGATE_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
  //   if (response.status === 200) {

  //     const preprocessedData = response.data.map(item => ({
  //       ...item,


  //       incidentDate: moment(item.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A'),
  //       actualImpact: `${item.actualImpact} ${item.potentialImpact && `(${item.potentialImpact})`}`

  //     }));
  //     setData(preprocessedData)
  //     setFilterData(preprocessedData)
  //   }
  // }
  const [files, setFiles] = useState([]);

  const handleFileChange = (file) => {
    setFiles(file)

  }


  const [showModal, setShowModal] = useState(false)



  useEffect(() => {
    getIncidentData();
  }, [showModal])


  const [currentIncident, setCurrentIncident] = useState('')
  const viewIncident = async (id) => {
    setCurrentIncident(id);
    getReportIncident(id);

  }

  const [incidentData, setIncidentData] = useState({})
  const getReportIncident = async (id) => {

    const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

    const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

    const response = await API.get(url);
    if (response.status === 200) {

      const data = response.data;

      setIncidentData(data)
      setShowModal(true)

    }
  }








  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
    const filteredData = data.filter(item => {
      return (
        (locationOneId === '' || item.locationOneId === locationOneId) &&
        (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
        (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
        (locationFourId === '' || item.locationFourId === locationFourId)
      );
    });

    setFilterData(filteredData);
  };
  const renderHeader = () => {
    const value = filters['global'] ? filters['global'].value : '';

    return (
      <div className='d-flex justify-content-between'>
        {/* <div className="">
          <span className='me-3'>Month Filter :</span>
          <Calendar view='month' className="w-full me-2" value={startDate} placeholder='From' onChange={(e) => setStartDate(e.value)} dateFormat="mm/yy" showIcon />
          <Calendar view='month' className="w-full  me-3" value={endDate} placeholder="To" onChange={(e) => setEndDate(e.value)} dateFormat="mm/yy" showIcon />

          <Button1  className='me-3' rounded text raised severity="success" aria-label="Search" onClick={() => onDateSearch()} label='Apply'/>
          <Button1  rounded text raised severity="danger" aria-label="Cancel" label='Cancel' onClick={() => { setFilterData(data); setStartDate(null); setEndDate(null) }} />

        </div>
        <span className="p-input-icon-left">
          <i className="fa fa-search" />
          <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} placeholder="Global Search" />
        </span> */}
      </div>
    );
  };

  const header = renderHeader();
  const onGlobalFilterChange = (event) => {
    const value = event.target.value;
    let _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
  };


  const onDateSearch = () => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = data.filter(item => isBetweenDateRange(item.created, start, end))

    setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const maskIdBodyTemplate = (row) => {

    return (
      <div className='maskid' onClick={() => viewIncident(row.id)}>
        {row.maskId}
      </div>
    );

  }
  const categoryFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={[{ name: 'Safety', value: 'Safety' }, { name: 'Health', value: 'Health' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const statusFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={[{ name: 'Reviewed', value: 'Reviewed' }, { name: 'Investigated', value: 'Investigated' }, { name: 'Under Investigation', value: 'Under Investigation' }, { name: 'Reported', value: 'Reported' }]} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const reportFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={user} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const ownerFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={owner} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }
  const inversFilterTemplate = (options) => {

    return (
      <React.Fragment>

        <MultiSelect value={options.value} options={inves} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
      </React.Fragment>
    );
  }

  const representativesItemTemplate = (option) => {
    return (
      <div className="flex align-items-center gap-2">

        <span>{option.value}</span>
      </div>
    );
  };

  const sortDate = (e) => {
    console.log(e)
    if (e.order === 1) {
        return e.data.sort((a, b) => {
            // Parse the dates using Moment.js
            const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY', moment.ISO_8601]);
            const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY', moment.ISO_8601]);

            // Compare the dates
            if (dateA.isBefore(dateB)) {
                return -1; // dateA comes before dateB
            } else if (dateA.isAfter(dateB)) {
                return 1; // dateA comes after dateB
            } else {
                return 0; // dates are equal
            }
        });
    } else {
      
        return e.data.sort((a, b) => {
            // Parse the dates using Moment.js
            const dateA = moment(a.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY',]);
            const dateB = moment(b.incidentDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ','Do MMM YYYY hh:mm A','Do MMM YYYY',]);
           
            // Compare the dates
            if (dateA.isBefore(dateB)) {
                return -1; // dateA comes before dateB
            } else if (dateA.isAfter(dateB)) {
                return 1; // dateA comes after dateB
            } else {
                return 0; // dates are equal
            }
        }).reverse()
    }
}
  return (
    <>

      <div>
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="">

                {/* <h4 className="card-title">Incident Investigation</h4> */}
                <div className="row">
                  <div className="col-12">
                    <div>
                      {/* <button type="button" className="btn btn-light btn-rounded mb-3 " onClick={(e) => { e.preventDefault(); setMdShow(true); }}><i className="mdi mdi-account-plus mr-2" /> Create New User</button> */}
                      {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
                      {/* <ThemeProvider theme={defaultMaterialTheme}>
                        <MaterialTable
                          columns={incidentColumns}
                          data={filterData}
                          title="Incident Investigation"
                          style={tableStyle}
                          actions={tableActions}
                          options={tableOptions}


                        />
                      </ThemeProvider> */}
                      {/* <DataTables thead={thead} options={options} /> */}

                      <DataTable value={filterData} paginator rows={10} header={header} filters={filters} onFilter={(e) => { setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        rowsPerPageOptions={[10, 25, 50]}
                        emptyMessage="No Data found." >

                        <Column field='maskId' body={maskIdBodyTemplate} header="ID" sortable showFilterMatchModes={false}></Column>

                        <Column field='incidentDate' header="Incident Date" sortable showFilterMatchModes={false} ortFunction={sortDate}></Column>

                        <Column field='title' header="Incident Title" showFilterMatchModes={false}></Column>

                        <Column field='IncidentCategory' header="Category" filter filterElement={categoryFilterTemplate} showFilterMatchModes={false}></Column>

                        <Column field='actualImpact' header="Impact Classification" ></Column>

                        {/* <Column field='status' header="Current Status" filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column> */}

                        <Column field='user.firstName' header="Reported By" filter filterElement={reportFilterTemplate} showFilterMatchModes={false}></Column>

                        <Column field='incidentOwner.firstName' header="Incident Owner" filter filterElement={ownerFilterTemplate} showFilterMatchModes={false}></Column>


                        <Column field='investigator.firstName' header="Lead Investigator"  filter filterElement={inversFilterTemplate} showFilterMatchModes={false}></Column>
                        {/* 
                        <Column header="Actions Taken" body={actionBodyTemplate} headerStyle={{ width: '15%' }}></Column>
                        {isIncidentTrigger &&
                          <Column header="Investigation Status" body={investBodyTemplate} sortable headerStyle={{ width: '16%' }}></Column>
                        } */}

                      </DataTable>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {incidentData && <IncidentInvestigationViewModal incidentData={incidentData} showModal={showModal} setShowModal={setShowModal} />}
    </>
  )
}

export default UnderIncidentInvestigation;
