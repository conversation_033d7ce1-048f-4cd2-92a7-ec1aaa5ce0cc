import React from 'react';
import { Dropdown } from 'primereact/dropdown';
import { ProgressBar } from 'primereact/progressbar';
import { Legend } from 'recharts';

const goals = [
    { label: 'Embed sustainability into Supply Chain', value: 'supply_chain' },
    { label: 'Improve energy efficiency', value: 'energy_efficiency' },
    // Add more goals as needed
];

const targets = [
    { name: 'Unsafe Act', value: 64, status: 'Unsafe Act' },
    { name: 'Unsafe Condition', value: 34, status: 'Unsafe Condition' },
    // Add more targets as needed
];

const GoalTargetsComponent = () => {
    const [selectedGoal, setSelectedGoal] = React.useState(goals[0].value);

    const legendData = [
        { name: 'Unsafe Act', color: '#F5C37B' },
        { name: 'Unsafe Condition', color: '#29C76F' },
        // { name: 'Completed', color: '#5B8FF7' },
        // { name: 'Ahead of time', color: '#315975' },
        // { name: 'Delayed', color: '#EE5724' }
    ];

    return (
    
          

            
                <div className='col d-flex align-items-center justify-center-center' style={{height:'100%'}}>
                    <div className="targets-section " style={{width:'100%'}}>
                        {/* <h4>Targets</h4> */}
                        {targets.map((target, index) => (
                            <div key={index} style={{ marginBottom: '20px' }}>
                                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                                    <i className="pi pi-circle-on" style={{ color: legendData.find(item => item.name === target.status).color, marginRight: '5px' }}></i>
                                    {target.name}
                                </div>
                                <ProgressBar value={target.value} color={target.color} />
                                {/* <div style={{ marginTop: '5px' }}>{`+${target.value}% achieved`}</div> */}
                            </div>
                        ))}
                    </div>
                </div>
         



    );
};

export default GoalTargetsComponent;
