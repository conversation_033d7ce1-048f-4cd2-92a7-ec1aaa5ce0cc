import React, { Component, useState, useEffect } from 'react'

import $ from "jquery";
import BasicTable from '../tables/BasicTable';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography'
import PropTypes from 'prop-types';
import Observation from './Observation';
import CardOverlay from './CardOverlay';
import ObservationOther from './ObservationOther';
import FilterLocation from './FilterLocation';
import LineChart from '../dashboard/LineChart';
import { Sticky, StickyContainer } from 'react-sticky';
import AllFilterLocation from './AllLocationFilter';
import { ACTION_URL, OBSERVATION_REPORT_URL, OBSERVATION_REPORT_BY_OTHERS_URL } from '../constants';
import { Button } from 'primereact/button';
import API from '../services/API';
import ActionCard from './ActionCard';
import AppSwitch from './AppSwitch';
import { Calendar } from 'primereact/calendar';
import TableHeader from './TableHeader';
import moment from 'moment'
import AllFilterLocationVertical from './AllFilterLocationVertical'
import { InputText } from 'primereact/inputtext';

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;



  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`observation-tabpanel-${index}`}
      aria-labelledby={`observation-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{}}>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `observation-tab-${index}`,
    'aria-controls': `observation-tabpanel-${index}`,
  };
}

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const Ehs = () => {
  const [actionData, setActionData] = useState([]);
  const [rendered, setRendered] = useState(0)
  const [obsData, setObsData] = useState([]);
  const [obsDataOther, setObsDataOther] = useState([]);
  const [obsDataOtherFilter, setObsDataOtherFilter] = useState([]);
  const [locationOneId, setlocationOneId] = useState('')
  const [locationTwoId, setlocationTwoId] = useState('')
  const [locationThreeId, setlocationThreeId] = useState('')
  const [locationFourId, setlocationFourId] = useState('')
  const [totalObservation, setTotalObservation] = useState(0)
  const [totalOtherObservation, setTotalOtherObservation] = useState(0)
  const [obsFilterData, setObsFilterData] = useState([])
  const [showFilter, setShowFilter] = useState(true)
  const [applyFilter, setApplyFilter] = useState(false)
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [clear, setClear] = useState(true)
  const [search, setSearch] = useState('')

  const [filter, setFilter] = useState([])

  const [dates, setDates] = useState([])

  useEffect(() => {
    getObservationData();
    getObservationDataOthers();
    getActionData();
  }, [])

  const displayStatus = (status) => {

    let returnText = '';

    switch (status) {

      case "At Risk - Closed":
        returnText = 'Reported & Closed'

        break;
      case "At Risk - Actions Assigned":
        returnText = 'Actions Assigned'

        break;
      case "At Risk - Actions Re-Assigned":
        returnText = 'Actions Re-Assigned'

        break;
      case "At Risk - Actions to be Verified":
        returnText = 'Actions Taken - Pending Verification'

        break;
      case "Safe - Closed":
        returnText = 'Reported & Closed'

        break;
      default:
        returnText = status
        break;

    }

    return returnText;

  }



  const getLastActionOwner = (items) => {
    // Iterate from the last item to the first
    for (let i = items?.length - 1; i >= 0; i--) {
      if (items[i].actionType === 'action_owner') {
        return items[i];
      }
    }
    // If no match is found, return null or any default value
    return null;
  };

  const getObservationData = async () => {

    const params = {
      "include": [{ "relation": "submitted" }, { "relation": "actions" }, { "relation": "workActivity" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }, { "relation": "actionOwner" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {
      const preprocessedData = response.data.map(item => {
        let conditionAct = '';
        try {
          const parsedRemarks = JSON.parse(item.remarks);
          conditionAct = parsedRemarks.condition_act ? `(${parsedRemarks.condition_act})` : '';
        } catch (error) {
          console.error('Error parsing remarks:', error);
        }

        console.log(item)
        const lastActionOwner = getLastActionOwner(item?.actions);
        return {
          ...item,
          'submitted.firstName': item.submitted ? item.submitted.firstName : '',
          'color': setColor(item),
          'created': moment(item.created).format('Do MMM YYYY'),
          'newStatus': item.rectifiedStatus === 'Yes' ? 'Reported & Rectified on Spot' : displayStatus(item.status),
          'type': item.isQR ? item.type + '(' + item.conditionAct + ')' : item.type + conditionAct,
          'closeDate': getCloseActionDate(item),
          'dueDate': lastActionOwner?.dueDate
        };
      });

      setObsData(sortDataByMaskId(preprocessedData))
      setObsFilterData(sortDataByMaskId(preprocessedData))

    }
  }
  const parseDate = (dateStr) => {
    const [day, month, year] = dateStr.replace('rd', '').replace('th', '').replace('st', '').replace('nd', '').split(' ');
    return new Date(`${month} ${day}, ${year}`);
  };
  const sortDataByMaskId = (data) => {
    return data.sort((a, b) => {
      const aId = parseInt(a.maskId.split('-').pop(), 10);
      const bId = parseInt(b.maskId.split('-').pop(), 10);

      if (bId !== aId) {
        return bId - aId; // Sort by maskId in descending order
      } else {
        return parseDate(b.created) - parseDate(a.created); // Sort by date in descending order if maskIds are equal
      }
    });
  };
  const sortByNumberAfterLastHyphen = (a, b) => {
    // Extract the numbers after the last hyphen in each ID
    const lastNumberA = parseInt(a.maskId.split('-').pop());
    const lastNumberB = parseInt(b.maskId.split('-').pop());

    // Compare the numbers
    return lastNumberB - lastNumberA;
  };
  const getCloseActionDate = (item) => {




    if (item.status === 'At Risk - Closed' || item.status === 'Action Verified - Closed' || item.status === 'Reported & Closed') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]

        return moment(last.createdDate).format('Do MMM YYYY')
      }






    } else {
      return ''
    }



  }

  const getObservationDataOthers = async () => {

    const params = {
      "include": [{ "relation": "submitted" }, { "relation": "actions" }, { "relation": "workActivity" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }, { "relation": "actionOwner" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_BY_OTHERS_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {

      const preprocessedData = response.data.map(item => {
        let conditionAct = '';
        try {
          const parsedRemarks = JSON.parse(item.remarks);
          conditionAct = parsedRemarks.condition_act ? `(${parsedRemarks.condition_act})` : '';
        } catch (error) {
          console.error('Error parsing remarks:', error);
        }
        const lastActionOwner = getLastActionOwner(item?.actions);
        return {
          ...item,
          'submitted.firstName': item.submitted ? item.submitted.firstName : '',
          'color': setColor(item),
          'created': moment(item.created).format('Do MMM YYYY'),
          'newStatus': item.rectifiedStatus === 'Yes' ? 'Reported & Rectified on Spot' : displayStatus(item.status),
          'type': item.isQR ? item.type + '(' + item.conditionAct + ')' : item.type + conditionAct,
          'closeDate': getCloseActionDate(item),
          'dueDate': lastActionOwner?.dueDate
        };
      });
      setObsDataOther(sortDataByMaskId(preprocessedData))
      setObsDataOtherFilter(sortDataByMaskId(preprocessedData))

    }
  }
  const setColor = (item) => {
    if (
      item.type === 'Safe' ||
      item.status === 'At Risk - Closed' ||
      item.status === 'Approved' ||
      item.dueDate === ''
    ) {
      return 'None';
    }

    // Parse dueDate
    const dueDate = moment(item.dueDate, [moment.ISO_8601, 'DD-MM-YYYY'], true);

    if (!dueDate.isValid()) {
      return 'None'; // Handle invalid dates
    }

    const today = moment().startOf('day');

    if (today.isSame(dueDate, 'day')) {
      return 'Due Soon';
    } else if (today.isAfter(dueDate, 'day')) {
      return 'Overdue';
    } else if (today.isBefore(dueDate, 'day')) {
      return 'Upcoming';
    }

    return 'None';
  };

  useEffect(() => {

  }, [])

  const getActionData = async () => {



    const response = await API.get(ACTION_URL);
    if (response.status === 200) {
      setActionData([...response.data].reverse())

    }
  }

  useEffect(() => {

    getActionData()
  }, [
    rendered
  ])

  const getFilteredActions = (applicationType, statusList) => {
    return actionData.filter(action =>
      action.application === applicationType && statusList.includes(action.status)
    );
  }
  const dates1 = [
    [new Date("2023-07-01").getTime(), 15000000],
    [new Date("2023-07-02").getTime(), 17000000],
    [new Date("2023-07-03").getTime(), 12000000],
    [new Date("2023-07-04").getTime(), 14000000],
    [new Date("2023-07-05").getTime(), 18000000],
    [new Date("2023-07-06").getTime(), 21000000],
    [new Date("2023-07-07").getTime(), 25000000],
    [new Date("2023-07-08").getTime(), 19000000],
    [new Date("2023-07-09").getTime(), 22000000],
    [new Date("2023-07-10").getTime(), 20000000],
  ];

  const dates2 = [
    [new Date("2023-07-01").getTime(), 17000000],
    [new Date("2023-07-02").getTime(), 20000000],
    [new Date("2023-07-03").getTime(), 15000000],
    [new Date("2023-07-04").getTime(), 17000000],
    [new Date("2023-07-05").getTime(), 28000000],
    [new Date("2023-07-06").getTime(), 24000000],
    [new Date("2023-07-07").getTime(), 24000000],
    [new Date("2023-07-08").getTime(), 22000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 500000000],
  ];

  const dates3 = [
    [new Date("2023-07-01").getTime(), 10000000],
    [new Date("2023-07-02").getTime(), 70000000],
    [new Date("2023-07-03").getTime(), 14000000],
    [new Date("2023-07-04").getTime(), 55000000],
    [new Date("2023-07-05").getTime(), 68000000],
    [new Date("2023-07-06").getTime(), 21000000],
    [new Date("2023-07-07").getTime(), 15000000],
    [new Date("2023-07-08").getTime(), 79000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 34000000],
  ];

  const dates4 = [
    [new Date("2023-07-01").getTime(), 55000000],
    [new Date("2023-07-02").getTime(), 22000000],
    [new Date("2023-07-03").getTime(), 66000000],
    [new Date("2023-07-04").getTime(), 64000000],
    [new Date("2023-07-05").getTime(), 78000000],
    [new Date("2023-07-06").getTime(), 91000000],
    [new Date("2023-07-07").getTime(), 35000000],
    [new Date("2023-07-08").getTime(), 69000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 20000000],
  ];

  const [value, setValue] = useState(0);
  const [topLevelValue, setTopLevelValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleTopLevelChange = (event, newValue) => {
    setTopLevelValue(newValue);
  };



  const data1 = [
    ['Construction Projects', 23],
    ['DC Ops', 44],
    ['Fitouts', 78],
  ];

  const columns1 = ['BU', 'Observation Intensity'];


  const data2 = [
    ['Construction Projects', 57],
    ['DC Ops', 35],
    ['Fitouts', 67],
  ];

  const columns2 = ['BU', 'Observation Intensity'];

  const data3 = [
    ['GMS 1.1 Management Governance', 567, 15],
    ['GMS 4.8 Confined Space Incident', 342, 25],
    ['GMS 4.7 Ground Disturbance Incident', 666, 67],
  ];

  const columns3 = ['GMS', '# of Observations', '% of Total Observations'];

  const data4 = [
    ['GMS 4.5 Fire and Explosion', 234, 67],
    ['GMS 4.4 Uncontrolled Release of Energy', 654, 54],
    ['GMS 4.2 Fall of Material/Object', 878, 78],
  ];

  const columns4 = ['GMS', '# of Observations', '% of Total Observations'];

  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {
    setDates([])
    const data = [
      locationOneId.name || '',
      locationTwoId.name || '',
      locationThreeId.name || '',
      locationFourId.name || '',

    ];

    if (startDate !== null && endDate !== null) {
      const date = [
        moment(startDate).format('MMM YYYY'),
        moment(endDate).format('MMM YYYY')
      ]
      setDates(date)
    }


    setFilter(data)
    setSearch('')

    setlocationOneId(locationOneId.id)
    setlocationTwoId(locationTwoId.id)
    setlocationThreeId(locationThreeId.id)
    setlocationFourId(locationFourId.id)
    setStartDate(startDate)
    setEndDate(endDate)
  };

  useEffect(() => {

    const filterData = (data) => {
      return data.filter(item => {
        return (
          (!locationOneId || item.locationOneId === locationOneId) &&
          (!locationTwoId || item.locationTwoId === locationTwoId) &&
          (!locationThreeId || item.locationThreeId === locationThreeId) &&
          (!locationFourId || item.locationFourId === locationFourId) &&
          (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))

        );
      });
    };

    setObsData(filterData(obsFilterData));
    setObsDataOther(filterData(obsDataOtherFilter));
    setFinalObsData(filterData(obsFilterData))
    setFinalObsDataOther(filterData(obsDataOtherFilter))
    // if (locationOneId === '' && locationTwoId === '' && locationThreeId === '' && locationFourId === '') {
    //   setObsData(obsFilterData); setObsDataOther(obsDataOtherFilter)
    // }

  }, [locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate])

  const [finalObsOther, setFinalObsDataOther] = useState([])
  const [finalObs, setFinalObsData] = useState([])
  useEffect(() => {


    if (!applyFilter) {
      setObsData(obsFilterData.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))
      setObsDataOther(obsDataOtherFilter.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
    } else {
      setObsData(finalObs.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))
      setObsDataOther(finalObsOther.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())));
    }

  }, [search])

  const onDateSearch = (startDate, endDate) => {
    const [from, to] = [startDate, endDate];
    if (from === null && to === null) return true;
    if (from !== null && to === null) return true;
    if (from === null && to !== null) return true;
    const start = moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month');
    const end = moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month');

    //  console.log(start,end)
    const searchData = obsDataOtherFilter.filter(item => isBetweenDateRange(item.created, start, end))

    const searchData1 = obsFilterData.filter(item => isBetweenDateRange(item.created, start, end))

    setObsData(searchData1)

    setObsDataOther(searchData)

    // setFilterData(searchData)

    // return isBetweenDateRange(value, moment(from, 'ddd MMM DD YYYY HH:mm:ss ZZ').startOf('month'), moment(to, 'ddd MMM DD YYYY HH:mm:ss ZZ').endOf('month'))
  }

  const resetData = () => {
    setObsData(obsFilterData)

    setObsDataOther(obsDataOtherFilter)
  }
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const onApplyFilter = (type) => {
    setApplyFilter(type)
    setShowFilter(true)
  }

  const onCancelFilter = (type) => {
    setApplyFilter(false)
    setShowFilter(true)
    setClear(!clear)
  }

  const onGlobalFilterChange = (e) => {
    setSearch(e)
  }
  const filterData = (data, e) => {
    return data.filter(item => item.maskId.includes(e));
  }
  const setOnSearch = (e) => {
    if (e) {
      setObsData(filterData(obsData, e));
      setObsDataOther(filterData(obsDataOther, e));
    } else {
      setObsData(obsFilterData);
      setObsDataOther(obsDataOtherFilter);
    }
  }





  // useEffect(() => {
  //   

  // }, [search])

  // const [filterTODashboard,setFilterTODashboard] =useState([])
  return (
    <>
      <CardOverlay>
        <StickyContainer>

          <AppSwitch value={{ label: 'Observation', value: 'ehs' }} />

          <div className='row'>
            <div className='col-12 mb-4'>
              <div className='d-flex align-items-center'>
                <div className='col-1 d-flex'>
                  {!applyFilter ?
                    <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                      <div className='d-flex flex-column align-items-end'>
                        <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                        <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                      </div>
                      <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                    </div>
                    :
                    <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                      <div className='d-flex flex-column align-items-end'>
                        <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                        <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                      </div>
                      <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                    </div>
                  }

                </div>
                <div className='col-9 d-flex'>
                  {applyFilter && <>
                    {filter.length !== 0 &&
                      <h5><b>Location : </b>{filter.map((location, index) => (
                        location !== '' &&
                        <React.Fragment key={index}>
                          <span className='loc-box'>{location}</span>
                          {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                        </React.Fragment>
                      ))}</h5>
                    }
                    {dates.length !== 0 &&
                      <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                        <React.Fragment key={index}>
                          <span className='loc-box'>{location}</span>
                          {index < dates.length - 1 && " To "}
                        </React.Fragment>
                      ))}</h5>
                    }
                  </>}
                </div>
                <div className='col-2'>
                  <div className="p-input-icon-left ">
                    <i className="fa fa-search" />
                    <InputText type="search" style={{ borderRadius: 8 }} placeholder='Search' onChange={(e) => setSearch(e.target.value)} />
                  </div>
                </div>
              </div>
            </div>
            <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>

              <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
            </div>
            {/* <TableHeader onDateSearch={onDateSearch} setFilterData={resetData}/> */}
            <div className={!showFilter ? 'col-9' : 'col-12'}>
              <Box sx={{ width: '100%' }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={topLevelValue} onChange={handleTopLevelChange} aria-label="observation report table">
                    <Tab
                      label={
                        <Typography variant="body1" style={customFontStyle}>
                          My Actions <span className='headerCount'>{getFilteredActions('Observation', ['open', 'returned']).length}</span>
                        </Typography>
                      }
                      {...a11yProps(0)}
                    />
                    <Tab
                      label={
                        <Typography variant="body1" style={customFontStyle}>
                          Observations Reported by You <span className='headerCount'>{obsData.length}</span>
                        </Typography>
                      }
                      {...a11yProps(1)}
                    />
                    <Tab
                      label={
                        <Typography variant="body1" style={customFontStyle}>
                          Observations Reported by Others <span className='headerCount'>{obsDataOther.length}</span>
                        </Typography>
                      }
                      {...a11yProps(2)}
                    />
                  </Tabs>
                </Box>
                <CustomTabPanel value={topLevelValue} index={0}>

                  <Box sx={{ width: '100%' }}>


                    {/* {getFilteredActions('Observation', ['open', 'returned']).map(action => ( */}
                    <ActionCard action={getFilteredActions('Observation', ['open', 'returned'])} applicationType="Observation" setRendered={setRendered} />
                    {/* ))} */}

                  </Box>
                </CustomTabPanel>
                <CustomTabPanel value={topLevelValue} index={1}>

                  <Observation setTotalObservation={setTotalObservation} obsdata={obsData} clear={clear} search={search} />
                </CustomTabPanel>
                <CustomTabPanel value={topLevelValue} index={2}>
                  <ObservationOther setTotalOtherObservation={setTotalOtherObservation} obsdata={obsDataOther} clear={clear} search={search} />

                </CustomTabPanel>

              </Box>
            </div>
          </div>




        </StickyContainer>


      </CardOverlay>
    </>
  )
}

export default Ehs
