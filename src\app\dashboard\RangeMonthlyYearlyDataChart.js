import React, { useState, useEffect, useRef } from 'react';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import HighchartsWrapper from './HighchartsWrapper';
import moment from 'moment';
import html2canvas from 'html2canvas';
import { Button } from '@material-ui/core';
import GetAppIcon from '@material-ui/icons/GetApp'; // Material UI Download Icon

const RangeMonthlyYearlyDataChart = ({
    initialStartDate,
    initialEndDate,
    data,
    preprocessData,
    calculateTRIR,
    chartId,
    manHoursKey,
    chartConfig
}) => {
    const [startDate, setStartDate] = useState(initialStartDate);
    const [endDate, setEndDate] = useState(initialEndDate);
    const [filteredData, setFilteredData] = useState([]);
    const [currentMonthData, setCurrentMonthData] = useState(null);
    const headerRef = useRef(null);
    useEffect(() => {
        if (startDate && endDate) {
            const startMoment = moment(startDate).startOf('month');
            const endMoment = moment(endDate).endOf('month');

            // Step 1: Filter data within the selected date range
            const filtered = data.filter(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.isSameOrAfter(startMoment) && itemDateMoment.isSameOrBefore(endMoment);
            });

            // Step 2: Extract existing months from the filtered data
            const existingMonths = new Set(filtered.map(item => moment(`${item.year}-${item.month}`, "YYYY-MMM").format("YYYY-MMM")));

            // Step 3: Generate all months between startDate and endDate
            const allMonths = [];
            let tempMoment = startMoment.clone();
            while (tempMoment.isSameOrBefore(endMoment, 'month')) {
                allMonths.push(tempMoment.format("YYYY-MMM"));
                tempMoment.add(1, 'month');
            }

            // Step 4: Fill missing months with zero values
            allMonths.forEach(monthYear => {
                if (!existingMonths.has(monthYear)) {
                    const [year, month] = monthYear.split('-');
                    filtered.push({
                        year: parseInt(year),
                        month: month,
                        country: "N/A",
                        bu: "N/A",
                        level: "N/A",
                        averageNoOfSTTGdcEmployeesPerDay: 0,
                        averageNoOfContractorEmployeesPerDay: 0,
                        totalNoOfEmployees: 0,
                        monthlyHoursWorked: 0,
                        cumulativeWorkHours: 0,
                        noOfSafetyInductionsConducted: 0,
                        noOfToolboxMeetingsSafetyBriefingsSafeStarts: 0,
                        noOfEHSTrainings: 0,
                        noOfEHSInspectionsAudits: 0,
                        noOfManagementSiteWalkInspection: 0,
                        authorityNGOUnionVisits: 0,
                        noOfSafeObservations: 0,
                        noOfAtRiskObservations: 0,
                        totalNoOfObservations: 0,
                        noOfFatality: 0,
                        noOfDaysAwayFromWorkCasesLTICases: 0,
                        noOfRestrictedWorkCasesLightDutyJobTransfer: 0,
                        noOfLossOfConsciousnessCases: 0,
                        noOfMedicalTreatmentCases: 0,
                        noOfHealthRelatedCases: 0,
                        noOfRecordableIncidentCases: 0,
                        legalAndOtherNonCompliances: 0,
                        noticesOrStopWorkOrders: 0,
                        authorityReportableIncident: 0,
                        noOfNearMissCases: 0,
                        noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence: 0,
                        noOfFirstAidCases: 0,
                        authorityNgoUnionVisits: null
                    });
                }
            });

            // Step 5: Sort data in chronological order
            filtered.sort((a, b) => moment(`${a.year}-${a.month}`, "YYYY-MMM").diff(moment(`${b.year}-${b.month}`, "YYYY-MMM")));

            // Step 6: Preprocess and update the state
            const processedFilteredData = filtered.length > 0 ? preprocessData(filtered) : [];

            setFilteredData(processedFilteredData);

            // Also check the current month's data
            const currentMonthFiltered = preprocessData(data.filter(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.isSame(moment(), 'month');
            }));

            setCurrentMonthData(currentMonthFiltered);
        }
    }, [startDate, endDate, data]);

    // Ensure we do not pass an empty dataset to the chart
    const finalChartData = filteredData.length > 0 ? calculateTRIR(filteredData, manHoursKey) : [];

    // Extract categories (months) and chart data
    const categories = finalChartData.map((item) => {
        const shortYear = item.year.toString().slice(-2); // Extract last two digits
        return `${item.month} '${shortYear}`;
    });

    const barData = finalChartData.map((item) => item[chartConfig.barKey] || 0);
    const lineData = finalChartData.map((item) => item[chartConfig.lineKey] || 0);

    // Highcharts configuration
    const options = {
        chart: {
            zoomType: 'xy',
        },
        title: {
            text: ``,
        },
        xAxis: {
            categories: categories,
            crosshair: true, labels: {
                style: {
                    fontSize: '15px', // Adjust this value as needed

                }
            }
        },
        yAxis: [
            {
                title: {
                    text: chartConfig.barLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                },
                opposite: false,
                allowDecimals: false,
                tickInterval: 1,
                min: 0,
                labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
            {
                title: {
                    text: chartConfig.lineLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                },
                opposite: true, labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
        ],
        tooltip: {
            shared: true,
        },
        legend: {
            itemStyle: {
                fontSize: '15px',  // Adjust the font size for series name

            }
        },
        series: [
            {
                name: chartConfig.barLegend,
                type: 'column',
                data: barData,
                color: chartConfig.barColor || '#007bff',
                yAxis: 0,
                dataLabels: {
                    enabled: true,
                    inside: true,
                    format: '{y}',
                    verticalAlign: 'bottom', // Align at the bottom of the bar
                    align: 'center',
                    y: -5,
                    style: {
                        textOutline: 'white',
                        color: 'black',
                        fontSize: '15px', // Adjust the font size for data labels

                    }
                },
            },
            {
                name: chartConfig.lineLegend,
                type: 'spline',
                data: lineData,
                color: chartConfig.lineColor || '#FF5733',
                yAxis: 1,
                dataLabels: {
                    enabled: true,
                    format: '{y}', style: {
                        fontSize: '15px', // Adjust the font size for data labels

                    }
                },
            },
        ],

    };

    const downloadHeader = async () => {
        if (headerRef.current) {
            const canvas = await html2canvas(headerRef.current);
            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = `chart-header-${chartId}.png`;
            link.click();
        }
    };

    return (
        <div className='card shadow'>
            <div className='card-body' ref={headerRef}>
                <div className='chart-header d-flex justify-content-between'>
                    <div className="left-content">
                        <h5 className='font-weight-bold'>
                            {chartConfig.title} for every {' '}
                            <select
                                id={`manHoursChart${chartId}`}
                                value={manHoursKey}
                                onChange={(e) => chartConfig.onManHoursChange(`chart${chartId}`, Number(e.target.value))}
                            >
                                <option value={200000}>200,000</option>
                                <option value={1000000}>1,000,000</option>
                            </select>{' '}
                            hours worked  From:
                            <Calendar
                                value={startDate}
                                onChange={(e) => setStartDate(e.value)}
                                view="month"
                                yearNavigator
                                yearRange="2010:2030"
                                monthNavigator
                                showIcon
                                dateFormat="M yy"
                                placeholder="Select Start Month"
                                className='w-25'
                            />
                            To:
                            <Calendar
                                value={endDate}
                                onChange={(e) => setEndDate(e.value)}
                                view="month"
                                yearNavigator
                                yearRange="2010:2030"
                                monthNavigator
                                showIcon
                                dateFormat="M yy"
                                placeholder="Select End Month"
                                className="w-25"
                                maxDate={new Date(moment().endOf('month').format('YYYY-MM-DD'))} // Prevent selecting future months
                            />
                        </h5>

                    </div>
                    <div>
                        <GetAppIcon
                            onClick={downloadHeader}
                            style={{
                                fontSize: '18px',
                                cursor: 'pointer',
                                color: 'black',
                            }}
                        />
                    </div>
                </div>
                <HighchartsWrapper options={options} />
            </div>
        </div>
    );
};

export default RangeMonthlyYearlyDataChart;
