import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import { parse, isWithinInterval } from "date-fns";

const HighRiskActivityPieChart = ({ dateRange, data }) => {
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    const processData = () => {
      if (!data?.highRiskBreakdownByMonth) {
        setChartData(null);
        return;
      }

      const { highRiskBreakdownByMonth } = data;

      // Parse dateRange into start and end dates
      const [startDate, endDate] = dateRange.map((date) => new Date(date));

      // Filter the breakdown by dateRange
      const filteredBreakdown = highRiskBreakdownByMonth.filter((month) => {
        const monthDate = parse(month.monthYear, "yyyy-MM", new Date());
        return isWithinInterval(monthDate, { start: startDate, end: endDate });
      });

      // Aggregate data for the pie chart
      const totalCounts = {
        "Work at Height": 0,
        "Energised System": 0,
        "Hot Works": 0,
        "Non-High Risk Activities": 0,  // Renamed from "Others"
      };

      filteredBreakdown.forEach((month) => {
        month.breakdown.forEach((activity) => {
          const label = activity.label === "Others" ? "Non-High Risk Activities" : activity.label;
          if (totalCounts[label] !== undefined) {
            totalCounts[label] += activity.count;
          }
        });
      });

      const labels = Object.keys(totalCounts);
      const dataValues = Object.values(totalCounts);

      setChartData({ labels, data: dataValues });
    };

    processData();
  }, [dateRange, data]);

  // Highcharts Pie Chart Configuration
  const options = chartData
    ? {
        chart: {
          type: "pie",
        },
        title: {
          text: ``,
        },
        tooltip: {
          pointFormat: "<b>{point.name}</b>: {point.y}",
        },
        accessibility: {
          point: {
            valueSuffix: " occurrences",
          },
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: "pointer",
            dataLabels: {
              enabled: true,
              format: "<b>{point.name}</b>: {point.y}",
            },
            showInLegend: true, // Enables legend interactivity
          },
        },
        legend: {
          enabled: true,
          labelFormatter: function () {
            return `${this.name} (${this.y})`; // Custom legend format
          },
        },
        series: [
          {
            name: "Occurrences",
            colorByPoint: true, // 🔹 Uses Highcharts default colors
            data: chartData.labels.map((label, index) => ({
              name: label,
              y: chartData.data[index],
            })),
          },
        ],
        exporting: {
          enabled: true,
          buttons: {
            contextButton: {
              menuItems: [
                "downloadPNG",
                "downloadJPEG",
                "downloadPDF",
                "downloadSVG",
                "separator",
                "downloadCSV",
                "downloadXLS",
              ],
            },
          },
        },
      }
    : null;

  return <>{options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}</>;
};

export default HighRiskActivityPieChart;
