import React, { useState, useEffect, useRef } from 'react';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import 'primereact/resources/themes/saga-blue/theme.css';
import HighchartsWrapper from './HighchartsWrapper';
import moment from 'moment';
import html2canvas from 'html2canvas';
import { Button } from '@material-ui/core';
import GetAppIcon from '@material-ui/icons/GetApp'; // Material UI Download Icon

const MonthlyYearlyDataChart = ({
    initialDate,
    data,
    preprocessData,
    calculateTRIR,
    chartId,
    manHoursKey,
    chartConfig
}) => {
    const [selectedDate, setSelectedDate] = useState(initialDate);
    const [filteredData, setFilteredData] = useState([]);
    const headerRef = useRef(null);

    // Accepts month as "Jan", "January", 1, or "1"
    function parseMonthYear(month, year) {
        let m;
        const y = Number(year);

        if (typeof month === "number" || /^\d+$/.test(month)) {
            const mm = Number(month) - 1; // JS month index 0-11
            m = moment({ year: y, month: mm, day: 1 });
        } else {
            m = moment(`${month} ${year}`, "MMM YYYY", true);
            if (!m.isValid()) m = moment(`${month} ${year}`, "MMMM YYYY", true);
        }
        return m.startOf("month");
    }


    function toMonthly(rows) {
        if (!Array.isArray(rows) || rows.length === 0) return [];

        const sorted = [...rows]
            .filter(r => r && r.month != null && r.year != null)
            .sort((a, b) => parseMonthYear(a.month, a.year) - parseMonthYear(b.month, b.year));

        if (sorted.length === 0) return [];

        // Detect cumulative series (monotonic non-decreasing)
        const isCumInc = sorted.every((r, i) =>
            i === 0 || ((r.noOfRecordableIncidentCases ?? 0) >= (sorted[i - 1].noOfRecordableIncidentCases ?? 0))
        );
        const isCumHrs = sorted.every((r, i) =>
            i === 0 || ((r.monthlyHoursWorked ?? 0) >= (sorted[i - 1].monthlyHoursWorked ?? 0))
        );

        return sorted.map((r, i) => {
            const prev = sorted[i - 1];
            return {
                ...r,
                noOfRecordableIncidentCases: isCumInc
                    ? Math.max(0, (r.noOfRecordableIncidentCases ?? 0) - (prev?.noOfRecordableIncidentCases ?? 0))
                    : (r.noOfRecordableIncidentCases ?? 0),
                monthlyHoursWorked: isCumHrs
                    ? Math.max(0, (r.monthlyHoursWorked ?? 0) - (prev?.monthlyHoursWorked ?? 0))
                    : (r.monthlyHoursWorked ?? 0),
            };
        });
    }


    function calculatePartialRollingTRIR(src, trirBase) {
        // Guard: empty or bad input
        if (!Array.isArray(src) || src.length === 0) {
            console.warn("calculatePartialRollingTRIR: empty src");
            return [];
        }

        // Remove rows with missing month/year or invalid dates
        const cleaned = src.filter(r => {
            if (!r || r.month == null || r.year == null) return false;
            const d = parseMonthYear(r.month, r.year);
            return d.isValid();
        });

        if (cleaned.length === 0) {
            console.warn("calculatePartialRollingTRIR: no valid rows after cleaning");
            return [];
        }

        // clone + sort; avoid mutating caller data
        const data = [...cleaned].sort(
            (a, b) => parseMonthYear(a.month, a.year) - parseMonthYear(b.month, b.year)
        );

        const earliestDate = parseMonthYear(data[0].month, data[0].year);
        const result = [];

        for (let i = 0; i < data.length; i++) {
            const currentItem = data[i];
            const currentDate = parseMonthYear(currentItem.month, currentItem.year);

            // Inclusive 12-month window: current minus 11 months through current
            const start = currentDate.clone().subtract(11, "months");

            const rollingData = data.filter(item => {
                const d = parseMonthYear(item.month, item.year);
                return d.isSameOrAfter(start, "month") && d.isSameOrBefore(currentDate, "month");
            });

            const totalIncidents = rollingData.reduce((s, r) => s + (r.noOfRecordableIncidentCases ?? 0), 0);
            const totalHours = rollingData.reduce((s, r) => s + (r.monthlyHoursWorked ?? 0), 0);

            const trir = totalHours > 0 ? (totalIncidents * trirBase) / totalHours : 0;
            const monthsFromStart = currentDate.diff(earliestDate, "months") + 1;

            result.push({
                year: currentItem.year,
                month: currentItem.month,
                noOfRecordableIncidentCases: currentItem.noOfRecordableIncidentCases ?? 0,
                rollingTRIR: Number(trir.toFixed(3)),
                isPartial: monthsFromStart < 12,
            });
        }

        return result;
    }





    useEffect(() => {
        if (selectedDate) {
            const selectedDateMoment = moment(selectedDate);

            // 🔹 STEP 1: PREPROCESS & CALCULATE TRIR FOR ALL DATA (Jan 2023 - Latest Available Month)
            const raw = typeof preprocessData === "function" ? preprocessData(data) : (data || []);
            let preProcessedData = Array.isArray(raw) ? raw : [];

            // Fill missing months (your existing code block here)

            // Ensure monthly (absolute) values before rolling calc
            const monthlyData = toMonthly(preProcessedData);

            // Guard: if still empty, bail early to avoid chart errors
            if (!monthlyData.length) {
                console.warn("No data to chart after preprocessing");
                setFilteredData([]);
                return;
            }

            // Rolling TRIR using inclusive 12M window
            const processedData = calculatePartialRollingTRIR(monthlyData, manHoursKey);

            // Show only the last 12 months ending at selected month (inclusive)
            const startForView = selectedDateMoment.clone().subtract(11, 'months');
            let filtered = processedData.filter(item => {
                const d = parseMonthYear(item.month, item.year);
                return d.isSameOrAfter(startForView, 'month') && d.isSameOrBefore(selectedDateMoment, 'month');
            });






            filtered.sort((a, b) => {
                // Sort by Year first
                if (a.year !== b.year) return a.year - b.year;

                // Convert "Jan", "Feb", etc. into numeric values (01, 02, ..., 12)
                const monthA = moment(a.month, "MMM").format("MM");
                const monthB = moment(b.month, "MMM").format("MM");

                return parseInt(monthA) - parseInt(monthB);
            });
            setFilteredData(filtered);
        }
    }, [selectedDate, data, manHoursKey, preprocessData]);

    const finalChartData = filteredData.length > 0 ? filteredData : [];
    const categories = finalChartData.map((item) => {
        const shortYear = item.year.toString().slice(-2); // Extract last two digits
        return `${item.month} '${shortYear}`;
    });
    const barData = finalChartData.map((item) => item[chartConfig.barKey] || 0);
    const lineData = finalChartData.map((item) => item[chartConfig.lineKey] || 0);

    const options = {
        chart: { zoomType: 'xy' },
        title: { text: `` },
        xAxis: {
            categories, crosshair: true, labels: {
                style: {
                    fontSize: '15px', // Adjust this value as needed

                }
            }
        },
        yAxis: [
            {
                title: {
                    text: chartConfig.barLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                },
                opposite: false,
                allowDecimals: false,
                tickInterval: 1,
                min: 0,
                labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
            {
                title: {
                    text: chartConfig.lineLegend, style: {
                        fontSize: '15px', // Adjust font size for Y-Axis title

                    }
                }, opposite: true, labels: {
                    style: {
                        fontSize: '15px', // Adjust this value as needed

                    }
                }
            },
        ],
        tooltip: { shared: true },
        legend: {
            itemStyle: {
                fontSize: '15px',  // Adjust the font size for series name

            }
        },
        series: [
            {
                name: chartConfig.barLegend,
                type: 'column',
                data: barData,
                color: chartConfig.barColor || '#007bff',
                yAxis: 0,
                dataLabels: {
                    enabled: true,
                    inside: true,
                    format: '{y}',
                    verticalAlign: 'bottom', // Align at the bottom of the bar
                    align: 'center',
                    y: 0,
                    style: {
                        textOutline: 'white',
                        color: 'black',
                        fontSize: '15px', // Adjust the font size for data labels

                    }
                },
            },
            {
                name: chartConfig.lineLegend,
                type: 'spline',
                data: lineData,
                color: chartConfig.lineColor || '#FF5733',
                yAxis: 1,
                dataLabels: {
                    enabled: true, format: '{y}', style: {
                        fontSize: '15px', // Adjust the font size for data labels
                        fontWeight: 'normal'
                    }
                },
            },
        ],
    };

    // Function to download the chart header
    const downloadHeader = async () => {
        if (headerRef.current) {
            const canvas = await html2canvas(headerRef.current);
            const link = document.createElement('a');
            link.href = canvas.toDataURL('image/png');
            link.download = `chart-header-${chartId}.png`;
            link.click();
        }
    };

    return (
        <div className='card shadow'>
            <div className='card-body' ref={headerRef}>
                <div className='chart-header d-flex justify-content-between' >
                    <div className="left-content">
                        <h5 className='font-weight-bold'>
                            {chartConfig.title} for every {' '}
                            <select
                                id={`manHoursChart${chartId}`}
                                value={manHoursKey}
                                onChange={(e) => chartConfig.onManHoursChange(`chart${chartId}`, Number(e.target.value))}
                            >
                                <option value={200000}>200,000</option>
                                <option value={1000000}>1,000,000</option>
                            </select>{' '}
                            hours worked as of{' '}
                            <Calendar
                                value={selectedDate}
                                onChange={(e) => setSelectedDate(e.value)}
                                view="month"
                                yearNavigator
                                yearRange="2010:2030"
                                monthNavigator
                                showIcon
                                dateFormat="M yy"
                                placeholder="Select Month"
                                minDate={moment().set({ year: 2023, month: 0, date: 1 }).toDate()}
                                maxDate={moment().endOf('month').toDate()}
                            />
                        </h5>
                    </div>
                    <div>
                        <GetAppIcon
                            onClick={downloadHeader}
                            style={{
                                fontSize: '18px',
                                cursor: 'pointer',
                                color: 'black',
                            }}
                        />
                    </div>
                </div>
                <HighchartsWrapper options={options} />
            </div>
        </div>
    );
};

export default MonthlyYearlyDataChart;
