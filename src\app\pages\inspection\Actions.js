import React, { useEffect, useState } from "react";
import moment from 'moment';
import TakeActionModal from "./../TakeActionModal";
import VerifyActionModal from "./../VerifyActionModal";
import IncidentInvestigationViewModal from "./../IncidentInvestigationViewModal";
import { REPORT_INCIDENT_URL_WITH_ID } from "../../constants";
import API from "../../services/API";
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import IncidentInformationModal from "./../IncidentInformationModal";
import IncidentInvestigationModal from "./../IncidentInvestigationModal";
import InspectionModal from "./../InspectionModal";
import { Modal, Button, Form } from 'react-bootstrap';
import AuditPanel from './../AuditPanel';

import ActionAuditFinding from "./../ActionAuditFinding";
import ReportDataModal from "./../ReportDataModal";
import ObsVerifyModal from "./../ObsVerifyModal";

const Actions = ({ action, applicationType, setRendered }) => {
 

    const [selectedAudit, setSelectedAudit] = useState('')
    const [actionModal, setActionModal] = useState(false)
    const currentDate = moment();
    const dueDateMoment = moment(action.dueDate, 'DD/MM/YYYY');
    const isPastDue = dueDateMoment.isBefore(currentDate);
    const isToday = dueDateMoment.isSame(currentDate, 'day');
    const [actionOne, setActionOne] = useState([])
    const [data, setData] = useState([])
    const [requiredAction, setRequiredAction] = useState([])
    const [reportModal, setReportModal] = useState(false)
    const [auditModal, setAuditModal] = useState(false)
    const [auditFindingModal, setAuditFindingModal] = useState(false)
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        'applicationDetails.maskId': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.title': { value: null, matchMode: FilterMatchMode.IN },
        'applicationDetails.incidentDate': { value: null, matchMode: FilterMatchMode.IN },
        description: { value: null, matchMode: FilterMatchMode.IN },
        dueDate: { value: null, matchMode: FilterMatchMode.IN },
        state: { value: null, matchMode: FilterMatchMode.IN },
    });
    const handleAudit = (id) => {
        setSelectedAudit(id)
        setAuditModal(true)
    }
    const [findingId, setFindingId] = useState('')
    const handleAuditFindings = (id, id1) => {
        setFindingId(id1)
        setSelectedAudit(id)
        setAuditFindingModal(true)
    }
    useEffect(() => {
        let required = []

        const final = action.map((item) => {
            item.state = renderActionTxt(item.actionType, item.actionToBeTaken)

            required.push({ 'name': renderActionTxt(item.actionType), 'value': renderActionTxt(item.actionType) })

            return item
        })


        setData(final)
        setRequiredAction(required.filter((ele, ind) => ind === required.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))



    }, [action])

    const renderActionTxt = (actionType, actionToBeTaken = 'Take Inspection Actions') => {
        let actionTxt = ''
        if (actionType === 'dcso_approver') actionTxt = 'Review, Isolate & Approve'
        else if (actionType === 'assessor') actionTxt = 'Review & Assess'
        else if (actionType === 'normalization') actionTxt = 'Normalize'
        else if (actionType === 'approver') actionTxt = 'Review and Approve'
        else if (actionType === 'take_actions_control') actionTxt = 'Implement Control Measures'
        else if (actionType === 'take_actions_control_post') actionTxt = 'Post Investigation Control Measures Assigned'
        else if (actionType === 'take_actions_ra') actionTxt = 'Review and Update RA / SWP'
        else if (actionType === 'verify_actions') actionTxt = 'Verify Implementation'
        else if (actionType === 'retake_actions') actionTxt = 'Retake Actions'
        else if (actionType === 'reviewer') actionTxt = 'OBS - Verify Actions'
        else if (actionType === 'action_owner') actionTxt = 'OBS - Take Actions'
        else if (actionType === 'verify_investigation_actions') actionTxt = 'Approve Investigation'
        else if (actionType === 'take_investigation_actions') actionTxt = 'Take Control Actions'
        else if (actionType === 'ins_take_actions_control') actionTxt = `Take Action: ${actionToBeTaken}`
        else if (actionType === 'ins_retake_actions') actionTxt = `Retake Action: ${actionToBeTaken}`
        else if (actionType === 'ins_verify_actions') actionTxt = 'Verify Inspection Actions'
        else if (actionType === 'inspect') actionTxt = 'Take Actions'
        else if (actionType === 'audit') actionTxt = 'Perform Audit'
        else if (actionType === 'audit_take_actions') actionTxt = 'Identify Root Cause(s) and take Corrective Actions'
        else if (actionType === 'aud_verify_actions') actionTxt = 'Verify identified root cause and corrective actions taken'
        else if (actionType === 'aud_retake_actions') actionTxt = 'Resubmit Identify Root Cause(s) and take Corrective Actions'
        else if (actionType === 'review_incident') actionTxt = 'Review Incident'
        else if (actionType === 'conduct_investigation') actionTxt = 'Conduct Investigation'
        else if (actionType === 'report') actionTxt = 'Submit Monthly Report'


        return actionTxt
    }

    let actionTypeText;
    switch (applicationType) {
        case 'Observation':
            actionTypeText = renderActionTxt(action.actionType)
            break;
        case 'INCIDENT':
            actionTypeText = renderActionTxt(action.actionType)
            break;
        case 'PermitToWork':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'Inspection':
            actionTypeText = renderActionTxt(action.actionType, action.actionToBeTaken);
            break;
        case 'Audit':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'AuditFinding':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        case 'Report':
            actionTypeText = renderActionTxt(action.actionType);
            break;
        default:
            actionTypeText = '';
    }

    const [showReviewModal, setShowReviewModal] = useState(false)
    const [showConductInvestigationModal, setShowConductInvestigationModal] = useState(false)
    const [showModal, setShowModal] = useState(false)
    const [showVerifyModal, setShowVerifyModal] = useState(false)
    const [showInvestigationModal, setShowInvestigationModal] = useState(false)
    const [showInspectionModal, setShowInspectionModal] = useState(false)

    const [reportData, setReportData] = useState(null)
    const handleReportModal = (data) => {
        setReportData(data)
        setReportModal(true)
    }

    const handleInspectionAction = (action) => {

        setShowInspectionModal(true)

    }
    const handleAction = (action) => {

        setActionOne(action)

        if (action.actionType === 'verify_investigation_actions') {
            getReportIncident(action.objectId)
        }
        else if (action.actionType === 'report') {
            setReportModal(true)
        }
        else if (['inspect'].includes(action.actionType)) {
            setShowInspectionModal(true)
        }
        else if (['verify_actions', 'reviewer', 'ins_verify_actions', 'aud_verify_actions'].includes(action.actionType)) {
            setShowVerifyModal(true)
        } else if (['conduct_investigation'].includes(action.actionType)) {
            getReportIncidentData(action.objectId); setShowConductInvestigationModal(true);
        } else if (['review_incident'].includes(action.actionType)) {
            getReportIncidentData(action.objectId); setShowReviewModal(true)
        } else {
            setShowModal(true)
        }

    }

    const [incidentData, setIncidentData] = useState({});
    const getReportIncident = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

        const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;

            setIncidentData(data)

            setShowInvestigationModal(true)
        }
    }

    const getReportIncidentData = async (id) => {

        const uriString = { include: ['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'locationFive', 'locationSix', 'incidentCircumstanceCategory', 'incidentCircumstanceDescription', 'incidentCircumstanceType', 'lighting', 'riskCategory', 'surfaceCondition', 'surfaceType', 'workActivity'] }

        const url = `${REPORT_INCIDENT_URL_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        const response = await API.get(url);
        if (response.status === 200) {

            const data = response.data;

            setIncidentData(data)


        }
    }

    useEffect(() => {
        setRendered(Math.random())
    }, [showModal, showVerifyModal, showInvestigationModal, showConductInvestigationModal, showReviewModal, auditModal])

    const renderHeader = () => {
        const value = filters['global'] ? filters['global'].value : '';

        return (
            <div className='d-flex justify-content-between align-items-center'>
                {applicationType === 'Observation' ?

                    <h5 className="m-0"> A listing of all actions due from you for the selected location(s) and time frame.</h5>
                    : ''}

                <span className="p-input-icon-left">
                    <i className="fa fa-search" />
                    <InputText type="search" value={value || ''} onChange={(e) => onGlobalFilterChange(e)} />
                </span>
            </div>
        );
    };

    const header = renderHeader();
    const onGlobalFilterChange = (event) => {
        const value = event.target.value;
        let _filters = { ...filters };

        _filters['global'].value = value;

        setFilters(_filters);
    };

    const maskIdBodyTemplateForReport = (row) => {
        return <div className='maskid' onClick={() => handleAction(row)}>  # {row.id} </div>
    }
    const maskIdBodyTemplate = (row) => {
        if (row.applicationType === 'INCIDENT') {
            console.log('in')

            if (row.applicationDetails) {
                return (
                    <div className='maskid' onClick={() => handleAction(row)}> {isPastDue ? <span className='overdue'></span> : isToday ? <span className='pending'></span> : ''} {row.applicationDetails && row.applicationDetails.maskId ? `# ${row.applicationDetails.maskId} - ${row.sequenceNo}` : ''}</div>
                )
            } else {
                return (
                    <div className='maskid' onClick={() => handleAction(row)}>  # {row.maskId}- {row.sequenceNo} </div>
                )
            }


        }

        else if (row.applicationType === 'Inspection') {
            console.log('in')
            return <div className='maskid' onClick={() => handleInspectionAction(row)}>  # {row.maskId} - {row.sequenceNo} </div>
        } else if (row.applicationType === 'AuditFinding') {
            console.log('in')
            return (
                <div className='maskid' onClick={() => handleAction(row)}>  {row.applicationDetails && row.applicationDetails.maskId ? `# ${row.applicationDetails.maskId}` : ''}</div>
            )

        } else {

            if (row.applicationDetails) {
                console.log('in', row.actionType)
                if (row.actionType === "inspect") {
                    return (
                        <div>{row.applicationDetails.maskId}</div>
                    )
                } else {
                    return (
                        <div className='maskid' onClick={() => handleAction(row)}> {isPastDue ? <span className='overdue'></span> : isToday ? <span className='pending'></span> : ''} {row.applicationDetails && row.applicationDetails.maskId ? `# ${row.applicationDetails.maskId} - ${row.sequenceNo || ''}` : ''}</div>
                    )
                }
            } else {
                if (row.maskId.includes('GMS') && row.maskId.includes('NC')) {
                    return (
                        <div className='maskid' onClick={(e) => handleAuditFindings(row.auditId, row.id)}>  # {row.maskId} - {row.sequenceNo} </div>
                    )

                } else {
                    return (
                        <div className='maskid' onClick={() => handleAudit(row.id)}>  # {row.maskId - row.sequenceNo} </div>
                    )
                }

            }
        }
    }
    const actionTextBodyTemplate = (row) => {
        return (
            <div> {renderActionTxt(row.actionType)}</div>
        )

    }
    const dueBodyTemplate = (row) => {
        if (row.dueDate) {

            return (
                <div> {moment(row.dueDate, ["YYYY-MM-DD", "DD-MM-YYYY"]).format("Do MMM YYYY")}</div>
            )

        }
    }

    const incidentTemplate = (row) => {
        if (applicationType === 'INCIDENT') {
            return (
                moment(row.applicationDetails.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY')
            )
        } else if (applicationType === 'Observation') {
            return (
                moment(row.applicationDetails.created).format('Do MMM YYYY')
            )
        }
        else if (applicationType === 'AuditFinding' || applicationType === 'Report') {
            return (
                moment(row.createdDate, 'DD/MM/YYYY hh:mm').format('Do MMM YYYY')
            )
        } else {
            return (
                moment(row.createdDate).format('Do MMM YYYY')
            )
        }


    }
    const titleBodyTemplate = (row) => {
        if (applicationType === 'INCIDENT') {
            return row.applicationDetails.title
        } else if (applicationType === 'Observation') {
            return row.applicationDetails.description
        }
        // else if (applicationType === 'AuditFinding') {
        //     return row.applicationDetails.category
        // }
        else if (applicationType === 'Inspection') {
            return row.applicationDetails.checklist.name
        }


    }

    const requiredFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <div className="mb-3 font-bold">Type</div>
                <MultiSelect value={options.value} options={requiredAction} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    };
    const sortDate = (e) => {

        if (e.order === 1) {
            return e.data.sort((a, b) => {

                // Parse the dates using Moment.js
                const dateA = moment(a.createdDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);
                const dateB = moment(b.createdDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            });
        } else {

            return e.data.sort((a, b) => {
                // Parse the dates using Moment.js
                const dateA = moment(a.createdDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);
                const dateB = moment(b.createdDate, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A']);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            }).reverse()
        }
    }

    const actionBodyTemplate = (row) => {

        if (row.applicationDetails) {
            return row.state

        } else {
            if (row.status === 'Initiated' || row.status === 'Re-Scheduled' || row.status === 'Draft') {
                return 'Conduct Audit'
            } else {
                return 'Conduct Audit'
            }
        }



    }
    const projectBodyTemplateForReport = (row) => {
        const details = row.applicationDetails || {};  // Fallback to empty object if applicationDetails is undefined
        const locationOne = details.locationOne ? details.locationOne.name + ' > ' : '';
        const locationTwo = details.locationTwo ? details.locationTwo.name + ' > ' : '';
        const locationThree = details.locationThree ? details.locationThree.name + ' > ' : '';
        const locationFour = details.locationFour ? details.locationFour.name : '';

        const fullLocationChain = locationOne + locationTwo + locationThree + locationFour;

        return fullLocationChain || ''; // Return the full chain or an empty string if none
    }


    const projectBodyTemplate = (row) => {

        if (row.applicationDetails) {
            return row.applicationDetails.audit.locationFour.name
        } else {
            if (row.locationFour && row.locationFour.name) {
                return row.locationFour.name
            } else {
                if (row.maskId && row.maskId.includes('GMS') && row.maskId.includes('NC')) {
                    return row.audit.locationFour.name
                }
            }
        }
        // if (row.maskId.startsWith('GMS-NC')) {
        //     return row.audit.locationFour.name
        // } else {
        // return row.locationFour.name
        // }
    }

    const requiredActionBody = (row) => {

        if (row.actionType === 'inspect') {
            return 'Conduct Inspection (from Mobile App)'
        }
        return row.state

    }
    return (
        <>
            <DataTable value={data} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }} sortField="applicationDetails.maskId"
                sortOrder={-1}>

                <Column field="applicationDetails.maskId" body={maskIdBodyTemplate} header="ID" sortable  ></Column>
                <Column field="state" header="Required Action" body={requiredActionBody} filter filterElement={requiredFilterTemplate} showFilterMatchModes={false} ></Column>
                <Column field="createdDate" header="Assigned Date" sortable body={incidentTemplate} sortFunction={sortDate}></Column>
                <Column field="dueDate" header="Due Date" body={dueBodyTemplate} style={{ width: '15%' }} ></Column>

            </DataTable>

            {(showConductInvestigationModal) && <IncidentInvestigationModal incidentData={incidentData} showModal={showConductInvestigationModal} setShowModal={setShowConductInvestigationModal} />}
            {(showReviewModal) && <IncidentInformationModal type={'Review'} id={incidentData.id} showModal={showReviewModal} setShowModal={setShowReviewModal} />}
            {showModal && <TakeActionModal data={actionOne} applicationType={applicationType} showModal={showModal} setShowModal={setShowModal} />}
            {applicationType === 'Observation' ?
                showVerifyModal && <ObsVerifyModal data={actionOne} applicationType={applicationType} showModal={showVerifyModal} setShowModal={setShowVerifyModal} />
                :
                showVerifyModal && <VerifyActionModal data={actionOne} applicationType={applicationType} showModal={showVerifyModal} setShowModal={setShowVerifyModal} />
            }
            {(incidentData && showInvestigationModal) && <IncidentInvestigationViewModal data={actionOne} verify={true} incidentData={incidentData} showModal={showInvestigationModal} setShowModal={setShowInvestigationModal} />}
            {/* {showInspectionModal && <InspectionModal data={actionOne} applicationType={applicationType} showModal={showInspectionModal} setShowModal={setShowInspectionModal} />} */}
            {reportModal && <ReportDataModal data={actionOne} applicationType={applicationType} showModal={reportModal} setShowModal={setReportModal} />}


            {selectedAudit && <Modal
                show={auditModal}
                size="lg"
                onHide={() => setActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <AuditPanel auditModal={auditModal} setAuditModal={setAuditModal} auditId={selectedAudit} />

                </Modal.Body>

            </Modal>}

            {selectedAudit && <Modal
                show={auditFindingModal}
                size="lg"
                onHide={() => setAuditFindingModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <ActionAuditFinding auditId={selectedAudit} id={findingId} />
                    {/* <AuditFindingPanel auditId={selectedAudit} /> */}

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditFindingModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>}
        </>
    );
}

export default Actions;