import React, { useEffect, useState } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import moment from "moment";

const monthMap = {
    jan: 0, feb: 1, mar: 2, apr: 3, may: 4, jun: 5,
    jul: 6, aug: 7, sep: 8, oct: 9, nov: 10, dec: 11,
};

// Function to prepare and filter data for Highcharts
const prepareEmployeeWorkHoursData = (data, dateRange) => {
    if (!dateRange || dateRange.length !== 2) return [];

    const monthlyData = {};

    // Ensure date range starts from the first day of the selected months
    const [startDate, endDate] = dateRange.map((date) => {
        const parsedDate = new Date(date);
        return new Date(parsedDate.getFullYear(), parsedDate.getMonth(), 1);
    });

    // 🔹 Step 1: Generate full list of months in range
    const monthsInRange = [];
    const current = new Date(startDate);
    while (current <= endDate) {
        const key = `${current.getFullYear()}-${current.getMonth()}`;
        monthsInRange.push({
            key,
            year: current.getFullYear(),
            monthIndex: current.getMonth(),
            dateKey: new Date(current),
        });
        current.setMonth(current.getMonth() + 1);
    }

    // 🔹 Step 2: Populate data from actual entries
    data.forEach((entry) => {
        const { year, month, averageNoOfSTTGdcEmployeesPerDay, averageNoOfContractorEmployeesPerDay, totalNoOfEmployees, monthlyHoursWorked } = entry;
        const monthIndex = monthMap[month?.toLowerCase()];
        if (monthIndex === undefined || year === undefined) return;

        const key = `${year}-${monthIndex}`;

        if (!monthlyData[key]) {
            monthlyData[key] = {
                year,
                monthIndex,
                dateKey: new Date(year, monthIndex, 1),
                sttGdcEmployees: 0,
                contractorEmployees: 0,
                totalNoOfEmployees: 0,
                totalHoursWorked: 0,
            };
        }

        monthlyData[key].sttGdcEmployees += averageNoOfSTTGdcEmployeesPerDay || 0;
        monthlyData[key].contractorEmployees += averageNoOfContractorEmployeesPerDay || 0;
        monthlyData[key].totalNoOfEmployees += totalNoOfEmployees || 0;
        monthlyData[key].totalHoursWorked += monthlyHoursWorked || 0;
    });

    // 🔹 Step 3: Fill missing months with zero data
    const result = monthsInRange.map(({ key, year, monthIndex, dateKey }) => {
        return monthlyData[key] || {
            year,
            monthIndex,
            dateKey,
            sttGdcEmployees: 0,
            contractorEmployees: 0,
            totalNoOfEmployees: 0,
            totalHoursWorked: 0,
        };
    });

    // Sort just in case
    return result.sort((a, b) => a.dateKey - b.dateKey);
};


const WorkHoursChart = ({ data, dateRange }) => {
    const [preparedData, setPreparedData] = useState([]);

    useEffect(() => {
        if (!data || data.length === 0) {
            console.warn("No data provided to the chart component.");
            return;
        }

        const filteredData = prepareEmployeeWorkHoursData(data, dateRange);

        setPreparedData(filteredData);
    }, [data, dateRange]);

    // Extract categories and series data for Highcharts
    const categories = preparedData.map((item) =>
        moment(item.dateKey).format("MMM YYYY")
    );

    const seriesData = [
        {
            name: "STT GDC Employees",
            type: "column",
            data: preparedData.map((item) => item.sttGdcEmployees),
            stack: "employees",
        },
        {
            name: "Contractor Employees",
            type: "column",
            data: preparedData.map((item) => item.contractorEmployees),
            stack: "employees",
        },
        {
            name: "Monthly Hours Worked",
            type: "spline",
            yAxis: 1,
            data: preparedData.map((item) => item.totalHoursWorked),
        },
    ];

    // Highcharts Configuration
    const options = {
        chart: {
            zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
        },
        title: {
            text: "",
        },
        xAxis: {
            categories: categories,
            title: {
                text: "Month-Year",
            },
            crosshair: true,
        },
        yAxis: [
            {
                title: {
                    text: "Average Employees",
                },
            },
            {
                title: {
                    text: "Monthly Hours Worked",
                },
                opposite: true,
            },
        ],
        tooltip: {
            shared: true,
        },
        plotOptions: {
            column: {
                stacking: "normal",
                dataLabels: {
                    enabled: true,
                },
            },
            spline: {
                marker: {
                    enabled: true,
                },
                dataLabels: {
                    enabled: true,
                },
            },
        },
        legend: {
            enabled: true,
        },
        series: seriesData,
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    menuItems: [
                        "downloadPNG",
                        "downloadJPEG",
                        "downloadPDF",
                        "downloadSVG",
                        "separator",
                        "downloadCSV",
                        "downloadXLS",
                    ],
                },
            },
        },
    };

    return <>{options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}</>;
};

export default WorkHoursChart;
