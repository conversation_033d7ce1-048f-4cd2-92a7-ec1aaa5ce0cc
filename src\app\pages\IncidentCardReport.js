import React, { useEffect, useState } from 'react'

import $ from "jquery";

import API from '../services/API';

import {  INCIDENT_REVIWERER_URL, REPORT_INCIDENT_REVIEW_URL } from '../constants';

import axios from 'axios';

import IncidentInformationModal from './IncidentInformationModal';
import moment from 'moment';

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const IncidentCardReport = () => {


  const [data, setData] = useState([]);

  useEffect(() => {
    getIncidentData();
  }, [])

  const getIncidentData = async () => {
    const response = await API.get(REPORT_INCIDENT_REVIEW_URL);
    if (response.status === 200) {
      setData(response.data)
    }
  }


  const [showModal, setShowModal] = useState(false)



  const [currentIncident, setCurrentIncident] = useState('')
  const viewIncident = async (id) => {
    setCurrentIncident(id);

    setShowModal(true)
  }


  return (
    <>

      <div>
        <div>
          {
            data.map(i => {
              return (
                <div className='zoom-on-hover card-body shadow-light border-radius-5 mb-3 cursor-pointer' key={i.id}
                  onClick={() => viewIncident(i.id)}
                >
                  <div className='d-flex justify-content-between'>
                    <p># {i.maskId}</p>

                    <p><strong>Review Incident</strong></p>
                  </div>
                  <p>Incident Date: {moment(i.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY hh:mm A')}</p>

                  <p>Description: {i.description}</p>
                  <p>Category: {i.IncidentCategory}</p>

                </div>
              )
            })
          }
        </div>
      </div>


      {(currentIncident && showModal) && <IncidentInformationModal type={'Review'} id={currentIncident} showModal={showModal} setShowModal={setShowModal} />}
    </>
  )
}

export default IncidentCardReport
