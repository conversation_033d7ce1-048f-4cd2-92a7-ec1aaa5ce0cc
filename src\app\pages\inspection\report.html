<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Inspection Report</title>
    <style>
      body {
        font-family: "Segoe UI", sans-serif;
        font-size: 14px;
        background-color: #fff;
        margin: 40px;
        color: #333;
      }

      h1 {
        font-size: 22px;
        margin-bottom: 20px;
      }

      .section-title {
        font-size: 18px;
        margin: 30px 0 10px;
        font-weight: bold;
      }

      .grid {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
      }

      .grid-column {
        flex: 1 1 45%;
      }

      .label {
        font-weight: bold;
        margin-top: 10px;
      }

      .status-tag {
        font-size: 12px;
        padding: 2px 8px;
        background-color: #fff3cd;
        color: #856404;
        border-radius: 5px;
        font-weight: bold;
      }

      .tabs {
        display: flex;
        margin: 20px 0 10px;
      }

      .tab {
        padding: 6px 15px;
        font-size: 13px;
        border-radius: 4px;
        margin-right: 6px;
        color: white;
      }

      .tab.green {
        background-color: #2ecc71;
      }
      .tab.blue {
        background-color: #27ae60;
      }
      .tab.orange {
        background-color: #f39c12;
      }
      .tab.red {
        background-color: #e74c3c;
      }
      .tab.gray {
        background-color: #95a5a6;
      }

      /* Areas Layout Styles */
      .areas-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin: 20px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .areas-section {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .areas-header {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #dee2e6;
        display: flex;
        align-items: center;
      }

      .areas-header.included {
        color: #28a745;
        border-bottom-color: #28a745;
      }

      .areas-header.not-included {
        color: #dc3545;
        border-bottom-color: #dc3545;
      }

      .areas-header::before {
        font-size: 18px;
        margin-right: 8px;
      }

      .areas-header.included::before {
        content: "✓";
      }

      .areas-header.not-included::before {
        content: "✕";
      }

      .areas-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .areas-list li {
        padding: 8px 12px;
        margin-bottom: 6px;
        border-radius: 5px;
        font-size: 14px;
        display: flex;
        align-items: center;
      }

      .areas-list.included li {
        background-color: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
      }

      .areas-list.not-included li {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
      }

      .areas-list li::before {
        margin-right: 8px;
        font-weight: bold;
      }

      .areas-list.included li::before {
        content: "●";
        color: #28a745;
      }

      .areas-list.not-included li::before {
        content: "●";
        color: #dc3545;
      }

      .checklist-item {
        border: 1px solid #e7e9ec;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 12px;
        background-color: #fff;
      }

      .response {
        float: right;
      }

      .btn {
        padding: 4px 10px;
        border-radius: 5px;
        font-size: 13px;
        border: 1px solid #ccc;
        margin-left: 5px;
        cursor: default;
      }

      .btn.yes {
        background-color: #e6f4ea;
        color: #28a745;
        border-color: #c3e6cb;
      }

      .btn.no {
        background-color: #fcebea;
        color: #dc3545;
        border-color: #f5c6cb;
      }

      .btn.na {
        background-color: #e2e3e5;
        color: #6c757d;
        border-color: #d6d8db;
      }

      .remarks {
        font-size: 13px;
        font-style: italic;
        margin-top: 8px;
        color: #666;
      }

      .action-card {
        border: 1px solid #e0e0e0;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .action-info {
        flex-grow: 1;
      }

      .action-id {
        font-size: 12px;
        color: #666;
        margin-bottom: 3px;
      }

      .action-title {
        font-weight: bold;
      }

      .action-meta {
        font-size: 12px;
        color: #444;
        margin-top: 5px;
      }

      .action-status {
        font-size: 12px;
        font-weight: 600;
        padding: 4px 10px;
        border-radius: 12px;
        display: inline-block;
        white-space: nowrap;
      }

      .completed {
        background-color: #d4edda;
        color: #155724;
      }

      .inprogress {
        background-color: #d1ecf1;
        color: #0c5460;
      }

      .pending {
        background-color: #fff3cd;
        color: #856404;
      }

      body {
        font-family: "Segoe UI", sans-serif;
        margin: 40px;
        color: #2f2f2f;
        font-size: 14px;
      }

      /* Header Styles */
      .header-section {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 20px;
        margin-bottom: 20px;
      }

      .header-title {
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
      }

      .header-title::before {
        content: "🗎";
        font-size: 20px;
        margin-right: 8px;
      }

      .header-block {
        margin-bottom: 15px;
      }

      .header-block.full-width {
        grid-column: 1 / -1;
      }

      .header-label {
        font-size: 12px;
        color: #888;
        margin-bottom: 4px;
      }

      .header-value {
        font-weight: 600;
        font-size: 14px;
      }

      /* Checklist UI */
      .checklist-item {
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #fff;
      }

      .checklist-text {
        font-weight: 500;
        margin-bottom: 8px;
      }

      .remark-label {
        font-size: 12px;
        color: #666;
        margin-top: 10px;
        display: flex;
        align-items: center;
      }

      .remark-label::before {
        content: "ⓘ";
        font-size: 14px;
        margin-right: 5px;
      }

      .remark-box {
        margin-top: 5px;
        padding: 10px;
        background-color: #fcfcfc;
        border: 1px solid #eee;
        border-radius: 5px;
        color: #555;
        font-style: italic;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f7fa;
        margin: 0;
        padding: 2rem;
        color: #333;
      }

      .container {
        max-width: 900px;
        margin: auto;
        background: #fff;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        overflow: hidden;
      }

      .header {
        background-color: #199653;
        padding: 1rem 2rem;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
      }

      .section {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #eee;
      }

      .section:last-child {
        border-bottom: none;
      }

      .section-title {
        font-weight: 600;
        font-size: 1rem;
        color: #555;
        margin-bottom: 0.5rem;
      }

      .section-content {
        font-size: 0.95rem;
        line-height: 1.6;
      }

      .label {
        font-weight: 500;
        color: #666;
      }

      .action-card {
        background-color: #f0fef6;
        border-left: 5px solid #199653;
        padding: 1rem;
        margin-top: 1rem;
        border-radius: 6px;
      }

      .verify-card {
        background-color: #eef4ff;
        border-left: 5px solid #2874d9;
        padding: 1rem;
        margin-top: 1rem;
        border-radius: 6px;
      }

      .footer {
        font-size: 0.85rem;
        text-align: right;
        padding: 1rem 2rem;
        color: #888;
      }

      /* Detail Card Styles - Aligned with existing layout */
      .detail-card {
        background: #fff;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        overflow: hidden;
        margin-top: 20px;
      }

      .detail-header {
        background-color: #199653;
        padding: 1rem 1.5rem;
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
      }

      .detail-section {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
      }

      .detail-section:last-child {
        border-bottom: none;
      }

      .detail-section-title {
        font-weight: 600;
        font-size: 1rem;
        color: #555;
        margin-bottom: 0.5rem;
      }

      .detail-section-content {
        font-size: 0.95rem;
        line-height: 1.6;
      }

      .detail-footer {
        font-size: 0.85rem;
        text-align: right;
        padding: 1rem 1.5rem;
        color: #888;
      }

      /* Status Badge Styles */
      .header-container {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .status-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #28a745;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 8px rgba(254, 255, 254, 0.3);
      }

      .status-badge::before {
        content: "✓";
        margin-right: 6px;
        font-size: 16px;
      }

      .status-badge.completed {
        background-color: #28a745;
      }

      .status-badge.in-progress {
        background-color: #17a2b8;
      }

      .status-badge.pending {
        background-color: #ffc107;
        color: #212529;
      }

      .status-badge.overdue {
        background-color: #dc3545;
      }

      /* Timestamp Styles */
      .timestamp {
        position: absolute;
        top: 35px;
        right: 0;
        font-size: 11px;
        color: #666;
        font-weight: normal;
        display: flex;
        align-items: center;
      }

      .timestamp::before {
        content: "🕒";
        margin-right: 4px;
        font-size: 12px;
      }

      /* A4 Page Formatting */
      @page {
        size: A4;
        margin: 20mm;
      }

      body {
        width: 210mm;
        min-height: 297mm;
        margin: 0 auto;
        padding: 20mm;
        box-sizing: border-box;
        background: white;
        font-size: 12px;
        line-height: 1.4;
      }

      /* Adjust container for A4 */
      .page-container {
        max-width: 170mm;
        margin: 0 auto;
        background: white;
      }

      /* Scale down fonts for A4 */
      .header-title {
        font-size: 20px;
      }

      .section-title {
        font-size: 16px;
        margin: 20px 0 8px;
      }

      .header-label {
        font-size: 10px;
      }

      .header-value {
        font-size: 12px;
      }

      .checklist-item {
        font-size: 11px;
        padding: 10px;
        margin-bottom: 8px;
      }

      .btn {
        font-size: 10px;
        padding: 3px 8px;
      }

      .action-card {
        padding: 10px;
        margin-bottom: 8px;
      }

      .action-id {
        font-size: 10px;
      }

      .action-title {
        font-size: 12px;
      }

      .action-meta {
        font-size: 10px;
      }

      .detail-card {
        margin-top: 15px;
      }

      .detail-header {
        font-size: 14px;
        padding: 12px;
      }

      .detail-section {
        padding: 12px;
      }

      .detail-section-title {
        font-size: 12px;
      }

      .detail-section-content {
        font-size: 11px;
      }

      .areas-container {
        gap: 20px;
        margin: 15px 0;
        padding: 15px;
      }

      .areas-section {
        padding: 15px;
      }

      .areas-header {
        font-size: 14px;
        margin-bottom: 10px;
      }

      .areas-list li {
        font-size: 11px;
        padding: 6px 10px;
        margin-bottom: 4px;
      }

      .status-badge {
        font-size: 12px;
        padding: 6px 12px;
      }

      /* Print styles */
      @media print {
        body {
          margin: 0;
          padding: 15mm;
          font-size: 11px;
        }

        .page-container {
          max-width: none;
        }

        .status-badge {
          box-shadow: none;
        }

        .timestamp {
          font-size: 10px;
        }

        .detail-card,
        .action-card,
        .areas-section {
          box-shadow: none;
          border: 1px solid #ddd;
        }

        .header-container {
          margin-bottom: 15px;
        }

        .section-title {
          margin: 15px 0 6px;
        }

        /* Ensure content fits on page */
        .checklist-item {
          break-inside: avoid;
        }

        .action-card {
          break-inside: avoid;
        }

        .detail-section {
          break-inside: avoid;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="header-container">
        <div class="header-title">Inspection Report</div>
        <div class="status-badge completed">Completed</div>
        <div class="timestamp">15th May 2023, 10:15 AM</div>
      </div>
      <div class="header-section">
        <!-- Row 1 -->
        <div class="header-block">
          <div class="header-label">Checklist Name</div>
          <div class="header-value">DC Operation Safety Checklist</div>
        </div>
        <div class="header-block">
          <div class="header-label">Type</div>
          <div class="header-value">DC Operation</div>
        </div>
        <div class="header-block">
          <div class="header-label">Reference #</div>
          <div class="header-value">INS-250514-0081</div>
        </div>

        <div class="header-block">
          <div class="header-label">Location</div>
          <div class="header-value">
            India-IPN - Noida - Construction Projects - Project Noida DC 1
          </div>
        </div>

        <div class="header-block"></div>

        <div class="header-block">
          <div class="header-label">Date of Inspection</div>
          <div class="header-value">17th May 2023</div>
        </div>

        <div class="header-block">
          <div class="header-label">Assigned Date</div>
          <div class="header-value">15th May 2023</div>
        </div>

        <div class="header-block">
          <div class="header-label">Start Date</div>
          <div class="header-value">19th May 2023</div>
        </div>
        <div class="header-block">
          <div class="header-label">Due Date</div>
          <div class="header-value">24th May 2023</div>
        </div>

        <div class="header-block">
          <div class="header-label">Assigned to</div>
          <div class="header-value">John Smith</div>
        </div>
        <div class="header-block">
          <div class="header-label">Assignee</div>
          <div class="header-value">Adhi Internal</div>
        </div>
      </div>

      <div class="areas-container">
        <div class="areas-section">
          <div class="areas-header included">Areas Included</div>
          <ul class="areas-list included">
            <li>Common Area</li>
          </ul>
        </div>

        <div class="areas-section">
          <div class="areas-header not-included">Areas Not Included</div>
          <ul class="areas-list not-included">
            <li>Car Park & Loading</li>
            <li>Electrical Systems</li>
            <li>Fire Safety</li>
            <li>HVAC Systems</li>
            <li>Security Systems</li>
          </ul>
        </div>
      </div>

      <div class="section-title">Inspection Checklist</div>

      <div class="tabs">
        <div class="tab green">1.Common Area</div>
      </div>

      <!-- Checklist Items -->
      <div class="checklist-item">
        All safety and directional/directory signage are in place and accurate.
        <div class="response">
          <span class="btn yes">✓ Yes</span>
        </div>
      </div>

      <div class="checklist-item">
        Riser shaft compartments are closed and unobstructed.
        <div class="response">
          <span class="btn no">✕ No</span>
        </div>
        <div class="remark-label">Remarks</div>
        <div class="remark-box">Need to check with maintenance team.</div>
      </div>

      <div class="checklist-item">
        Fire extinguishers, hose reels and manual call points are functional.
        <div class="response">
          <span class="btn yes">✓ Yes</span>
        </div>
      </div>

      <div class="tabs">
        <div class="tab green">2. Safety Checklist</div>
      </div>

      <!-- Checklist Items -->
      <div class="checklist-item">
        All safety and directional/directory signage are in place and accurate.
        <div class="response">
          <span class="btn yes">✓ Yes</span>
        </div>
      </div>

      <div class="checklist-item">
        Riser shaft compartments are closed and unobstructed.
        <div class="response">
          <span class="btn no">✕ No</span>
        </div>
        <div class="remark-label">Remarks</div>
        <div class="remark-box">Need to check with maintenance team.</div>
      </div>

      <div class="checklist-item">
        Fire extinguishers, hose reels and manual call points are functional.
        <div class="response">
          <span class="btn yes">✓ Yes</span>
        </div>
      </div>

      <!-- Actions -->
      <div class="section-title">INS-250514-0081 Actions</div>

      <div class="action-card">
        <div class="action-info">
          <div class="action-id">INS-250514-0081 : IA-1.1</div>
          <div class="action-title">Check main entrance lighting</div>
          <div class="action-meta">
            Due: <strong>1st Jan 2023</strong> &nbsp; Assigned to:
            <strong>Adhi Internal</strong>
          </div>
        </div>
        <div class="action-status completed">✓ Completed</div>
      </div>

      <!-- Detailed Action Information -->
      <div class="section-title">Action Details</div>

      <div class="detail-card">
        <div class="detail-header">
          INS-250514-0081 : IA-1.1 - Check main entrance lighting
        </div>

        <div class="detail-section">
          <div class="detail-section-title">Description</div>
          <div class="detail-section-content">Check main entrance lighting</div>
        </div>

        <div class="detail-section">
          <div class="detail-section-title">Assigned Action</div>
          <div class="detail-section-content">
            <div
              style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 15px;
              "
            >
              <div><span class="label">Section:</span> Q1 Main Entrance</div>
              <div><span class="label">Assigned To:</span> Adhi Internal</div>
            </div>
            <div
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px"
            >
              <div>
                <span class="label">Assigned Date:</span> 14th May 2023, 08:31
                AM
              </div>
              <div><span class="label">Due Date:</span> 1st Jan 2023</div>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <div class="detail-section-title">Action Taken</div>
          <div class="action-card">
            <div class="detail-section-content">
              Checked all main entrance lighting fixtures. Replaced 2 faulty LED
              bulbs and cleaned light covers.
              <br /><br />
              <strong>Evidence:</strong> Photos taken of all lighting fixtures
              before and after maintenance. All lights now functioning properly.
            </div>
          </div>
        </div>

        <div class="detail-section">
          <div class="detail-section-title">Action Verified</div>
          <div class="verify-card">
            <div class="detail-section-content">
              <div
                style="
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 20px;
                  margin-bottom: 15px;
                "
              >
                <div><span class="label">Verified By:</span> John Smith</div>
                <div>
                  <span class="label">Verified Date:</span> 15th May 2023, 10:15
                  AM
                </div>
              </div>
              <div>
                <span class="label">Comments:</span> All lighting fixtures
                verified working. Good maintenance work completed.
              </div>
            </div>
          </div>
        </div>

        <div class="detail-footer">
          Action completed and verified successfully
        </div>
      </div>
    </div>
    <!-- Close page-container -->
  </body>
</html>
