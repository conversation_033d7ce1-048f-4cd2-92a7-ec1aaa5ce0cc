import React, { useEffect, useState, useMemo } from 'react'
import API from '../services/API';
import { Modal, Button, Form } from 'react-bootstrap';

import { INSPECTION_URL } from '../constants';

import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import Box from '@mui/material/Box';
import Select from 'react-select'
import FormRender from '../apps/FormRender';
import ActionTable from './inspection/ActionTable';
const InspectionTable = ({ inspection }) => {


    const [actionModal, setActionModal] = useState(false)
    const [current, setCurrent] = useState('')
    const [auditActionModal, setAuditActionModal] = useState(false)
    const [maskId, setMaskId] = useState('')
    const [totalAction, setTotalAction] = useState('')




    const [data, setData] = useState(null);
    const [filterData, setFilterData] = useState([]);


    useEffect(() => {
        if (inspection) {
            setFilterData(inspection)
        }
    }, [inspection])

    // const getInspectionData = async () => {
    //     const params = {
    //         "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

    //     };
    //     const response = await API.get(`${INSPECTION_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);


    //     if (response.status === 200) {
    //         setData(response.data)
    //         setFilterData(response.data)
    //     }
    // }


    const [showModal, setShowModal] = useState(false)





    const viewInspection = (option) => {


        setData(option)
        setShowModal(true)
    }
    const viewTemplate = (option) => {
        return (
            <i onClick={() => viewInspection(option)} style={{ fontSize: '18px' }} className='mdi mdi-eye'></i>
        )

    }

    const maskIdBodyTemplate = (row) => {
        // console.log('Row data:', row);

        // // Set the current row for further operations (assuming `setCurrent` is available in your scope)
        // setCurrent(row);

        // // Group actions by their description using the `groupByDescription` function
        // const totalActionData = groupByDescription(row.inspectionData.totalActions);
        // console.log('Grouped action data:', totalActionData);

        // // Filter the grouped actions to find those that are 'approve' and 'submitted'
        // const totalCompleted = totalActionData.filter(
        //     (item) => item.lastActionType === 'approve' && item.lastStatus === 'submitted'
        // );
        // console.log('Total completed actions:', totalCompleted);

        // // Example: Extracting maskId and action counts if needed
        // const id = row.maskId;
        // // Assuming you want to store the count of completed actions

        // // Update the state with the maskId and the number of completed actions
        // setMaskId(id);
        // setTotalAction(totalCompleted);

        // Render the clickable mask ID with a callback to view more details about the inspection
        return (
            <div className='maskid' onClick={() => viewInspection(row)}>
                {row.maskId}
            </div>
        );
    };


    const locationBodyTemplate = (rowData) => {


        return `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''}`



    }
    function groupByDescription(data) {
        console.log(data);
        console.log('in');

        const filterData = data.filter(item => item.actionType !== 'inspect')

        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status } = item;
            if (!descriptionMap[description]) {
                descriptionMap[description] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[description].lastActionType = actionType;
                descriptionMap[description].actionTypes.push(actionType);
                descriptionMap[description].lastAssignedToId = assignedToId;
                descriptionMap[description].lastStatus = status;

            }
            descriptionMap[description].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }


    const actionBodyTemplate = (rowData) => {

        setCurrent(rowData)

        const totalActionData = groupByDescription(rowData.inspectionData.totalActions)

        console.log(totalActionData)

        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';




        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleAuditActionCard(rowData.maskId, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }

    const handleAuditActionCard = (id, actions) => {

        console.log(actions)
        setAuditActionModal(true)

        setMaskId(id)
        setTotalAction(actions)
    }
    return (
        <>

            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="">

                                <h4 className="card-title"></h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>



                                            <DataTable value={filterData} paginator rows={10} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                rowsPerPageOptions={[10, 25, 50]}
                                                emptyMessage="No Data found.">
                                                <Column field='maskId' header='ID' body={maskIdBodyTemplate} filter showFilterMatchModes={false}></Column>
                                                <Column field='checklist.name' header='Type of Inspection' filter showFilterMatchModes={false}></Column>
                                                <Column field="location" header="Location" body={locationBodyTemplate} />
                                                <Column field='dateTime' header="Date" sortable ></Column>
                                                <Column field='assignedTo.firstName' header="Assignee" sortable ></Column>\
                                                {/* <Column field='status' header="Status" sortable></Column> */}
                                                <Column field='' header="Action Status" body={actionBodyTemplate}></Column>
                                                {/* <Column field='' header="View Reports" body={viewTemplate} sortable ></Column> */}

                                            </DataTable>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <Box>
                        {data && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                    <h5 className="card-title"># ({data.maskId})</h5>
                                    {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Checklist</p>
                                            <p className="obs-content">
                                                {data.checklist?.name}
                                            </p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title">Description</p>
                                            <p className="obs-content">
                                                {data.checklist?.description}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Location</p>
                                            <p className="obs-content">
                                                {data.locationOne?.name} > {data.locationTwo?.name} > {data.locationThree?.name} > {data.locationFour?.name}
                                            </p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title"> Date</p>
                                            <p className="obs-content">
                                                {data.created || ''}
                                            </p>
                                        </div>
                                    </div>

                                    {/* <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Location</p>
                                            <p className="obs-content">
                                                {data.locationOne?.name} > {data.locationTwo?.name} > {data.locationThree?.name} > {data.locationFour?.name}
                                            </p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title"> Date</p>
                                            <p className="obs-content">
                                                {data.created || ''}
                                            </p>
                                        </div>
                                    </div> */}

                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Scheduler</p>
                                            <p className="obs-content">
                                                {data.assignedBy?.firstName}
                                            </p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title"> Inspector</p>
                                            <p className="obs-content">
                                                {data.assignedTo?.firstName || ''}
                                            </p>
                                        </div>
                                    </div>



                                    {/* <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <p>{data.locationOne?.name} > {data.locationTwo?.name} > {data.locationThree?.name} > {data.locationFour?.name}</p>
                                    </div> */}

                                    {/* <div className="mb-3">
                                        <label className="form-label">Month / Year</label>
                                        <p>{data.month} / {data.year}</p>
                                    </div> */}

                                    <p className="obs-title">Checklists</p>
                                    <FormRender formData={data.checklistReport || []} />



                                    <>

                                        <p lassName="obs-title">Assigned Actions</p>

                                        {/* {data.postActions && data.postActions?.length > 0 && data.postActions?.map((action, index) => (
                                            <div className="form-group" key={index}>
                                                <label>
                                                    Corrective/Control measures:
                                                    <input
                                                        className="form-control"
                                                        type="text"
                                                        value={action.actionToBeTaken}
                                                        disabled
                                                    />
                                                </label>

                                                <label>
                                                    Due Date
                                                    <input
                                                        className="form-control"
                                                        type="date"
                                                        value={action.dueDate}
                                                        disabled
                                                    />
                                                </label>



                                            </div>
                                        ))} */}

                                        {/* <ActionTable id={maskId} actions={totalAction} current={current} /> */}

                                    </>




                                </div>
                            </div>
                        </div>}
                    </Box>

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setShowModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>

            <Modal
                show={auditActionModal}
                size="lg"
                onHide={() => setAuditActionModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>

                    <ActionTable id={maskId} actions={totalAction} current={current} />
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditActionModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default InspectionTable;
