/* Role Assignment Modal Styles */

.role-assignment-modal {
  .modal-dialog {
    max-width: 95% !important;
    margin: 2rem auto;
  }

  .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }
}

.role-assignment-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem 2rem;

  .modal-title {
    color: #495057;
    font-weight: 600;
    font-size: 1.5rem;
    margin: 0;
  }

  .user-info {
    margin-top: 0.25rem;
    
    .user-name {
      font-weight: 500;
      color: #6c757d;
      font-size: 1rem;
    }

    .user-email {
      font-size: 0.875rem;
      margin-left: 0.5rem;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .btn {
      border-radius: 6px;
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.assign-permissions-modal {
  .modal-content-wrapper {
    height: 70vh;
    min-height: 600px;
  }

  .permissions-form-section {
    background: #ffffff;
    border-right: 1px solid #e9ecef;
    padding: 0;
    overflow-y: auto;
    max-height: 70vh;

    .permissions-form-container {
      padding: 2rem;
    }
  }

  .preview-section {
    background: #f8f9fa;
    padding: 0;
    overflow-y: auto;
    max-height: 70vh;

    .preview-container {
      padding: 2rem;
    }
  }

  .section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;

    .section-title {
      color: #495057;
      font-weight: 600;
      font-size: 1.25rem;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;

      i {
        font-size: 1.5rem;
      }
    }

    .section-subtitle {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .location-filter-section {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .roles-section {
    .roles-header {
      margin-bottom: 1.5rem;

      .roles-title {
        color: #495057;
        font-weight: 600;
        font-size: 1.1rem;
        margin: 0 0 0.25rem 0;
        display: flex;
        align-items: center;

        i {
          font-size: 1.25rem;
        }
      }
    }

    .module-group {
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .module-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e9ecef;

        .module-title {
          color: #495057;
          font-weight: 600;
          font-size: 1rem;
          margin: 0;
          display: flex;
          align-items: center;

          i {
            font-size: 1.1rem;
            color: #6c757d;
          }
        }
      }

      .role-checkboxes-grid {
        padding: 1.5rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 0.75rem;
      }
    }
  }

  .role-checkbox-item {
    position: relative;

    &.disabled {
      opacity: 0.6;
    }

    .role-checkbox-label {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      margin: 0;
      position: relative;

      &:hover {
        background: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
      }

      .role-checkbox {
        position: absolute;
        opacity: 0;
        cursor: pointer;

        &:checked + .checkmark {
          background-color: #007bff;
          border-color: #007bff;

          &:after {
            display: block;
          }
        }

        &:disabled + .checkmark {
          background-color: #e9ecef;
          border-color: #e9ecef;
          cursor: not-allowed;
        }
      }

      .checkmark {
        height: 18px;
        width: 18px;
        background-color: #ffffff;
        border: 2px solid #dee2e6;
        border-radius: 3px;
        margin-right: 0.75rem;
        position: relative;
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:after {
          content: "";
          position: absolute;
          display: none;
          left: 5px;
          top: 2px;
          width: 4px;
          height: 8px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      }

      .role-name {
        font-weight: 500;
        color: #495057;
        font-size: 0.875rem;
        flex: 1;
      }

      .role-status {
        font-size: 0.75rem;
        color: #6c757d;
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        margin-left: 0.5rem;
      }
    }
  }

  .modal-fixed-footer {
    background: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 1.5rem 2rem;
    margin-top: auto;

    .footer-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .btn {
        border-radius: 6px;
        font-weight: 500;
        padding: 0.5rem 1.5rem;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        &.btn-primary {
          background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
          }
        }
      }
    }
  }

  .preview-content {
    .current-selection {
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .preview-subtitle {
      color: #6c757d;
      font-weight: 600;
      font-size: 0.875rem;
      margin-bottom: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .preview-item {
      .location-path {
        background: #e3f2fd;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        margin-bottom: 1rem;
        border-left: 4px solid #2196f3;

        strong {
          color: #1976d2;
          font-weight: 600;
        }

        i {
          color: #2196f3;
        }
      }

      .module-preview {
        margin-bottom: 1rem;

        .module-preview-title {
          color: #495057;
          font-weight: 600;
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
          display: flex;
          align-items: center;

          i {
            color: #17a2b8;
          }
        }

        .role-list {
          list-style: none;
          padding: 0;
          margin: 0;

          .role-item {
            padding: 0.25rem 0;
            font-size: 0.875rem;
            color: #6c757d;
            display: flex;
            align-items: center;

            i {
              color: #28a745;
              font-size: 0.75rem;
            }
          }
        }
      }
    }

    .empty-state {
      padding: 3rem 1rem;
      text-align: center;

      i {
        color: #dee2e6;
        margin-bottom: 1rem;
      }

      p {
        color: #6c757d;
        font-size: 0.875rem;
      }
    }
  }
}

/* View Roles Modal Styles */
.view-roles-modal {
  .modal-dialog {
    max-width: 800px !important;
    width: 90%;
    margin: 2rem auto;
  }

  .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }
}

.view-roles-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem 2rem;

  .modal-title {
    color: #495057;
    font-weight: 600;
    font-size: 1.5rem;
    margin: 0;
  }

  .user-info {
    margin-top: 0.25rem;

    .user-name {
      font-weight: 500;
      color: #6c757d;
      font-size: 1rem;
    }

    .user-email {
      font-size: 0.875rem;
      margin-left: 0.5rem;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .btn {
      border-radius: 6px;
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.view-roles-modal-body {
  .modal-content-wrapper {
    height: 70vh;
    min-height: 600px;
  }

  // Single column layout
  .single-column-layout {
    background: #ffffff;
    height: 100%;
    overflow-y: auto;
    max-height: 70vh;
    width: 100%;

    .user-info-container {
      padding: 2rem;
      width: 100%;
      max-width: none;
      margin: 0;
    }
  }

  // Legacy two-column layout support
  .user-info-section {
    background: #ffffff;
    border-right: 1px solid #e9ecef;
    padding: 0;
    overflow-y: auto;
    max-height: 70vh;

    .user-info-container {
      padding: 2rem;
    }
  }

  .location-roles-section-wrapper {
    background: #f8f9fa;
    padding: 0;
    overflow-y: auto;
    max-height: 70vh;

    .location-roles-container {
      padding: 2rem;
    }
  }

  .section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;

    .section-title {
      color: #495057;
      font-weight: 600;
      font-size: 1.25rem;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;

      i {
        font-size: 1.5rem;
      }
    }

    .section-subtitle {
      margin: 0;
      font-size: 0.875rem;
    }
  }

  .user-details-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    width: 100%;
    margin-bottom: 1.5rem;

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f8f9fa;

      &:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .detail-value {
        font-weight: 500;
        color: #495057;
        text-align: right;
        flex: 1;
        margin-left: 1rem;
      }
    }
  }

  .application-roles-container {
    width: 100%;

    .module-group {
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .module-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e9ecef;

        .module-title {
          color: #495057;
          font-weight: 600;
          font-size: 1rem;
          margin: 0;
          display: flex;
          align-items: center;

          i {
            font-size: 1.1rem;
            color: #6c757d;
          }
        }
      }

      .role-badges-grid {
        padding: 1.5rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 0.75rem;
      }

      .role-badge-item {
        .role-badge {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          font-weight: 500;
          color: #495057;
          font-size: 0.875rem;
          width: 100%;
          transition: all 0.2s ease;

          &:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
          }

          i {
            font-size: 0.75rem;
          }
        }
      }
    }
  }

  .location-roles-section {
    .location-assignment-card {
      background: #ffffff;
      border: 2px solid #dc3545;
      border-radius: 12px;
      margin-bottom: 20px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);

      .location-header {
        background: #ffffff;
        padding: 15px 20px;
        border-bottom: 1px solid #f8f9fa;

        .location-path {
          display: flex;
          align-items: center;
          font-size: 1rem;
          font-weight: 600;
          color: #495057;

          .mdi-map-marker {
            font-size: 1.2rem;
          }
        }
      }

      .location-modules {
        padding: 0;

        .location-module {
          border-bottom: 1px solid #f8f9fa;

          &:last-child {
            border-bottom: none;
          }

          .module-header-location {
            background: #fff3cd;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #856404;
            border-bottom: 1px solid #ffeaa7;

            .mdi {
              font-size: 1.1rem;
            }

            .module-name {
              font-size: 0.95rem;
            }
          }

          .module-roles {
            padding: 0;

            .location-role-item {
              padding: 10px 20px;
              display: flex;
              align-items: center;
              background: #ffffff;
              border-bottom: 1px solid #f8f9fa;
              transition: background-color 0.2s ease;

              &:hover {
                background: #f8f9fa;
              }

              &:last-child {
                border-bottom: none;
              }

              .mdi-check-circle {
                font-size: 1rem;
                flex-shrink: 0;
              }

              .role-name {
                font-size: 0.9rem;
                color: #495057;
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    // Legacy support for old structure
    .location-role-item {
      background: #ffffff;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .location-path {
        background: #e3f2fd;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e9ecef;
        border-left: 4px solid #2196f3;

        strong {
          color: #1976d2;
          font-weight: 600;
        }

        i {
          color: #2196f3;
        }
      }

      .location-role-badges {
        padding: 1.5rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 0.75rem;

        .location-role-badge {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          font-weight: 500;
          color: #495057;
          font-size: 0.875rem;
          transition: all 0.2s ease;

          &:hover {
            background: #e8f5e8;
            border-color: #4caf50;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
          }

          i {
            font-size: 0.75rem;
          }
        }
      }
    }
  }

  .empty-state {
    padding: 3rem 1rem;
    text-align: center;

    i {
      color: #dee2e6;
      margin-bottom: 1rem;
    }

    p {
      color: #6c757d;
      font-size: 0.875rem;
    }
  }

  .modal-fixed-footer {
    background: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 1.5rem 2rem;
    margin-top: auto;

    .footer-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .btn {
        border-radius: 6px;
        font-weight: 500;
        padding: 0.5rem 1.5rem;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .role-assignment-modal,
  .view-roles-modal {
    .modal-dialog {
      max-width: 95% !important;
      width: 95%;
      margin: 1rem auto;
    }
  }

  .assign-permissions-modal,
  .view-roles-modal-body {
    .modal-content-wrapper {
      height: auto;
      min-height: auto;
    }

    .permissions-form-section,
    .preview-section,
    .user-info-section,
    .location-roles-section-wrapper {
      max-height: none;
    }

    .permissions-form-section,
    .user-info-section {
      border-right: none;
      border-bottom: 1px solid #e9ecef;
    }

    .role-checkboxes-grid,
    .role-badges-grid,
    .location-role-badges {
      grid-template-columns: 1fr !important;
    }
  }
}
