import React, { useState, useEffect, useMemo } from 'react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import TimeRangeButtons from './TimeRangeButtons'; // Make sure to import the TimeRangeButtons
import { getTrendlinePoints } from './TrendLinePoints';

// Utility function to subtract time from a date
// ... (same subtractDate function as before)
const subtractDate = (date, interval, units) => {
    const ret = new Date(date);
    switch (interval.toLowerCase()) {
        case 'years':
            ret.setFullYear(ret.getFullYear() - units);
            break;
        case 'months':
            ret.setMonth(ret.getMonth() - units);
            break;
        // ... handle other cases as needed
        default:
            throw new Error('Invalid interval');
    }
    return ret;
};

const LTIFR = ({ rawData, timeline }) => {
    const isUpwardTrend = () => {
        // Assuming the data is sorted by date, compare the first and last values
        const firstValue = rawData[0]?.LTIFR;
        const lastValue = rawData[rawData.length - 1]?.LTIFR;
        return lastValue >= firstValue;
    };

    const lineColor = isUpwardTrend() ? 'red' : 'green'; // Green for upward, red for downward

    const [chartWidth, setChartWidth] = useState(window.innerWidth);
    useEffect(() => {
        console.log(timeline)
        const handleResize = () => {
            // Set chart width based on the window width or parent container's width
            setChartWidth(window.innerWidth);
        };

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Call handler right away so state gets updated with initial window size
        handleResize();

        // Remove event listener on cleanup
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const [activeTab, setActiveTab] = useState('1Y'); // Default to 3M
    const [filteredData, setFilteredData] = useState(rawData); // State for the filtered data

    // Effect hook to update the chart data when the activeTab changes
    useEffect(() => {
        const currentDate = new Date();
        let startDate;

        switch (activeTab) {
            case '3M':
                startDate = subtractDate(currentDate, 'months', 3);
                break;
            case '6M':
                startDate = subtractDate(currentDate, 'months', 6);
                break;
            case '1Y':
                startDate = subtractDate(currentDate, 'years', 1);
                break;
            case '3Y':
                startDate = subtractDate(currentDate, 'years', 3);
                break;
            case 'Max':
                startDate = new Date(Math.min(...rawData.map((data) => new Date(data.date))));
                break;
            default:
                startDate = currentDate; // Default to current date if for some reason the activeTab is not recognized
        }

        // Filter rawData where the date is greater than or equal to startDate
        const newFilteredData = rawData.filter((data) => new Date(data.date) >= startDate);
        setFilteredData(newFilteredData);
    }, [activeTab, rawData]);

    // Callback function to be passed to TimeRangeButtons component
    const onRangeSelected = (range) => {
        setActiveTab(range);
    };

    const trendlinePoints = useMemo(() => getTrendlinePoints(filteredData), [filteredData]);

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', { month: 'short', year: '2-digit' });
    };
    return (
        <div>
            <div className=''>
                <h4>LTIFR* {timeline && timeline.monthYearString} : 0.002</h4>
                <TimeRangeButtons onRangeSelected={onRangeSelected} />
            </div>


            <ResponsiveContainer width="100%" aspect={4.0 / 2.0}>
                <LineChart width={chartWidth} height={200} data={filteredData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="LTIFR"   stroke={lineColor} activeDot={{ r: 8 }} />

                    {/* <Line type="linear" dataKey="y" data={trendlinePoints} stroke="#efefef" dot={true} strokeWidth={0.5} /> */}
                </LineChart>
            </ResponsiveContainer>
        </div>
    );
};

// ... Your rawData and App component here

export default LTIFR;
