import React, { useEffect, useState } from 'react'
import DataTables from '../tables/DataTables';
import $ from "jquery";
import { deletePopup, singlePopup } from "../notifications/Swal";
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { ALL_USERS_BY_LOCATION, API_URL, INCIDENT_REVIWERER_URL, OBSERVATION_REPORT_URL, REPORT_INCIDENT_INVESTIGATION_REINVESTIGATE_URL_WITH_ID, REPORT_INCIDENT_INVESTIGATION_URL_WITH_ID, REPORT_INCIDENT_INVESTIGATION_VERIFY_URL_WITH_ID, REPORT_INCIDENT_LEAD_INVESTIGATOR_URL, REPORT_INCIDENT_URL, REPORT_INCIDENT_URL_WITH_ID, STATIC_URL } from '../constants';
import { Mo<PERSON>, But<PERSON>, Form } from 'react-bootstrap';
import { DropzoneArea } from 'material-ui-dropzone';
import Switch from "react-switch";
import { BodyComponent } from "reactjs-human-body";
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepContent from '@mui/material/StepContent';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import FilterLocation from './FilterLocation';
import IncidentInformationModal from './IncidentInformationModal';
import moment from 'moment';
import Select from 'react-select';

const IncidentInvestigationViewModal = ({ data, verify, incidentData, showModal, setShowModal }) => {

    const [showReviewModal, setShowReviewModal] = useState(false)

    const [incidentReviewer, setIncidentReviewer] = useState([])
    const [selectedReviewer, setSelectedReviewer] = useState('')
    const [files, setFiles] = useState([]);
    const [formData, setFormData] = useState({
        purposeOfInvestigation: "",
        consequenceOfInterest: "",
        timeframeOfInterest: "",
        peopleOfInterest: "",
        equipmentOfInterest: "",
        activitiesOfInterest: "",
        geographicalBoundariesOfInterest: "",
        incidentDescription: "",
        a11: "",
        a12: "",
        a13: "",
        a14: "",
        a15: "",
        a16: "",
        a2: "",
        a3: "",
        a4: "",
        a5: "",
        a6: "",
        b1: "",
        b2: "",
        b3: "",
        b4: "",
        b5: "",
        b6: "",
        c1: "",
        c2: "",
        c3: "",
        c4: "",
        d1: "",
        d2: "",
        d3: "",
        d4: "",
        e1: "",
        e2: "",
        e3: "",
        conclusionRemarks: "",
        conclusionFiles: [],
        sequenceFiles: []
    })

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prevState => ({
            ...prevState,
            [name]: value
        }));
    };
    const handleFileChange = (file) => {
        setFiles(file)

    }
    const [dynamicForm, setDynamicForm] = useState({
        teamMembers: [
            {
                name: "",
                designation: ""
            }
        ],
        events: [
            {
                description: "",
                date: "",
                time: ""
            }
        ],
        people: [
            {
                name: "",
                company: "",
                role: ""
            }
        ],
        equipment: [
            {
                description: ""
            }
        ],
        material: [
            {
                description: ""
            }
        ],
        environment: [
            {
                description: ""
            }
        ],
        document: [
            {
                description: ""
            }
        ]

    })
    useEffect(() => {
        if (incidentData && incidentData.investigationStep) {
            console.log(incidentData, 'Data')

            setDynamicForm(incidentData.investigationStep.dynamicForm)
            setFormData(incidentData.investigationStep)
        }
    }, [incidentData])
    const addItem = (key, item) => {
        setDynamicForm(prevState => ({
            ...prevState,
            [key]: [...prevState[key], item]
        }));
    };

    const updateItem = (key, index, field, value) => {
        setDynamicForm(prevState => {
            const updatedItems = [...prevState[key]];
            updatedItems[index][field] = value;
            return { ...prevState, [key]: updatedItems };
        });
    };

    const deleteItem = (key, index) => {
        setDynamicForm(prevState => {
            const updatedItems = [...prevState[key]];
            updatedItems.splice(index, 1);
            return { ...prevState, [key]: updatedItems };
        });
    };

    const addTeamMembers = () => addItem('teamMembers', { name: "", designation: "" });
    const handleTeamMemberChange = (index, field, value) => updateItem('teamMembers', index, field, value);
    const handleDeleteTeamMember = (index) => deleteItem('teamMembers', index);

    const addEvent = () => addItem('events', { description: "", date: "", time: "" });
    const handleEventChange = (index, field, value) => updateItem('events', index, field, value);
    const handleDeleteEvent = (index) => deleteItem('events', index);

    const addPeople = () => addItem('people', { name: "", company: "", role: "" });
    const handlePeopleChange = (index, field, value) => updateItem('people', index, field, value);
    const handleDeletePeople = (index) => deleteItem('people', index);

    const addEquipment = () => addItem('equipment', { description: "" });
    const handleEquipmentChange = (index, field, value) => updateItem('equipment', index, field, value);
    const handleDeleteEquipment = (index) => deleteItem('equipment', index);

    const addMaterial = () => addItem('material', { description: "" });
    const handleMaterialChange = (index, field, value) => updateItem('material', index, field, value);
    const handleDeleteMaterial = (index) => deleteItem('material', index);

    const addEnvironment = () => addItem('environment', { description: "" });
    const handleEnvironmentChange = (index, field, value) => updateItem('environment', index, field, value);
    const handleDeleteEnvironment = (index) => deleteItem('environment', index);

    const addDocument = () => addItem('document', { description: "" });
    const handleDocumentChange = (index, field, value) => updateItem('document', index, field, value);
    const handleDeleteDocument = (index) => deleteItem('document', index);

    const [locationUsers, setLocationUsers] = useState([]);

    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const result = await axios.post(ALL_USERS_BY_LOCATION, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId });
                setLocationUsers(result.data.map(user => ({
                    value: user.id,
                    label: user.firstName
                })));
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };
        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId) {
            fetchUsers();
            setSelectedReviewer(incidentData.incidentOwnerId)
        }



    }, [incidentData])
    // const handleSubmit = async () => {
    //     const modifiedFormData = { ...formData, dynamicForm: dynamicForm }
    //     const response = await API.patch(REPORT_INCIDENT_INVESTIGATION_URL_WITH_ID(incidentData.id), {


    //         investigationStep: modifiedFormData,
    //         incidentOwnerId: selectedReviewer
    //     })

    //     if (response.status === 204) {
    //         cogoToast.success('Submitted')
    //         setShowModal(false)
    //     }
    // }
    useEffect(() => {
        const fetchData = async () => {
            try {

                const result = await axios.post(INCIDENT_REVIWERER_URL, { locationOneId: incidentData.locationOneId, locationTwoId: incidentData.locationTwoId, locationThreeId: incidentData.locationThreeId, locationFourId: incidentData.locationFourId });
                setIncidentReviewer(result.data);
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };
        if (incidentData.locationOneId && incidentData.locationTwoId && incidentData.locationThreeId && incidentData.locationFourId)
            fetchData();

    }, [incidentData.locationFourId])

    const steps = [

        {
            label: 'Investigation Team',
            description: (
                <>
                    <p className="h5 mb-4">Team Members</p>
                    {dynamicForm.teamMembers.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-4'>
                                Name
                                <input
                                    disabled
                                    className="form-control"
                                    type="text"
                                    value={action.name}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "name", e.target.value)
                                    }
                                />
                            </label>
                            <br />
                            <label className='col-4'>
                                Designation
                                <input
                                    disabled
                                    className="form-control"
                                    type="text"
                                    value={action.designation}
                                    onChange={(e) =>
                                        handleTeamMemberChange(index, "designation", e.target.value)
                                    }
                                />
                            </label>
                            <br />

                        </div>
                    ))}

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Purpose of Investigation</label>
                                <input type='text' name="purposeOfInvestigation" value={formData.purposeOfInvestigation} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Consequence of
                                    Interest</label>
                                <input type='text' name="consequenceOfInterest" value={formData.consequenceOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Timeframe of
                                    Interest</label>
                                <input type='text' name="timeframeOfInterest" value={formData.timeframeOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">People of
                                    Interest</label>
                                <input type='text' name="peopleOfInterest" value={formData.peopleOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Equipment of
                                    Interest</label>
                                <input type='text' name="equipmentOfInterest" value={formData.equipmentOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Activities of
                                    Interest</label>
                                <input type='text' name="activitiesOfInterest" value={formData.activitiesOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Geographical
                                    Boundaries of
                                    Interest</label>
                                <input type='text' name="geographicalBoundariesOfInterest" value={formData.geographicalBoundariesOfInterest} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>
                </>
            )
        },
        {
            label: 'Sequence of Events Leading to Damage / Incident',
            description: (
                <>
                    <p className="h5 mb-4"></p>
                    {dynamicForm.events.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-12'>
                                Description
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleEventChange(index, "description", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                            <label className='col-6'>
                                Date
                                <input
                                    className="form-control"
                                    type="date"
                                    value={action.date}
                                    onChange={(e) =>
                                        handleEventChange(index, "date", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                            <label className='col-6'>
                                Time
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.time}
                                    onChange={(e) =>
                                        handleEventChange(index, "time", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                        </div>
                    ))}


                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Incident Description</label>
                                <input type='text' name="incidentDescription" value={formData.incidentDescription} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <div>
                                    {formData.sequenceFiles.map((fileName, index) => (
                                        <div key={index} style={{ margin: '10px', position: 'relative', display: 'flex', alignItems: 'center' }}>
                                            {/* Hyperlink to the file */}
                                            <a href={`${STATIC_URL}/${fileName}`} target="_blank" rel="noopener noreferrer" style={{ marginRight: '10px' }}>
                                                {fileName}
                                            </a>
                                            {/* Delete button */}



                                        </div>
                                    ))}
                                </div>
                                {/* <DropzoneArea
                                    acceptedFiles={[
                                        'application/pdf',
                                        'image/jpeg',
                                        'image/png'

                                    ]}
                                    dropzoneText={"Drag and drop files that includes sketch/pictures"}
                                    filesLimit={5}

                                    onChange={handleFileChange}
                                /> */}
                            </div>
                        </div>
                    </div>
                </>
            ),
        },
        {
            label: 'Information gathering (A. People)',
            description:
                (<>

                    <p className="h5 mb-4">A. People</p>
                    {dynamicForm.people.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>
                            <label className='col-12'>
                                Name
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.name}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "name", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                            <label className='col-6'>
                                Company / Contractor
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.company}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "company", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                            <label className='col-6'>
                                Role / Involvement
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.role}
                                    onChange={(e) =>
                                        handlePeopleChange(index, "role", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                        </div>
                    ))}

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Name of Injured Person?</label>
                                <input type='text' name="a11" value={formData.a11} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Date of Birth?</label>
                                <input type='text' name="a12" value={formData.a12} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Gender</label>
                                <input type='text' name="a13" value={formData.a13} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Nationality</label>
                                <input type='text' name="a14" value={formData.a14} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Trade Activity</label>
                                <input type='text' name="a15" value={formData.a15} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A1. Responsibility</label>
                                <input type='text' name="a16" value={formData.a16} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A2. What injury, ill-health or damage was caused?</label>
                                <input type='text' name='a2' value={formData.a2} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A3. What did the people involved in the incident do/not do that was essential to continuing the incident
                                    sequence?</label>
                                <input type='text' name="a3" value={formData.a3} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A4. Consider - Physical capability or condition / Mental state / Behaviour / Culture or custom.</label>
                                <input type='text' name="a4" value={formData.a4} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A5. Consider - Skill or competence level.</label>
                                <input type='text' name="a5" value={formData.a5} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">A6. Who were the first responders and what actions were taken?</label>
                                <input type='text' name="a6" value={formData.a6} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                </>)
        },
        {
            label: 'Information Gathering (B. Equipment)',
            description: (<>
                <p className="h5 mb-4">(List the 'Equipment' elements involved in the immediate circumstances of the incident -
                    plant, machinery, equipment, tools, PPE, etc.)</p>
                {dynamicForm.equipment.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleEquipmentChange(index, "description", e.target.value)
                                }
                                disabled
                            />
                        </label>

                    </div>
                ))}


                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B1. Were the equipment in good working condition?</label>
                            <input type='text' name="b1" value={formData.b1} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B2. How were the equipment used?</label>
                            <input type='text' name="b2" value={formData.b2} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B3. Was the shape / nature of the equipment relevant to the incident? Describe.</label>
                            <input type='text' name="b3" value={formData.b3} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B4. Was difficulty / unfamiliarity in using the equipment, etc. a contributory factor? If “Yes”,
                                describe.</label>
                            <input type='text' name="b4" value={formData.b4} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B5. Were the required safety controls implemented to address potential risks from the equipment?
                                Describe.</label>
                            <input type='text' name="b5" value={formData.b5} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">B6. Was safety equipment (e.g., PPE) adequate to operate the equipment in a safe manner?
                                Describe.</label>
                            <input type='text' name="b6" value={formData.b6} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>





            </>),
        },
        {
            label: 'Information Gathering (C. Material)',
            description: (<>

                <p className="h5 mb-4">List the 'Material' elements involved in the immediate circumstances of the incident</p>
                {dynamicForm.material.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleMaterialChange(index, "description", e.target.value)
                                }
                                disabled
                            />
                        </label>

                    </div>
                ))}



                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C1. How were the materials used?</label>
                            <input type='text' name="c1" value={formData.c1} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C2. Was the shape / nature / property of the materials relevant to the incident? Describe.</label>
                            <input type='text' name="c2" value={formData.c2} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C3. Was difficulty / unfamiliarity in handling the materials, etc. a contributory factor? If “Yes”, describe.</label>
                            <input type='text' name="c3" value={formData.c3} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">C4. Were there any safety controls required as per manufacturer’s requirements and were they
                                implemented? Describe.</label>
                            <input type='text' name="c4" value={formData.c4} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>



            </>)
        }, {
            label: 'Information Gathering (D. Environment)',
            description: (<>

                <p className="h5 mb-4">List the 'Environment' elements involved in the immediate circumstances of the
                    incident</p>
                {dynamicForm.environment.map((action, index) => (
                    <div className="form-group align-items-center" key={index}>


                        <label className='col-12'>

                            <input
                                className="form-control"
                                type="text"
                                value={action.description}
                                onChange={(e) =>
                                    handleEnvironmentChange(index, "description", e.target.value)
                                }
                                disabled
                            />
                        </label>

                    </div>
                ))}



                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D1. Location of the incident on the project / operation:
                                What were the positions of all parties (injured party / witnesses), any machinery, materials, barriers,
                                signs, protections, tools &amp; equipment, etc.?
                                (Provide plan)</label>
                            <input type='text' name="d1" value={formData.d1} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D2. Were there anything unusual about the working conditions and work environment? Describe.</label>
                            <input type='text' name="d2" value={formData.d2} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D3. Were maintenance, workplace layout and/or housekeeping relevant factors? If so, what were they?</label>
                            <input type='text' name="d3" value={formData.d3} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>

                <div className='row'>
                    <div className='col'>
                        <div className='form-group'>
                            <label htmlFor="">D4. What other features of the environment were present/absent that was essential to contribute to the
                                incident occurring? Describe.</label>
                            <input type='text' name="d4" value={formData.d4} onChange={handleChange} className='form-control' disabled />
                        </div>
                    </div>
                </div>
            </>)
        }, {
            label: 'Information Gathering (E. Method)',
            description: (
                <>

                    <p className="h5 mb-4">List the 'Documents' elements involved in the immediate circumstances of the
                        incident</p>
                    {dynamicForm.document.map((action, index) => (
                        <div className="form-group align-items-center" key={index}>


                            <label className='col-12'>

                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}
                                    onChange={(e) =>
                                        handleDocumentChange(index, "description", e.target.value)
                                    }
                                    disabled
                                />
                            </label>

                        </div>
                    ))}



                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E1. What documentation is relevant and are they adequate? Describe.
                                    Paper evidence includes all relevant documentation e.g., risk assessments and risk registers / JSA or
                                    safety method statements / EHS plans / drawings / instructions / permits / certification (test,
                                    examination, training), licenses / induction &amp; toolbox talk registers.</label>
                                <input type='text' name="e1" value={formData.e1} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E2. Were the organisation and arrangements for the works contributory factors? Describe.</label>
                                <input type='text' name="e2" value={formData.e2} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>

                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">E3. What other aspects of Systems and Processes were contributory to the incident occurring?
                                    Describe.</label>
                                <input type='text' name="e3" value={formData.e3} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>


                </>
            )
        },
        {
            label: 'Conclusions of Investigation Team',
            description: (
                <>
                    <div className='row'>
                        <div className='col'>
                            <div className='form-group'>
                                <label htmlFor="">Conclusion Remarks
                                </label>
                                <input type='text' name="conclusionRemarks" value={formData.conclusionRemarks} onChange={handleChange} className='form-control' disabled />
                            </div>
                        </div>
                    </div>
                    <div className='row'>
                        <div className='col'>
                            <div>
                                {formData.conclusionFiles.map((fileName, index) => (
                                    <div key={index} style={{ margin: '10px', position: 'relative', display: 'flex', alignItems: 'center' }}>
                                        {/* Hyperlink to the file */}
                                        <a href={`${STATIC_URL}/${fileName}`} target="_blank" rel="noopener noreferrer" style={{ marginRight: '10px' }}>
                                            {fileName}
                                        </a>
                                        {/* Delete button */}

                                      

                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </>)
        },
        {
            label: 'Assign Action Controls',
            description: (
                <>
                    <p className="h5 mb-4">Assign Action Controls</p>
                    {dynamicForm.actions && dynamicForm.actions.map((action, index) => (<>
                        <div className="form-group d-flex align-items-center" key={index}>
                            <label>
                                Corrective/Control measures:
                                <input
                                    className="form-control"
                                    type="text"
                                    value={action.description}

                                    disabled
                                />
                            </label>
                            <br />
                            <label>
                                Due Date
                                <input
                                    className="form-control"
                                    type="text"
                                    value={moment(action.date, 'YYYY-MM-DD').format('Do MMM YYYY')}

                                    disabled
                                />
                            </label>
                            <br />
                            <label>
                                Person Responsible

                                <Select

                                    value={locationUsers.find(option => option.value === action.personResponsible)}
                                    isDisabled={true} // Assuming disabled based on your original code
                                    options={locationUsers.sort((a, b) => a.label.localeCompare(b.label))} // Sort alphabetically


                                    placeholder="Choose" // Optional placeholder text
                                    isClearable={false} // Optional: Disable clearing selection
                                />


                            </label>
                            <br />
                        </div>

                    </>
                    ))}
                    <br />

                </>)
        }

    ];

    const [activeStep, setActiveStep] = useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };

    const handleSubmit = async (type) => {
        if (data) {
            let url = '';
            switch (type) {
                case 'approve':
                    url = REPORT_INCIDENT_INVESTIGATION_VERIFY_URL_WITH_ID(incidentData.id, data.id)
                    break;
                case 'reject':
                    url = REPORT_INCIDENT_INVESTIGATION_REINVESTIGATE_URL_WITH_ID(incidentData.id, data.id)
                    break;
            }

            const response = await API.patch(url, {
                status: ''
            })

            if (response.status === 204) {
                cogoToast.success('Submitted!')
                setShowModal(false);
                setActiveStep(0);
            }
        }

    }
    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    Investigation Part
                </Modal.Header>

                <Modal.Body>
                    <Button onClick={() => setShowReviewModal(true)}> <i className='mdi mdi-eye'></i> View Incident Report</Button>
                    <Box>
                        <Stepper activeStep={activeStep} orientation="vertical">
                            {steps.map((step, index) => (
                                <Step key={step.label}>
                                    <StepLabel
                                        onClick={() => setActiveStep(index)}
                                        style={{ cursor: 'pointer' }}>
                                        {step.label}
                                    </StepLabel>
                                    <StepContent>
                                        <Typography>{step.description}</Typography>
                                        <Box sx={{ mb: 2 }}>
                                            <div>
                                                <Button
                                                    disabled={index === 0}
                                                    className='mt-2'
                                                    onClick={handleBack}
                                                    sx={{ mt: 1, mr: 1 }}
                                                >
                                                    Back
                                                </Button>
                                                {index === steps.length - 1 ? (
                                                    <>

                                                    </>

                                                ) : (

                                                    <Button
                                                        variant="light"
                                                        className='me-2 mt-2'
                                                        onClick={handleNext}
                                                        sx={{ mt: 1, mr: 1 }}
                                                    >
                                                        Continue
                                                    </Button>
                                                )}


                                            </div>
                                        </Box>
                                    </StepContent>
                                </Step>
                            ))}
                        </Stepper>
                        {activeStep === steps.length && (
                            <Paper square elevation={0} sx={{ p: 3 }}>
                                <Typography>Submitted</Typography>

                            </Paper>
                        )}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">

                    {
                        verify && (<>
                            <Button variant="success" onClick={() => { handleSubmit('approve') }}>Approve</Button>
                            <Button variant="primary" onClick={() => { handleSubmit('reject') }}>Return</Button>
                        </>)
                    }

                    <Button variant="light" onClick={() => { setShowModal(false); setActiveStep(0); }}>Close</Button>




                </Modal.Footer>
            </Modal>

            {(incidentData.id && showReviewModal) && <IncidentInformationModal readOnly={true} type={'View'} id={incidentData.id} showModal={showReviewModal} setShowModal={setShowReviewModal} />}
        </>
    )
}

IncidentInvestigationViewModal.defaultProps = {
    verify: false,
    data: false
}

export default IncidentInvestigationViewModal;