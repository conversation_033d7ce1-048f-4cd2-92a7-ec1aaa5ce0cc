import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import moment from "moment";
import { ALL_INCIDENT_URL } from "../constants";
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';

const TopWorkplaceActivitiesBarChart = ({ data, dateRange, filterCriteria }) => {
    const impactLevels = [
  'Near-Miss',
  'Level 1 (First Aid Incident - FAI)',
  'Level 2 (Medical Treatment Incident - MTI)',
  'Level 3 (Lost Time Incident - LTI)',
  'Level 4 (High Severity Incident)',
  'Level 5 (Critical Incident)'
];
    const [chartData, setChartData] = useState(null);
    const [selectedImpacts, setSelectedImpacts] = useState(impactLevels);

    useEffect(() => {
        const fetchData = async () => {
            try {
                if (!data) return;
                let incidents = data;




                incidents = incidents.filter(item => {
                    // Extract country from locationOne.name (text inside parentheses)
                    const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                    // Determine BU from locationThree.name
                    let bu = "Other";
                    const buName = item.locationThree?.name || "";

                    if (/Construction|Fitouts/i.test(buName)) {
                        bu = "Construction";
                    } else if (/DC|Data Center|Data Centre/i.test(buName)) {
                        bu = "DC";
                    } else if (/Office/i.test(buName)) {
                        bu = "Office";
                    }

                    // Normalize impact level
                    const impactRaw = (item.actualImpact || '').toLowerCase().trim();
                    let impactLevel = null;
                    if (impactRaw.includes("near miss")) {
                        impactLevel = "Near-Miss";
                    } else if (impactRaw.includes("level 1")) {
                        impactLevel = "Level 1 (First Aid Incident - FAI)";
                    } else if (impactRaw.includes("level 2")) {
                        impactLevel = "Level 2 (Medical Treatment Incident - MTI)";
                    } else if (impactRaw.includes("level 3")) {
                        impactLevel = "Level 3 (Lost Time Incident - LTI)";
                    } else if (impactRaw.includes("level 4")) {
                        impactLevel = "Level 4 (High Severity Incident)";
                    } else if (impactRaw.includes("level 5")) {
                        impactLevel = "Level 5 (Critical Incident)";
                    }





                    // Now apply the filters
                    const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
                    const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
                    const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;
                    const isImpactSelected = selectedImpacts.length === 0 || 
                        (impactLevel && selectedImpacts.includes(impactLevel));

                    return isCountrySelected && isBUSelected && isSiteSelected && isImpactSelected;
                });

                const [startDate, endDate] = dateRange.map((d) => moment(d));

                // Step 1: Filter incidents by date range using incidentDate
                const filteredIncidents = incidents.filter((incident) => {
                    const date = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A", true);
                    return date.isValid() && date.isBetween(startDate, endDate, null, "[]");
                });

                // Step 2: Group by monthYear → activity → count
                const activityCountsByMonth = {};

                filteredIncidents.forEach((incident) => {
                    const date = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A");
                    const monthYear = date.format("YYYY-MM");
                    const activity = incident.workActivity?.name || "Unknown";

                    if (!activityCountsByMonth[monthYear]) {
                        activityCountsByMonth[monthYear] = {};
                    }

                    if (!activityCountsByMonth[monthYear][activity]) {
                        activityCountsByMonth[monthYear][activity] = 0;
                    }

                    activityCountsByMonth[monthYear][activity] += 1;
                });

                // Generate all months in the date range
                const allMonths = [];
                let current = startDate.clone().startOf("month");
                while (current.isSameOrBefore(endDate, "month")) {
                    allMonths.push(current.format("YYYY-MM"));
                    current.add(1, "month");
                }

                // Ensure every month has an entry in activityCountsByMonth
                allMonths.forEach((monthYear) => {
                    if (!activityCountsByMonth[monthYear]) {
                        activityCountsByMonth[monthYear] = {};
                    }
                });

                // Format the data with guaranteed months
                const formattedData = allMonths.map((monthYear) => {
                    const topActivities = Object.entries(activityCountsByMonth[monthYear])
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 5)
                        .map(([activity, count]) => ({ activity, count }));

                    return {
                        monthYear,
                        activities: topActivities,
                    };
                });

                // Generate labels from allMonths
                const labels = allMonths.map((monthYear) =>
                    moment(monthYear, "YYYY-MM").format("MMM YYYY")
                );


                // Step 4: Get all unique top activities across all months
                const allActivities = [
                    ...new Set(
                        formattedData.flatMap((item) =>
                            item.activities.map((a) => a.activity)
                        )
                    ),
                ];

                const customColors = [
                    "#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#E91E63",
                    "#795548", "#00BCD4", "#FFEB3B", "#FF5722", "#607D8B",
                    "#673AB7", "#3F51B5", "#8BC34A", "#CDDC39", "#F44336",
                    "#03A9F4", "#FFB300", "#D32F2F", "#388E3C", "#7B1FA2",
                ];

                // Step 5: Build Highcharts series
                const seriesData = allActivities.map((activity, index) => {
                    const dataPoints = formattedData.map((monthData) => {
                        const act = monthData.activities.find((a) => a.activity === activity);
                        return act ? act.count : 0;
                    });

                    return {
                        name: activity,
                        data: dataPoints,
                        color: customColors[index % customColors.length],
                        stack: "activityStack",
                    };
                });

                setChartData({ labels, series: seriesData });
            } catch (error) {
                console.error("Error fetching workplace activities data:", error);
            }
        };

        fetchData();
    }, [data, dateRange, selectedImpacts]);

    const options = chartData
        ? {
            chart: {
                type: "column",
                zoomType: "xy",
            },
            title: {
                text: "",
            },
            xAxis: {
                categories: chartData.labels,
                title: { text: "Month-Year" },
                crosshair: true,
            },
            yAxis: {
                min: 0,
                title: { text: "Activity Count" },
                stackLabels: {
                    enabled: true,
                },
            },
            tooltip: {
                shared: true,
                pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
            },
            plotOptions: {
                column: {
                    stacking: "normal",
                    dataLabels: {
                        enabled: true,
                    },
                },
            },
            legend: {
                layout: "vertical",
                align: "right",
                verticalAlign: "middle",
                floating: false,
                itemMarginBottom: 5,
                labelFormatter() {
                    return ` ${this.name}`;
                },
                navigation: {
                    enabled: true,
                },
            },
            series: chartData.series,
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            "downloadPNG",
                            "downloadJPEG",
                            "downloadPDF",
                            "downloadSVG",
                            "separator",
                            "downloadCSV",
                            "downloadXLS",
                        ],
                    },
                },
            },
        }
        : null;

    const handleImpactChange = (value) => {
        setSelectedImpacts(value);
    };

    return (
        <div>
            <div style={{ marginBottom: '20px' }}>
                <MultiSelect
                    value={selectedImpacts}
                    options={impactLevels}
                    onChange={(e) => handleImpactChange(e.value)}
                    placeholder="Select Impact Levels"
                    style={{ width: '50%' }}
                    display="chip"
                />
            </div>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default TopWorkplaceActivitiesBarChart;
