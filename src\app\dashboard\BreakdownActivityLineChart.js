import React, { useState, useEffect, useCallback } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import { parse, isWithinInterval } from "date-fns";
import moment from "moment";

// Color palette moved outside component to avoid re-creation
const colorPalette = [
    "#E6194B", "#3CB44B", "#FFE119", "#0082C8", "#F58231",
    "#911EB4", "#46F0F0", "#F032E6", "#D2F53C", "#FABEBE",
    "#008080", "#E6BEFF", "#AA6E28", "#800000", "#AAFFC3",
    "#808000", "#FFD8B1", "#000080", "#808080", "#000000"
];

const BreakdownActivityLineChart = ({ dateRange, data }) => {
    const [chartData, setChartData] = useState(null);

    // Function to get color in circular fashion
    const getColor = useCallback((index) => colorPalette[index % colorPalette.length], []);

    useEffect(() => {
        const processData = () => {
            if (!data?.highRiskBreakdownByMonth) {
                setChartData(null);
                return;
            }

            try {
                const { highRiskBreakdownByMonth } = data;

                if (!highRiskBreakdownByMonth || !Array.isArray(highRiskBreakdownByMonth)) {
                    console.error("Invalid data structure:", data);
                    return;
                }

                const [startDate, endDate] = dateRange.map((date) => new Date(date));

                const filteredData = highRiskBreakdownByMonth.filter((item) => {
                    const itemDate = parse(item.monthYear, "yyyy-MM", new Date());
                    return isWithinInterval(itemDate, { start: startDate, end: endDate });
                });

                if (filteredData.length === 0) {
                    console.warn("No data within the specified date range.");
                    setChartData(null);
                    return;
                }

                const labels = filteredData.map((item) =>
                    moment(item.monthYear, "YYYY-MM").format("MMM YYYY")
                );

                const uniqueActivities = Array.from(
                    new Set(filteredData.flatMap((item) => item.activities.map((a) => a.activity)))
                );

                // Assign colors dynamically
                const seriesData = uniqueActivities.map((activity, index) => ({
                    name: activity,
                    data: filteredData.map((month) => {
                        const activityData = month.activities.find((a) => a.activity === activity);
                        return activityData ? activityData.count : 0;
                    }),
                    color: getColor(index), // Assign color in circular order
                }));

                setChartData({
                    categories: labels,
                    series: seriesData,
                });
            } catch (error) {
                console.error("Error processing data:", error);
            }
        };

        processData();
    }, [dateRange, data, getColor]);

    // Highcharts Configuration
    const options = chartData
        ? {
            chart: {
                type: "spline",
                zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
            },
            title: {
                text: "",
            },
            xAxis: {
                categories: chartData.categories,
                title: {
                    text: "Month-Year",
                },
                crosshair: true,
            },
            yAxis: {
                title: {
                    text: "Activity Count",
                },
                min: 0,
            },
            tooltip: {
                shared: true,
                valueSuffix: " occurrences",
            },
            plotOptions: {
                spline: {
                    marker: {
                        enabled: true,
                    },
                    dataLabels: {
                        enabled: true,
                    },
                },
            },
            legend: {
                enabled: true,
            },
            series: chartData.series,
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            "downloadPNG",
                            "downloadJPEG",
                            "downloadPDF",
                            "downloadSVG",
                            "separator",
                            "downloadCSV",
                            "downloadXLS",
                        ],
                    },
                },
            },
        }
        : null;

    return <>{options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}</>;
};

export default BreakdownActivityLineChart;
