import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    INCIDENT_CATEGORY_URL, INCIDENT_CATEGORY_TYPE_URL, INCIDENT_TYPE_DESCRIPTION_URL
} from '../constants';
import { createAxiosInstanceWithToken } from './TempAxios';

const IncidentCircumstanceDropdown = ({ incidentData, setIncidentData, readOnly }) => {
    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);


    const [selectedLocationOne, setSelectedLocationOne] = useState(incidentData.incidentCircumstanceCategory ? incidentData.incidentCircumstanceCategory.id ?? '' : '');
    const [selectedLocationTwo, setSelectedLocationTwo] = useState(incidentData.incidentCircumstanceType ? incidentData.incidentCircumstanceType.id ?? '' : '');
    const [selectedLocationThree, setSelectedLocationThree] = useState(incidentData.incidentCircumstanceDescription ? incidentData.incidentCircumstanceDescription.id ?? '' : '');


    const axiosInstance = createAxiosInstanceWithToken();

    useEffect(() => {
        axiosInstance.get(INCIDENT_CATEGORY_URL)
            .then(response => {
                setLocationOne(response.data);
            })
            .catch(error => {
                console.error('Error fetching location-one options', error);
            });
    }, []);

    useEffect(() => {
        if (selectedLocationOne) {
            axiosInstance.get(INCIDENT_CATEGORY_TYPE_URL(selectedLocationOne))
                .then(response => {
                    setLocationTwo(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-two options', error);
                });
        }
    }, [selectedLocationOne]);

    useEffect(() => {
        if (selectedLocationTwo) {
            axiosInstance.get(INCIDENT_TYPE_DESCRIPTION_URL(selectedLocationTwo))
                .then(response => {
                    setLocationThree(response.data);

                })
                .catch(error => {
                    console.error('Error fetching location-three options', error);
                });
        }
    }, [selectedLocationTwo]);



    return (
        <div className='row'>

            {/* ... similar dropdowns for locationOne, locationTwo, etc. ... */}
            {/* Just as a representative sample, here's the first dropdown corrected for potential type mismatch: */}
            <div className='form-group col-md-2'>
                <label>Incident Type <span style={{ color: 'red' }}>*</span></label>
                <select
                    className="form-select me-2"
                    value={selectedLocationOne}
                    disabled={readOnly}
                    onChange={(e) => {
                        setSelectedLocationOne(e.target.value);
                        setIncidentData((prev) => ({ ...prev, incidentCircumstanceCategoryId: e.target.value }));
                    }}
                >
                    <option value="">Select</option>
                    {locationOne.map(location => (
                        <option key={location.id} value={location.id} disabled={location.name === "Environmental"}>
                            {location.name}
                        </option>
                    ))}
                </select>
            </div>



            {/* Location Two Dropdown */}
            <div className='form-group col-md-5'>
                <label>Category <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" value={selectedLocationTwo} disabled={readOnly} onChange={(e) => { setSelectedLocationTwo(e.target.value); setIncidentData((prev) => ({ ...prev, incidentCircumstanceTypeId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationTwo.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>
            </div>

            {/* Location Three Dropdown */}
            <div className='form-group col-md-5'>
                <label>Circumstance <span style={{ color: 'red' }}>*</span></label>
                <select className="form-select me-2" value={selectedLocationThree} disabled={readOnly} onChange={(e) => { setSelectedLocationThree(e.target.value); setIncidentData((prev) => ({ ...prev, incidentCircumstanceDescriptionId: e.target.value })) }}>
                    <option value="">Select</option>
                    {locationThree.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                    ))}
                </select>
            </div>


        </div>
    );
};

IncidentCircumstanceDropdown.defaultProps = {
    readOnly: false
}
export default IncidentCircumstanceDropdown;
