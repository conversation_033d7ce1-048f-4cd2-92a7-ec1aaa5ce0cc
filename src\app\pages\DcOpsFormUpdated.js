import React, { useState } from 'react';
import { Form, Card } from 'react-bootstrap';

const DcOpsFormUpdated = () => {
  // State variables for fire department and insurance
  const [informFireDept, setInformFireDept] = useState('');
  const [fireDeptDetails, setFireDeptDetails] = useState('');
  const [informFireInsurance, setInformFireInsurance] = useState('');
  const [fireInsuranceDetails, setFireInsuranceDetails] = useState('');

  // Handle fire department radio button change
  const handleFireDeptChange = (value) => {
    setInformFireDept(value);
    // Clear details if "No" is selected
    if (value === 'No') {
      setFireDeptDetails('');
    }
  };

  // Handle fire insurance radio button change
  const handleFireInsuranceChange = (value) => {
    setInformFireInsurance(value);
    // Clear details if "No" is selected
    if (value === 'No') {
      setFireInsuranceDetails('');
    }
  };

  return (
    <div className="container">
      <Card className="mb-4">
        <Card.Body>
          <div className="section p-1">
            <div className="row">
              <div className="col-12">
                <label>Is there any need to inform fire department? </label>
                <div className="isolation-options p-3">
                  <label className="me-3 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}>
                    <input
                      type="radio"
                      name="informFireDept"
                      value="Yes"
                      checked={informFireDept === 'Yes'}
                      onChange={() => handleFireDeptChange('Yes')}
                    />
                    <span className="ps-3"> Yes</span>
                  </label>
                  <label className="p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}>
                    <input
                      type="radio"
                      name="informFireDept"
                      value="No"
                      checked={informFireDept === 'No'}
                      onChange={() => handleFireDeptChange('No')}
                    />
                    <span className="ps-3"> No </span>
                  </label>
                </div>
                
                {/* Conditional text box for fire department details */}
                {informFireDept === 'Yes' && (
                  <div className="mt-3">
                    <Form.Group>
                      <Form.Label>Please provide fire department details:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        value={fireDeptDetails}
                        onChange={(e) => setFireDeptDetails(e.target.value)}
                        placeholder="Enter fire department details"
                      />
                    </Form.Group>
                  </div>
                )}
              </div>

              <div className="col-12 mt-4">
                <label>Is there any need to inform fire insurance company? </label>
                <div className="isolation-options p-3">
                  <label className="me-3 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}>
                    <input
                      type="radio"
                      name="informFireInsurance"
                      value="Yes"
                      checked={informFireInsurance === 'Yes'}
                      onChange={() => handleFireInsuranceChange('Yes')}
                    />
                    <span className="ps-3"> Yes</span>
                  </label>
                  <label className="p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }}>
                    <input
                      type="radio"
                      name="informFireInsurance"
                      value="No"
                      checked={informFireInsurance === 'No'}
                      onChange={() => handleFireInsuranceChange('No')}
                    />
                    <span className="ps-3"> No </span>
                  </label>
                </div>
                
                {/* Conditional text box for fire insurance details */}
                {informFireInsurance === 'Yes' && (
                  <div className="mt-3">
                    <Form.Group>
                      <Form.Label>Please provide fire insurance company details:</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        value={fireInsuranceDetails}
                        onChange={(e) => setFireInsuranceDetails(e.target.value)}
                        placeholder="Enter fire insurance company details"
                      />
                    </Form.Group>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default DcOpsFormUpdated;
