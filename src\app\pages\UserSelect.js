import React, { useEffect, useState } from "react";

const UserSelect = ({ allUsers, handleSelectedUsers, selectedUsersList, newSignal }) => {

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [deselectedUsers, setDeselectedUsers] = useState([]);

    useEffect(() => { setDeselectedUsers([]) }, [newSignal])
    useEffect(() => {
        console.log(selectedUsersList)
        if(selectedUsersList)
            setSelectedUsers(selectedUsersList)
        // setDeselectedUsers([])
    }, [selectedUsersList])

    function filterUsers(users) {
        if (searchQuery.trim() === '') {
            return users;
        }

        return users.filter((user) => {
            return user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                user.email.toLowerCase().includes(searchQuery.toLowerCase());
        });
    }
    function handleCheckboxChange(event, user) {
        if (event.target.checked) {
            setSelectedUsers([...selectedUsers, user]);
            setDeselectedUsers(deselectedUsers.filter((u) => u !== user.id));
        } else {
            setSelectedUsers(selectedUsers.filter((u) => u.id !== user.id));
            setDeselectedUsers(prev => [...prev, user.id])
        }
    }

    function handleSave() {
        handleSelectedUsers(selectedUsers, deselectedUsers)

    }
    return (<>
        <input

            type="text"
            className='form-control w-50'
            placeholder="Search users..."
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.target.value)}
        />
        <ul className='list-style-type-none'>
            {filterUsers(allUsers).map((user) => (
                <li key={user.id}>
                    <label>
                        <input
                            type="checkbox"
                            checked={selectedUsers.some((u) => u.id === user.id)}
                            onChange={(event) => handleCheckboxChange(event, user)}
                        />
                        &nbsp; {user.firstName} ({user.email})
                    </label>
                    {/* <i class="mdi mdi-text-box-check-outline text-danger cursor-pointer" onClick={(e) => openAssignmentModal(user.id, user.email)}></i> */}
                </li>
            ))}
        </ul>
        <button className="btn btn-primary" onClick={handleSave}>Save Selection</button>
    </>)
}

export default UserSelect;