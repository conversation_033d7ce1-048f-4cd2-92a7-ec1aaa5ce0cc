import React from "react";
import Chart from 'react-apexcharts';

function linearRegression(y, x) {
    let lr = {};
    let n = y.length;
    let sum_x = 0;
    let sum_y = 0;
    let sum_xy = 0;
    let sum_xx = 0;
    let sum_yy = 0;

    for (let i = 0; i < y.length; i++) {
        sum_x += x[i];
        sum_y += y[i];
        sum_xy += (x[i] * y[i]);
        sum_xx += (x[i] * x[i]);
        sum_yy += (y[i] * y[i]);
    }

    lr['slope'] = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x);
    lr['intercept'] = (sum_y - lr.slope * sum_x) / n;

    return lr;
}


const LineChart = (props) => {
    // Assume that props.dates is an array of arrays, where each sub-array is [date, value]
    let xValues = props.dates.map(point => new Date(point[0]).getTime());
    let yValues = props.dates.map(point => point[1]);

    let lr = linearRegression(yValues, xValues);

    // Generate best fit line data
    let bestFitLine = props.dates.map(point => {
        return [point[0], lr.slope *  new Date(point[0]).getTime() + lr.intercept];
    });

    const options = {
        chart: {
            type: 'area',
            stacked: false,
            height: 350,
            zoom: {
                type: 'x',
                enabled: true,
                autoScaleYaxis: true
            },
            toolbar: {
                autoSelected: 'zoom'
            }
        },
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0,
        },
        title: {
            text: props.header,
            align: 'left'
        },
        stroke: {
            width: [2, 1], // Adjust line thickness here if needed
            opacity: [1, 0.5]
        },
    
        colors: ['#FF0000', '#000'],
        fill: {
            type: ['gradient', 'solid'],
            gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0,
                stops: [0, 90, 100]
            },
        },
        yaxis: {
            labels: {
                formatter: function (val) {
                    return (val / 1000000).toFixed(0);
                },
            },
            title: {
                text: 'data'
            },
        },
        xaxis: {
            type: 'datetime',
        },
        annotations: {
            position: 'front',
            yaxis: [{
                y: lr.slope * new Date(props.dates[Math.floor(props.dates.length / 2)][0]).getTime() + lr.intercept,
                borderColor: '#000',
                label: {
                    borderColor: '#000',
                    style: {
                        color: '#fff',
                        background: '#000',
                    },
                    text: 'Trendline',
                },
            }],
        },
        tooltip: {
            shared: false,
            y: {
                formatter: function (val) {
                    return (val / 1000000).toFixed(0)
                }
            }
        }
    }


    const series = [{
        name: props.title,
        data: props.dates, // Use the dummy dataset here
        type: 'area'
    }]


    return (<>
        <Chart
            options={options}
            series={series}
            type="area"
            height={props.height ? props.height : "250"}
        />
    </>)
}

export default LineChart;