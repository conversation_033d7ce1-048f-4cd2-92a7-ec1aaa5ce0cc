/* Todo list */

.add-items {
  margin-top: 0.5rem;
  overflow: hidden;
  input[type="text"] {
    width: 100%;
    background: transparent;
    border: 0;
    padding-left: 0;
    @include placeholder {
      font-size: 1rem;
      color: #9b9b9b;
    }
  }
  .btn {
    margin-left: .5rem;
    i {
      font-size: 2.25rem;
    }
    .rtl & {
      margin-left: auto;
      margin-right: .5rem;
    }
    &.btn-icons {
      width: 24px;
      height: 24px;
      padding: 0;
      padding-top: 1px;
      text-align: center;
      i {
        font-size: 1rem;
        margin-right: 0;
      }
    }
  }
}

.list-wrapper {
  max-height: 100%;
  ul {
    padding: 0;
    text-align: left;
    list-style: none;
    margin-bottom: 0;
    li {
      @extend .d-flex;
      @extend .align-items-center;
      @extend .justify-content-start;
      font-size: .9375rem;
      padding: 1.063rem 0;
      border: none;
      border-bottom: 1px solid $border-color;
      margin-bottom: 0.812rem;
      border-radius: 7px;
      .form-check{
        @extend %ellipsor;
        max-width: 90%;
        margin-top: .25rem;
        margin-bottom: .25rem;
        .form-check-label{
          @extend%ellipsor;
        }
      }
    }
    &.todo-list {
      &.rtl-todo {
        display: none;
      }
      .rtl & {
        display: none;
        &.rtl-todo {
          display: block;
        }
      }
    }
  }

  input[type="checkbox"] {
    margin-right: 15px;
  }

  .remove {
    @extend .ms-auto;
    @extend .text-secondary;
    .rtl & {
      @extend .ms-0;
      @extend .me-auto;
    }
    cursor: pointer;
    font-size: .7rem;
    font-weight: 600;
    width: 1.25rem;
    height: 1.25rem;
    line-height: 1.25rem;
    text-align: center;
  }

  .completed {
    .form-check {
      text-decoration: line-through;
      text-decoration-color: $info;
    }
    .remove {
      @extend .text-primary;
    }
  }
}
