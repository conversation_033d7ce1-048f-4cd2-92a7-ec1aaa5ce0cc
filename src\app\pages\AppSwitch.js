import React, { useEffect, useState } from 'react'
import Select from 'react-select'
import * as Icon from 'feather-icons-react';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import { useSelector } from 'react-redux';

function AppSwitch({ value }) {
    const history = useHistory()
    const me = useSelector((state) => state.login.user)
    const validationRoles = me?.validationRoles || [];
    const isReporter = validationRoles.some(role => role.name === 'Statistics Reporter' || role.name === 'Statistics Reviewer');
    const [option, setOption] = useState([{ label: 'Observation', value: 'ehs' }, { label: 'ePermit Work', value: 'eptw' }, { label: 'Incident', value: 'incident' }, { label: 'Inspection', value: 'inspection' }, { label: 'Audit', value: 'audit' }, { label: 'Plant & Equipment', value: 'equipment' }, { label: 'EHS Statistics', value: 'reports' }])


    const getApp = (e) => {
        history.push(e.value)


    }
    useEffect(() => {

        if (isReporter === false) {
            const updatedOption = option.filter(item => item.value !== 'reports');
            setOption(updatedOption);
        }

    }, [])
    const BootstrapTooltip = styled(({ className, ...props }: TooltipProps) => (
        <Tooltip  {...props} arrow classes={{ popper: className }} />
    ))(({ theme }) => ({
        [`& .${tooltipClasses.arrow}`]: {
            color: theme.palette.common.black,
        },
        [`& .${tooltipClasses.tooltip}`]: {
            backgroundColor: theme.palette.common.black,
            padding: 10
        },
    }));
    return (
        <div className='row mb-5'>
            <div className='row align-items-center'>
                <div className='col-3 mt-3 mb-2'>
                    <Select
                        labelKey="label"
                        id="user_description"
                        placeholder="Type..."
                        options={option}
                        onChange={getApp}
                        className={'fw-bold switchapp'}
                        value={value}
                        styles={{
                            // Fixes the overlapping problem of the component
                            menu: provided => ({ ...provided, zIndex: 9999 })
                        }}
                    />
                </div>
                <div className="col-1 p-0 mt-1" >

                    <BootstrapTooltip title="Switch between assigned applications">
                        <Button>  <Icon.Info currentColor={'#315975'} /></Button>
                    </BootstrapTooltip>


                </div>
            </div>
            {/* <p className='mb-5 actionDesc '>Nam quo sed id quis. Odio est perferendis sit necessitatibus accusamus sint in.</p> */}
        </div>
    )
}

export default AppSwitch