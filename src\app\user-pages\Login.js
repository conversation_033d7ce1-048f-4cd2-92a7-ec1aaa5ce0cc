import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Form } from 'react-bootstrap';
import { Auth } from 'aws-amplify';
import { useHistory, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { loginActions } from '../store/login-slice';
import { CognitoUserPool, CognitoUser, AuthenticationDetails } from 'amazon-cognito-identity-js';

// Auth.configure({

//   userPoolId: 'ap-southeast-1_fz5Vs76Zi', //UserPool ID
//   region: 'ap-southeast-1',
//   userPoolWebClientId: '442hm82196m9s23ouppdsu169u', //WebClientId
//   oauth: {
//     domain: "ssowithinternalusers.auth.ap-southeast-1.amazoncognito.com",
//     scope: [
//       "email",
//       "openid",
//     ],
//     redirectSignIn: "http://localhost:3001",
//     redirectSignOut: "http://localhost:3001",
//     responseType: "token",

//   }
// });

const Login = () => {
  const email = useRef();
  const password = useRef();
  const history = useHistory();
  const location = useLocation();

  const dispatch = useDispatch();

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const code = searchParams.get('code');
    if (code) {
      fetch(`https://${process.env.REACT_APP_AWS_DOMAIN}/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `grant_type=authorization_code&client_id=${process.env.REACT_APP_AWS_CLIENT_ID}&code=${code}&redirect_uri=${encodeURIComponent(process.env.REACT_APP_REDIRECT_URL)}`
      })
        .then(response => response.json())
        .then(data => {
          console.log(data)
          const access_token = data.access_token;
          localStorage.setItem('access_token', access_token);
          dispatch(loginActions.setLogin());
          history.push('/dashboard')

        })
        .catch(error => console.error('Error:', error));
    }


  }, [])

  // const handleLogin = (e) => {
  //   e.preventDefault();
  //   const rEmail = email.current.value;
  //   const rPassword = password.current.value;
  //   Auth.signIn(rEmail, rPassword).then(async (result) => {
  //     //Success 
  //     console.log(result)
  //     const token = await getToken();
  //     localStorage.setItem('access_token', token);
  //     dispatch(loginActions.setLogin());
  //     history.push('/dashboard')

  //   }).catch((err) => {
  //     // Something is Wrong
  //   })

  // }

  const handleLoginWithAzure = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    // const newUser = await Auth.federatedSignIn({ provider: 'ADWITHAWS' });
    // console.log(newUser);
    const redirectUrl = `https://${process.env.REACT_APP_AWS_DOMAIN}/oauth2/authorize?client_id=${process.env.REACT_APP_AWS_CLIENT_ID}&response_type=code&scope=email+openid+phone&redirect_uri=${encodeURIComponent(process.env.REACT_APP_REDIRECT_URL)}`;
    console.log(redirectUrl)
    window.location.replace(redirectUrl);
    setIsLoading(false);

  }

  const getToken = async () => {
    var data = await Auth.currentSession()
    return data.idToken.jwtToken
  }


  return (
    <div>
      <div className="row  auth px-0 ">
        <div className='col-6 bg-white' style={{ position: 'relative' }}>
          <div className='bg-background-cover'></div>

        </div>
        <div className='col-6 d-flex align-items-center bg-white'>
          <div className='col-12 p-0'>
            <div className="text-left " >
              <div className="brand-logo">
                <img src={require("../../assets/images/logo.png")} alt="logo" />
              </div>
              <h2>Welcome to Environment, Health and Safety</h2>
              <h6 className="font-weight-light">Sign in to continue.</h6>
              <Form className="pt-3">

                <div className="mb-2">
                  <button onClick={(e) => handleLoginWithAzure(e)} type="button" className={isLoading ? "btn btn-block btn-secondary  disabled" : "btn btn-block btn-secondary "}  >
                    {process.env.REACT_APP_LOGIN_BUTTON_TEXT}
                  </button>
                </div>

              </Form>
            </div>
          </div>
        </div>

      </div>
    </div>
  )

}

export default Login
