import React, { useState, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { LOCATION1_URL } from '../constants';
import API from '../services/API';
// Your API endpoints


const LocationSelector = ({ selectedProject }) => {
    const [countries, setCountries] = useState([]);
    const [selectedCountry, setSelectedCountry] = useState(null);

    const [cities, setCities] = useState([]);
    const [selectedCity, setSelectedCity] = useState(null);

    const [businessUnits, setBusinessUnits] = useState([]);
    const [selectedBusinessUnit, setSelectedBusinessUnit] = useState(null);

    const [projects, setProjects] = useState([]);
    const [selectedProjectData, setSelectedProjectData] = useState(null);

    const [levels, setLevels] = useState([]);
    const [selectedLevel, setSelectedLevel] = useState(null);

    const [zones, setZones] = useState([]);
    const [selectedZone, setSelectedZone] = useState(null);

    useEffect(() => {
        // Start the tier-based fetch process from the selected project (which has locationThreeId)
        if (selectedProject) {
            setSelectedProjectData(selectedProject)
            getAllLocations()
        }
    }, [selectedProject]);

    const getAllLocations = async () => {
        const response = await API.get(LOCATION1_URL);
        setCountries(response.data)
    }


    return (
        <div>



            <label htmlFor="country">Country</label>
            <Dropdown
                id="country"
                value={selectedCountry}
                options={[selectedCountry]}  // Pre-fill as a single option
                disabled={true}  // Disable the dropdown
            />


        </div>
    );
};

export default LocationSelector;
