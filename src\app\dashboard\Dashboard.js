import React, { useState, useEffect } from 'react';
import Card<PERSON>verlay from '../pages/CardOverlay';
import { OBSERVATION_REPORT_URL, OBSERVATION_REPORT_WITH_ID, STATIC_URL, ACTION_URL, PERMIT_REPORT_WITH_ID } from '../constants';
import FilterLocation from '../pages/FilterLocation';
import API from '../services/API';
import ObservationModal from '../pages/ObservationModal';
import moment from 'moment';
import IncidentReport from '../pages/IncidentReport';
import IncidentCardReport from '../pages/IncidentCardReport';
import PermitModal from '../pages/PermitModal';
import LineChart from './LineChart';
import { Sticky, StickyContainer } from 'react-sticky';
import AllFilterLocation from '../pages/AllLocationFilter';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import TimeRangeButtons from './TimeRangeButtons';
import MonthSelector from '../pages/MonthSelector';
import IncidentStats from '../pages/IncidentStats';
import TRIR from './TRIR';
import LTIFR from './LTIFR';

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`observation-tabpanel-${index}`}
      aria-labelledby={`observation-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `observation-tab-${index}`,
    'aria-controls': `observation-tabpanel-${index}`,
  };
}

const Dashboard = () => {


  const dates1 = [
    [new Date("2023-07-01").getTime(), 15000000],
    [new Date("2023-07-02").getTime(), 17000000],
    [new Date("2023-07-03").getTime(), 12000000],
    [new Date("2023-07-04").getTime(), 14000000],
    [new Date("2023-07-05").getTime(), 18000000],
    [new Date("2023-07-06").getTime(), 21000000],
    [new Date("2023-07-07").getTime(), 25000000],
    [new Date("2023-07-08").getTime(), 19000000],
    [new Date("2023-07-09").getTime(), 22000000],
    [new Date("2023-07-10").getTime(), 20000000],
  ];

  const dates2 = [
    [new Date("2023-07-01").getTime(), 17000000],
    [new Date("2023-07-02").getTime(), 20000000],
    [new Date("2023-07-03").getTime(), 15000000],
    [new Date("2023-07-04").getTime(), 17000000],
    [new Date("2023-07-05").getTime(), 28000000],
    [new Date("2023-07-06").getTime(), 24000000],
    [new Date("2023-07-07").getTime(), 24000000],
    [new Date("2023-07-08").getTime(), 22000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 500000000],
  ];

  const dates3 = [
    [new Date("2023-07-01").getTime(), 10000000],
    [new Date("2023-07-02").getTime(), 70000000],
    [new Date("2023-07-03").getTime(), 14000000],
    [new Date("2023-07-04").getTime(), 55000000],
    [new Date("2023-07-05").getTime(), 68000000],
    [new Date("2023-07-06").getTime(), 21000000],
    [new Date("2023-07-07").getTime(), 15000000],
    [new Date("2023-07-08").getTime(), 79000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 34000000],
  ];

  const dates4 = [
    [new Date("2023-07-01").getTime(), 55000000],
    [new Date("2023-07-02").getTime(), 22000000],
    [new Date("2023-07-03").getTime(), 66000000],
    [new Date("2023-07-04").getTime(), 64000000],
    [new Date("2023-07-05").getTime(), 78000000],
    [new Date("2023-07-06").getTime(), 91000000],
    [new Date("2023-07-07").getTime(), 35000000],
    [new Date("2023-07-08").getTime(), 69000000],
    [new Date("2023-07-09").getTime(), 12000000],
    [new Date("2023-07-10").getTime(), 20000000],
  ];

  const dates5 = [
    [new Date("2023-07-01").getTime(), 23000000],
    [new Date("2023-07-02").getTime(), 27000000],
    [new Date("2023-07-03").getTime(), 22000000],
    [new Date("2023-07-04").getTime(), 24000000],
    [new Date("2023-07-05").getTime(), 28000000],
    [new Date("2023-07-06").getTime(), 31000000],
    [new Date("2023-07-07").getTime(), 35000000],
    [new Date("2023-07-08").getTime(), 29000000],
    [new Date("2023-07-09").getTime(), 32000000],
    [new Date("2023-07-10").getTime(), 30000000],
  ];

  const dates6 = [
    [new Date("2023-07-01").getTime(), 37000000],
    [new Date("2023-07-02").getTime(), 40000000],
    [new Date("2023-07-03").getTime(), 35000000],
    [new Date("2023-07-04").getTime(), 37000000],
    [new Date("2023-07-05").getTime(), 48000000],
    [new Date("2023-07-06").getTime(), 44000000],
    [new Date("2023-07-07").getTime(), 44000000],
    [new Date("2023-07-08").getTime(), 42000000],
    [new Date("2023-07-09").getTime(), 32000000],
    [new Date("2023-07-10").getTime(), 700000000],
  ];

  const dates7 = [
    [new Date("2023-07-01").getTime(), 30000000],
    [new Date("2023-07-02").getTime(), 90000000],
    [new Date("2023-07-03").getTime(), 34000000],
    [new Date("2023-07-04").getTime(), 75000000],
    [new Date("2023-07-05").getTime(), 88000000],
    [new Date("2023-07-06").getTime(), 41000000],
    [new Date("2023-07-07").getTime(), 35000000],
    [new Date("2023-07-08").getTime(), 99000000],
    [new Date("2023-07-09").getTime(), 32000000],
    [new Date("2023-07-10").getTime(), 54000000],
  ];

  const dates8 = [
    [new Date("2023-07-01").getTime(), 65000000],
    [new Date("2023-07-02").getTime(), 32000000],
    [new Date("2023-07-03").getTime(), 76000000],
    [new Date("2023-07-04").getTime(), 74000000],
    [new Date("2023-07-05").getTime(), 88000000],
    [new Date("2023-07-06").getTime(), 101000000],
    [new Date("2023-07-07").getTime(), 45000000],
    [new Date("2023-07-08").getTime(), 79000000],
    [new Date("2023-07-09").getTime(), 42000000],
    [new Date("2023-07-10").getTime(), 50000000],
  ];




  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

  };

  const currentDate = moment();
  const [obsData, setObsData] = useState([]);
  const [actionData, setActionData] = useState([]);

  useEffect(() => {
    getObservationData();
    getActionData();
  }, [])

  const getObservationData = async () => {
    const response = await API.get(OBSERVATION_REPORT_URL);
    if (response.status === 200) {
      setObsData(response.data)

    }
  }

  const getActionData = async () => {



    const response = await API.get(ACTION_URL);
    if (response.status === 200) {
      setActionData(response.data)

    }
  }

  const [showObsReportModal, setShowObsReportModal] = useState(false);
  const [obsReportData, setObsReportData] = useState(null);


  const [showEptwReportModal, setShowEptwReportModal] = useState(false);
  const [eptwReportData, setEptwReportData] = useState(null);

  const viewEptwReport = async (id) => {

    const params = {
      "include": [{ "relation": "permitReportAction" }, { "relation": "applicant" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

    };
    const response = await API.get(`${PERMIT_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      const actionUploads = (response.data.permitReportActions && response.data.permitReportActions.length) ? response.data.permitReportActions.flatMap(obj => obj.uploads) : [];

      response.data.uploads = [...response.data.uploads, ...actionUploads]

      response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []


      setEptwReportData(response.data)
      setShowEptwReportModal(true)
    }

  }

  const viewObservationReport = async (id) => {

    const params = {
      "include": [{ "relation": "actions" }, { "relation": "workActivity" }, { "relation": "ghsOne" }, { "relation": "ghsTwo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

    };
    const response = await API.get(`${OBSERVATION_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

    if (response.status === 200) {

      const actionUploads = (response.data.actions && response.data.actions.length) ? response.data.actions.flatMap(obj => obj.uploads) : [];

      response.data.uploads = [...response.data.uploads]

      response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []




      response.data.evidence = response.data.evidence ? response.data.evidence.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []


      response.data.evidences = response.data.evidences ? response.data.evidences.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []

      response.data.actionUploads = [...actionUploads];
      response.data.actionUploads = response.data.actionUploads ? response.data.actionUploads.map(i => {
        return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
      }) : []


      setObsReportData(response.data)
      setShowObsReportModal(true)
    }

  }

  const renderActionTxt = (actionType) => {
    let actionTxt = ''
    if (actionType == 'dcso_approver') actionTxt = 'Review, Isolate & Approve'
    else if (actionType == 'assessor') actionTxt = 'Review & Assess'
    else if (actionType == 'normalization') actionTxt = 'Normalize'
    else if (actionType == 'approver') actionTxt = 'Review and Approve'
    return actionTxt
  }

  const [value, setValue] = useState(0);


  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const [timeline, setTimeline] = useState({
    monthYearString: '',
    month: 0,
    year: 0
  })
  const handleMonthChange = (monthYearString, month, year) => {

    setTimeline({ monthYearString: monthYearString, month: month, year: year })
  }

  const rawDataCumulativeTRIR = [
    { date: '2023-01-01', TRIR: 0.16 },
    { date: '2023-02-01', TRIR: 0.00 },
    { date: '2023-03-01', TRIR: 0.11 },
    { date: '2023-04-01', TRIR: 0.07 },
    { date: '2023-05-01', TRIR: 0.11 },
    { date: '2023-06-01', TRIR: 0.09 },
    { date: '2023-07-01', TRIR: 0.07 },
    { date: '2023-08-01', TRIR: 0.06 },
    { date: '2023-09-01', TRIR: 0.06 },
    { date: '2023-10-01', TRIR: 0.05 },
    { date: '2023-11-01', TRIR: 0.11 },
    { date: '2023-12-01', TRIR: 0.11 },

    { date: '2022-01-01', TRIR: 0.13 },
    { date: '2022-02-01', TRIR: 0.00 },
    { date: '2022-03-01', TRIR: 0.00 },
    { date: '2022-04-01', TRIR: 0.12 },
    { date: '2022-05-01', TRIR: 0.17 },
    { date: '2022-06-01', TRIR: 0.13 },
    { date: '2022-07-01', TRIR: 0.17 },
    { date: '2022-08-01', TRIR: 0.14 },
    { date: '2022-09-01', TRIR: 0.17 },
    { date: '2022-10-01', TRIR: 0.19 },
    { date: '2022-11-01', TRIR: 0.17 },
    { date: '2022-12-01', TRIR: 0.18 },

  ];

  const rawDataCumulativeLTIFR = [
    { date: '2023-01-01', value: 0.03 },
    { date: '2023-02-01', value: 0.00 },
    { date: '2023-03-01', value: 0.00 },
    { date: '2023-04-01', value: 0.00 },
    { date: '2023-05-01', value: 0.05 },
    { date: '2023-06-01', value: 0.00 },
    { date: '2023-07-01', value: 0.00 },
    { date: '2023-08-01', value: 0.00 },
    { date: '2023-09-01', value: 0.00 },
    { date: '2023-10-01', value: 0.00 },
    { date: '2023-11-01', value: 0.00 },
    { date: '2023-12-01', value: 0.00 },

  ];
  return (

    <>
      <CardOverlay>

        <Box sx={{ width: '100%' }}>


          <div className='row'>
            <h4 className='text-center'>EHS PERFORMANCE DASHBOARD</h4>
            <div className='my-3'></div>
            <MonthSelector onChange={handleMonthChange} className="mb-3" />
            <div className='my-3'></div>
            <StickyContainer>
              <Sticky topOffset={0}>
                {({ style }) => (
                  <div style={{ ...style, top: 97, backgroundColor: '#fff', zIndex: 100 }}>
                    <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={false} />
                  </div>
                )}
              </Sticky>


              <IncidentStats />
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>

              <div className='row'>
                <div className='col-md-6'>
                  <div className='card shadow'>
                    <div className='card-body'>
                      {/* <TimeRangeButtons /> */}
                      <TRIR rawData={rawDataCumulativeTRIR.sort((a, b) => new Date(a.date) - new Date(b.date))} timeline={timeline} />
                      {/* <LineChart dates={dates6} height={400} header="LTIFR Trends" title="" /> */}
                    </div>
                  </div>
                </div>
                <div className='col-md-6'>
                  <div className='card shadow'>
                    <div className='card-body'>
                      <LTIFR rawData={rawDataCumulativeLTIFR.sort((a, b) => new Date(a.date) - new Date(b.date))} timeline={timeline} />
                      {/* <TimeRangeButtons /> */}
                      {/* <LineChart dates={dates5} height={400} header="TRIR Trends" title="" /> */}
                    </div>
                  </div>
                </div>
              </div>

              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-3'>
                      <div className='d-flex justify-content-between align-items-center'>
                        <h4 className='flex-1'>YTD No of At Risk Observations </h4>
                        <h1>67</h1>
                      </div>
                      <LineChart dates={dates1} header="" title="Observation" />
                    </div>
                    <div className='col-3'>
                      <div className='d-flex justify-content-between align-items-center'>
                        <h4 className='flex-1'>YTD No of Safe Observations </h4>
                        <h1>89</h1>
                      </div>
                      <LineChart dates={dates2} header="" title="Observation" />
                    </div>

                    <div className='col-3'>
                      <div className='d-flex justify-content-between align-items-center'>
                        <h4 className='flex-1'> YTD Observations Corrected on the Spot </h4>
                        <h1>10</h1>
                      </div>
                      <LineChart dates={dates3} header="" title="Observation" />
                    </div>

                    <div className='col-3'>
                      <div className='d-flex justify-content-between align-items-center'>
                        <h4 className='flex-1'>YTD % Actions not Closed within Identified Time </h4>
                        <h1>98%</h1>
                      </div>
                      <LineChart dates={dates4} header="" title="Observation" />
                    </div>
                  </div>
                </div>

              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>Monthly Serious or Potentially Serious Frequency Rate</h6>
                      <h1 className='text-center my-3'>56</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i>No Target Set</h4>
                        </div>

                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-down-thin'></i> 27% </span></h4>
                          <p>Vis-&#225;-Vis June 2022</p>
                        </div>

                      </div>

                      <div className='mt-5'>
                        <div className='card-body'>
                          <h6 className='text-center'>YTD Serious or Potentially Serious Frequency Rate
                          </h6>
                          <h1 className='text-center my-3'>23</h1>
                          <div className='d-flex justify-content-between'>
                            <div>
                              <h4><i className='mdi mdi-target me-2'></i>No Target Set</h4>
                            </div>

                            <div>
                              <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-down-thin'></i> 12% </span></h4>
                              <p>YoY as of June 2023</p>
                            </div>
                          </div>

                        </div>
                      </div>
                    </div>
                    <div className='col-md-6'>
                      <LineChart dates={dates5} height={400} header="Serious or Potentially Serious Frequency Rate Trends" title="" />
                    </div>
                  </div>

                </div>
              </div>

              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Observation Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD Lost Time Injury Incidence Rate</h6>
                      <h1 className='text-center my-5'>2.34</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i>No Target Set</h4>
                        </div>

                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-down-thin'></i> 53% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates2} header="Lost Time Injury Incidence Rate Trends" title="Incident" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Permit to Work Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD % of Operational Controls Found Effective During Audits</h6>
                      <h1 className='text-center my-5'>98.7</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 3% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>

                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 12% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates3} header="% of Operational Controls Effectiveness Trends" title="Percentage" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD % of Planned Corrective Actions Implemented on Time</h6>
                      <h1 className='text-center my-5'>55</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i>No Target Set</h4>
                        </div>

                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 200% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates4} header="% of Planned Corrective Actions Timely Implementation Trends" title="Planned Actions" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD No. of Toolbox talks for 100,000 hours of Work</h6>
                      <h1 className='text-center my-5'>83.5</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 5% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>


                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 20% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates5} header="No. of Toolbox talks Trends" title="Toolbox Talks" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD Risk Competency Index of Internal Teams</h6>
                      <h1 className='text-center my-5'>66</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i><span className='text-success'> <i className='mdi mdi-arrow-up-thin'></i> 7% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>


                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-up-thin'></i> 12% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates6} header="Risk Competency Index Trends" title="Risk Competency Index" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD Risk Competency Index of Contract Staff</h6>
                      <h1 className='text-center my-5'>72</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i><span className='text-success'> <i className='mdi mdi-arrow-up-thin'></i> 8% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>


                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-up-thin'></i> 8% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates7} header="Risk Competency Index Trends" title="Risk Competency Index" />
                    </div>
                  </div>
                </div>
              </div>
              <div className='mt-5 mb-3'>
                {/* <h4 className='text-center'> Overall Incident Reporting Dashboard</h4> */}
              </div>
              <div className='card shadow'>
                <div className='card-body'>
                  <div className='row'>
                    <div className='col-md-6'>

                      <h6 className='text-center'>YTD # of Delayed Implementation of Identified Risk Control Measures</h6>
                      <h1 className='text-center my-5'>102</h1>
                      <div className='d-flex justify-content-between'>
                        <div>
                          <h4><i className='mdi mdi-target me-2'></i><span className='text-primary'> <i className='mdi mdi-arrow-down-thin'></i> 5% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>


                        <div>
                          <h4><i className='mdi mdi-calendar-month'></i><span className='text-success'> <i className='mdi mdi-arrow-down-thin'></i> 27% </span></h4>
                          <p>YoY as of June 2023</p>
                        </div>
                      </div>


                    </div>
                    <div className='col-md-6'>

                      <LineChart dates={dates8} header="Delated Implementation Trends" title="" />
                    </div>
                  </div>
                </div>
              </div>

            </StickyContainer>
          </div>



        </Box>




      </CardOverlay>

      <ObservationModal reportData={obsReportData} showReportModal={showObsReportModal} setShowReportModal={(status) => setShowObsReportModal(status)} />
      <PermitModal reportData={eptwReportData} showReportModal={showEptwReportModal} setShowReportModal={(status) => setShowEptwReportModal(status)} />
    </>

  )
}

export default Dashboard;