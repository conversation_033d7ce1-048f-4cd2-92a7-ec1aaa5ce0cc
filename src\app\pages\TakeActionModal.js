import React, { useState, useEffect, useRef } from "react";
import { Mo<PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, AUDIT_ACTION_PLAN_REVIEWER, AUDIT_ACTION_URL, AUDIT_FINDINGS_ACTION_URL, AUDIT_FINDINGS_ASSIGN_ACTION_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_URL, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import moment from 'moment';
import PdfIcon from '@material-ui/icons/PictureAsPdf';
import DescriptionIcon from '@material-ui/icons/Description';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import GalleryPage from "../apps/Gallery";
import IncidentInformationModal from "./IncidentInformationModal";

const TakeActionModal = ({ data, applicationType, showModal, setShowModal }) => {

    console.log(data)

    const [showReviewModal, setShowReviewModal] = useState(false)

    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])

    const userId = useRef();

    const actionTaken = useRef();


    const [rootCause, setRootCause] = useState('');
    const [correctiveAction, setCorrectiveAction] = useState('');
    const [actionDesc, setActionDesc] = useState('');


    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {
            if (applicationType === 'Observation') {
                getObsUsers();

            }



            if (applicationType === 'Inspection') {
                getInspectionUsers();
            }
        }

        if (data.applicationDetails?.audit?.locationOneId && data.applicationDetails?.audit?.locationTwoId && data.applicationDetails?.audit?.locationThreeId && data.applicationDetails?.audit?.locationFourId) {

            if (applicationType === 'AuditFinding') {
                getAuditUsers();
            }
        }
    }, [])

    const getObsUsers = async () => {
        const response = await API.post(OBSERVATION_REVIEWER_LIST_URL, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const getAuditUsers = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, { locationOneId: data.applicationDetails.audit.locationOneId, locationTwoId: data.applicationDetails.audit.locationTwoId, locationThreeId: data.applicationDetails.audit.locationThreeId, locationFourId: data.applicationDetails.audit.locationFourId, mode: 'audit-action-plan-reviewer' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

    const getInspectionUsers = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId, mode: 'inspection-action-plan-reviewer' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const handleSubmit = async () => {
        try {

            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {

                const originalNames = fileResponse.data.files.map(file => file.originalname);

                let url = '';
                let actionType = '';
                let assignedToId = '';

                switch (applicationType) {
                    case 'Observation':
                        url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
                        actionType = 'reviewer'
                        assignedToId = userId.current.value
                        break;

                    case 'INCIDENT':
                        url = REPORT_INCIDENT_ACTIONS_WITH_ID(data.id);
                        actionType = data.actionType
                        break;

                    case 'Audit':
                        url = AUDIT_FINDINGS_ACTION_URL(data.id);
                        actionType = data.actionType
                        break;

                    case 'AuditFinding':
                        url = AUDIT_ACTION_URL(data.id);
                        actionType = data.actionType
                        assignedToId = userId.current.value
                        break;

                    case 'Inspection':
                        url = INSPECTION_ACTION_URL(data.id);
                        actionType = data.actionType
                        assignedToId = userId.current.value
                        break;
                }
                let taken = "";
                if (applicationType === 'AuditFinding') {
                    taken = JSON.stringify({
                        rootCause: rootCause,
                        actionDesc: correctiveAction,
                        correctiveAction: actionDesc
                    })

                } else {
                    taken = actionTaken.current.value
                }

                const response = await API.patch(url, {
                    actionType: actionType,
                    comments: data.comments ? data.comments : '',
                    actionTaken: taken,
                    assignedToId: assignedToId,
                    actionToBeTaken: data.actionToBeTaken,
                    objectId: data.objectId,
                    description: data.description,
                    dueDate: data.dueDate,
                    uploads: originalNames,
                    createdDate: data.createdDate,
                    sequenceNo: data.sequenceNo || ''
                })

                if (response.status === 204) {
                    cogoToast.success('Submitted!')
                    setShowModal(false)
                }
            }
        } catch (e) { console.log(e); setShowModal(false) }
    }

    const getFileAddedMessage = (fileName) => {
        return `${fileName} successfully added.`;
    };

    const getPreviewIcon = (fileObject, classes) => {
        const { type, name } = fileObject.file;
        if (type === 'application/pdf') {
            return <PdfIcon className={classes.image} />;
        } else if (name.endsWith('.xlsx') || name.endsWith('.xls')) {
            return <DescriptionIcon className={classes.image} />;
        }
        // Default preview for non-PDF/Excel files
        return (
            <img
                alt={fileObject.file.name}
                className={classes.image}
                src={fileObject.data}
            />
        );
    };
    const quillStyle = {}; // default height

    const matched = data.applicationDetails.postActions?.find(
        (p) => p.sequenceNo === data.sequenceNo
      );

      // Extract label from checklist using i and index
      let questionLabel = '';
      let main = '';
      if (
        matched &&
        matched.i !== undefined &&
        matched.index !== undefined &&
        data.applicationDetails.checklist?.value?.[matched.index]?.questions?.[matched.i]
      ) {
        questionLabel = data.applicationDetails.checklist.value[matched.index].questions[matched.i].label;
        main =data.applicationDetails.checklist.value[matched.index].label
      }

    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>


                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>}
                        {applicationType === 'INCIDENT' && `Incident - ${data.applicationDetails.title}`}
                        {applicationType === 'AuditFinding' && <h5 className="card-title" style={{ fontSize: '20px' }}>{applicationType} </h5>}


                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>

                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                {!applicationType === 'Inspection' &&  <Button onClick={() => setShowReviewModal(true)} className="mb-3"> <i className='mdi mdi-eye'></i> View Incident Report</Button>}
                                    {<h5 className="card-title">ID : {data.applicationDetails.maskId}</h5>}
                                    {applicationType === 'Inspection' && <h5 className="card-title"> {main}</h5>}
                                 {applicationType === 'Inspection' && <h5 className="card-title"> Q : {questionLabel}</h5>}

                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <input type="text" className="form-control" value={`${data.applicationDetails.locationOne && data.applicationDetails.locationOne.name} > ${data.applicationDetails.locationTwo && data.applicationDetails.locationTwo.name} > ${data.applicationDetails.locationThree && data.applicationDetails.locationThree.name} > ${data.applicationDetails.locationFour && data.applicationDetails.locationFour.name}`} readOnly />
                                    </div>}

                                    {applicationType === 'INCIDENT' && <>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Title</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.title}
                                                </p>
                                            </div>
                                            {/* <div className="col-md-6">
                                                <p className="obs-title">Location</p>
                                                <p className="obs-content">
                                                    {[
                                                        data.applicationDetails.locationOne?.name,
                                                        data.applicationDetails.locationTwo?.name,
                                                        data.applicationDetails.locationThree?.name,
                                                        data.applicationDetails.locationFour?.name
                                                    ].filter(Boolean).join(" > ")}
                                                </p>

                                            </div> */}
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Incident Date</p>
                                                <p className="obs-content">
                                                    {moment(data.applicationDetails.incidentDate, 'DD/MM/YYYY hh:mm A').format('Do MMM YYYY')}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">Location</p>
                                                <p className="obs-content">
                                                    {[
                                                        data.applicationDetails.locationOne?.name,
                                                        data.applicationDetails.locationTwo?.name,
                                                        data.applicationDetails.locationThree?.name,
                                                        data.applicationDetails.locationFour?.name
                                                    ].filter(Boolean).join(" > ")}
                                                </p>

                                            </div>
                                        </div>
                                        {/* <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Incident Date</p>
                                                <p className="obs-content">
                                                ${data.applicationDetails.actualImpact} ${data.applicationDetails.potentialImpact && `(${data.applicationDetails.potentialImpact})`}`
                                                </p>
                                            </div>
                                        </div> */}
                                    </>}

                                    {applicationType === 'Inspection' && <>

                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Checklist</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.checklist?.name}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title">Description</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.checklist?.description}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Location</p>
                                                <p className="obs-content">
                                                    {[
                                                        data.applicationDetails.locationOne?.name,
                                                        data.applicationDetails.locationTwo?.name,
                                                        data.applicationDetails.locationThree?.name,
                                                        data.applicationDetails.locationFour?.name
                                                    ].filter(Boolean).join(" > ")}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title"> Date</p>
                                                <p className="obs-content">
                                                    {moment(data.applicationDetails.created).format('Do MMM YYYY') || '' || ''}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <p className="obs-title">Scheduler</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.assignedBy?.firstName}
                                                </p>
                                            </div>
                                            <div className="col-md-6">
                                                <p className="obs-title"> Inspector</p>
                                                <p className="obs-content">
                                                    {data.applicationDetails.assignedTo?.firstName || ''}
                                                </p>
                                            </div>
                                        </div>
                                    </>


                                    }


                                    {
                                        applicationType === 'AuditFinding' && <>
                                            <div className="row mb-4 p-2" style={{ border: '1px solid #e7e6e6' }}>
                                                <div className="col-6">
                                                    <p>Project/DC: <b>{data.applicationDetails?.audit?.locationFour.name}</b></p>
                                                    <p>Start Date: <b>{moment(data.applicationDetails?.audit?.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>

                                                </div>
                                                <div className="col-6">
                                                    <p>Auditor Name: <b>{data.applicationDetails?.audit?.assignedTo?.firstName}</b></p>
                                                    <p>End Date: <b>{moment(data.applicationDetails?.audit?.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>
                                                </div>
                                                <div className="col-12">
                                                    <p>Findings: <b>{data.applicationDetails?.findings}</b></p>
                                                </div>

                                                <div className="col-6">
                                                    <p>Category: <b>{data.applicationDetails?.category}</b></p>
                                                    <p>Standards And References: <b>{data.applicationDetails?.standardsAndReferences}</b></p>
                                                    <p>Recommended Mitigation Measures: <b>{data.applicationDetails?.recommendations}</b></p>
                                                </div>
                                                <div className="col-6">
                                                    <p>Classification: <b>{data.applicationDetails?.classification}</b></p>
                                                    <p>Potential Consequences: <b>{data.applicationDetails?.potentialHazard}</b></p>
                                                    <p>Due Date: <b>{data.dueDate}</b></p>
                                                </div>

                                            </div>

                                            {/* <p>Findings: {data.applicationDetails?.findings}</p> */}
                                        </>}

                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Zone</label>
                                        <input type="text" className="form-control" value={data.applicationDetails.locationSix && data.applicationDetails.locationSix.name} readOnly />
                                    </div>}

                                    {
                                        data.uploads && data.uploads.length > 0 && (
                                            <div className="mb-3">
                                                <label className="form-label">Uploads</label>
                                                <div className="border p-3 row">
                                                    {(() => {
                                                        const images = [];
                                                        const otherFiles = [];

                                                        data.uploads.forEach((i) => {
                                                            const fileExtension = i.split('.').pop().toLowerCase();

                                                            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                                // Collect image files for gallery
                                                                images.push({ src: `${STATIC_URL}/${i}`, width: 4, height: 3 });
                                                            } else {
                                                                // Collect non-image files
                                                                otherFiles.push({ file: i, fileExtension });
                                                            }
                                                        });

                                                        return (
                                                            <>
                                                                {/* Render image gallery */}
                                                                {images.length > 0 && (
                                                                    <div className="col-12 mb-3">
                                                                        <GalleryPage photos={images} />
                                                                    </div>
                                                                )}

                                                                {/* Render other files */}
                                                                {otherFiles.map(({ file, fileExtension }) => {
                                                                    if (fileExtension === 'pdf') {
                                                                        // Handle PDF files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-pdf"
                                                                                        style={{
                                                                                            color: "red",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    View PDF
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                                        // Handle Excel files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-excel"
                                                                                        style={{
                                                                                            color: "green",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    Download Excel File
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else if (['doc', 'docx'].includes(fileExtension)) {
                                                                        // Handle Word files
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <a
                                                                                    href={`${STATIC_URL}/${file}`}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                >
                                                                                    <i
                                                                                        className="pi pi-file-word"
                                                                                        style={{
                                                                                            color: "blue",
                                                                                            fontSize: "1.5em",
                                                                                        }}
                                                                                    ></i>{" "}
                                                                                    Download Word File
                                                                                </a>
                                                                            </div>
                                                                        );
                                                                    } else {
                                                                        // Handle other file types
                                                                        return (
                                                                            <div className="col-md-3" key={file}>
                                                                                <i
                                                                                    className="pi pi-file"
                                                                                    style={{ fontSize: "1.5em" }}
                                                                                ></i>{" "}
                                                                                <p>Unsupported file type: {fileExtension}</p>
                                                                            </div>
                                                                        );
                                                                    }
                                                                })}
                                                            </>
                                                        );
                                                    })()}
                                                </div>
                                            </div>
                                        )
                                    }

                                    {
                                        (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && (
                                            <div className="mb-3">
                                                <label className="form-label">Uploads</label>
                                                <div className="border p-3 row">
                                                    {data.applicationDetails.uploads.map((i, index) => {
                                                        const fileExtension = i.split('.').pop().toLowerCase();

                                                        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                            // Handle image files in a gallery
                                                            const images = data.applicationDetails.uploads
                                                                .filter((file) => ['jpg', 'jpeg', 'png', 'gif'].includes(file.split('.').pop().toLowerCase()))
                                                                .map((file) => ({
                                                                    src: `${STATIC_URL}/${file}`,
                                                                    width: 4,
                                                                    height: 3,
                                                                }));

                                                            return (
                                                                <div className="col-md-12" key={index}>
                                                                    <GalleryPage photos={images} />
                                                                </div>
                                                            );
                                                        } else if (fileExtension === 'pdf') {
                                                            // Handle PDF files with an icon
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a
                                                                        href={`${STATIC_URL}/${i}`}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                    >
                                                                        <i
                                                                            className="pi pi-file-pdf"
                                                                            style={{ color: "red", fontSize: "1.5em" }}
                                                                        ></i>{" "}
                                                                        View PDF
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                            // Handle Excel files with an icon
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a
                                                                        href={`${STATIC_URL}/${i}`}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                    >
                                                                        <i
                                                                            className="pi pi-file-excel"
                                                                            style={{ color: "green", fontSize: "1.5em" }}
                                                                        ></i>{" "}
                                                                        Download Excel File
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else if (['doc', 'docx'].includes(fileExtension)) {
                                                            // Handle Word files with an icon
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a
                                                                        href={`${STATIC_URL}/${i}`}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                    >
                                                                        <i
                                                                            className="pi pi-file-word"
                                                                            style={{ color: "blue", fontSize: "1.5em" }}
                                                                        ></i>{" "}
                                                                        Download Word File
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else {
                                                            // Handle other file types or show a default icon and message
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <i
                                                                        className="pi pi-file"
                                                                        style={{ fontSize: "1.5em" }}
                                                                    ></i>{" "}
                                                                    <p>Unsupported file type: {fileExtension}</p>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )
                                    }

                                    {(applicationType === 'INCIDENT' && data.applicationDetails.description) && <div className="mb-3">
                                        <label className="form-label">Description of Incident</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.applicationDetails.description}</textarea>
                                    </div>}

                                    {(!applicationType === 'INCIDENT' && data.description) && <div className="mb-3">
                                        <label className="form-label">Description</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.description}</textarea>
                                    </div>}


                                    {data.comments && <div className="mb-3">
                                        <label className="form-label">Comments</label>
                                        <textarea className="form-control" rows="3" readOnly>{data.comments}</textarea>
                                    </div>}

                                    {
                                        applicationType === 'AuditFinding' ? <> {data.actionToBeTaken && <div className="mb-3">
                                            {/* <label className="form-label">{data.actionToBeTaken}</label> */}
                                            {/* <textarea className="form-control" rows="3" value={data.actionToBeTaken} readOnly></textarea> */}
                                        </div>}</> : <>
                                            {data.actionToBeTaken && <div className="mb-3">
                                                <label className="form-label">Actions to be taken</label>
                                                <textarea className="form-control" rows="3" value={data.actionToBeTaken} readOnly></textarea>
                                            </div>}</>
                                    }

                                    {
                                        applicationType === 'AuditFinding' ? <>  <div className="mb-3">
                                            <label className="form-label">Identify the Root Cause(s)</label>
                                            <ReactQuill value={rootCause} onChange={setRootCause} className="form-control p-0" style={quillStyle} />
                                            {/* <textarea ref={actionTaken} className="form-control" rows="3"></textarea> */}
                                        </div></> : <>
                                            <div className="mb-3">
                                                <label className="form-label">Action taken</label>
                                                <textarea ref={actionTaken} className="form-control p-0" rows="3"></textarea>
                                            </div></>
                                    }

                                    {
                                        applicationType === 'AuditFinding' && <>  <div className="mb-3">
                                            <label className="form-label">Identified Corrective Actions</label>
                                            <ReactQuill value={correctiveAction} onChange={setCorrectiveAction} className="form-control p-0" style={quillStyle} />
                                            {/* <textarea ref={correctiveAction} className="form-control" rows="3"></textarea> */}
                                        </div></>
                                    }

                                    {
                                        applicationType === 'AuditFinding' && <>  <div className="mb-3">
                                            <label className="form-label">Description of the Action Taken</label>
                                            <ReactQuill value={actionDesc} onChange={setActionDesc} className="form-control p-0" style={quillStyle} />
                                            {/* <textarea ref={actionDesc} className="form-control" rows="3"></textarea> */}
                                        </div></>
                                    }




                                    <div className="mb-3">
                                        <label className="form-label">{applicationType === 'AuditFinding' ? "Upload evidence of Action Taken" : 'Upload evidence'}</label>
                                        <DropzoneArea
                                            acceptedFiles={[
                                                'image/jpeg',
                                                'image/png',
                                                'application/pdf',
                                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                                'application/vnd.ms-excel'
                                            ]}
                                            dropzoneText={"Drag and Drop / Upload Evidence"}
                                            filesLimit={5}
                                            maxFileSize={104857600}
                                            onChange={handleFileChange}
                                            showPreviewsInDropzone={true}
                                            getFileAddedMessage={getFileAddedMessage}
                                            getPreviewIcon={getPreviewIcon}

                                        />
                                    </div>

                                    {applicationType === 'Observation' && <div className="mb-3">
                                        <label className="form-label">Submit to</label>
                                        <select className="form-select" ref={userId}>
                                            <option>Select</option>
                                            {
                                                users.map(u => (
                                                    <option key={u.id} value={u.id}>{u.firstName}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                    }

                                    {applicationType === 'AuditFinding' && <div className="mb-3">
                                        <label className="form-label">{applicationType === 'AuditFinding' ? 'Audit Action Reviewer' : 'Submit to'}</label>
                                        <select className="form-select" ref={userId}>
                                            <option>Select</option>
                                            {
                                                users.map(u => (
                                                    <option key={u.id} value={u.id}>{u.firstName}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                    }

                                    {applicationType === 'Inspection' && <div className="mb-3">
                                        <label className="form-label">Submit to</label>
                                        <select className="form-select" ref={userId}>
                                            <option>Select</option>
                                            {
                                                users.map(u => (
                                                    <option key={u.id} value={u.id}>{u.firstName}</option>
                                                ))
                                            }
                                        </select>
                                    </div>
                                    }

                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={handleSubmit}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Submit
                    </Button>


                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>

             {(data.applicationDetails?.id && showReviewModal) && <IncidentInformationModal readOnly={true} type={'View'} id={data.applicationDetails?.id} showModal={showReviewModal} setShowModal={setShowReviewModal} />}
        </>
    )
}

export default TakeActionModal;