import React, { useState, useMemo, useEffect } from 'react'
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import { useSelector } from "react-redux";
import CardOverlay from './CardOverlay';
import API from '../services/API';
import { ACTION_URL, CHECKLIST_URL, INSPECTION_URL } from '../constants';
import InspectionForm from './InspectionForm';
import cogoToast from 'cogo-toast';
import InspectionTable from './InspectionTable';
import ActionCard from './ActionCard';
import AppSwitch from './AppSwitch';
import { InputText } from 'primereact/inputtext';
import AllFilterLocationVertical from './AllFilterLocationVertical';
import moment from 'moment'
import InspectionScheduling from './InspectionScheduling';
import Dashboard from './inspection/Dashboard';
import Typography from '@mui/material/Typography';
import Reports from './inspection/Reports';
import Actions from './inspection/Actions';
function CustomTabPanel(props) {
  const { children, value, tabValue, ...other } = props;


  return (
    <div
      role="tabpanel"
      hidden={value !== tabValue}
      id={`incident-tabpanel-${tabValue}`}
      aria-labelledby={`incident-tab-${tabValue}`}
      {...other}
    >
      {value === tabValue && (
        <Box >

          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};
const TABS = {
  ACTION: "ACTION",
  ALL: "ALL",
  REVIEW: "REVIEW",
  INVESTIGATION: "INVESTIGATION",
  UNDER_INVESTIGATION: "UNDER_INVESTIGATION",

};
const Inspection = () => {
  const me = useSelector((state) => state.login.user)
  const [showFilter, setShowFilter] = useState(true)
  const [applyFilter, setApplyFilter] = useState(false)

  const [clear, setClear] = useState(true)

  const [dates, setDates] = useState([])
  const [filter, setFilter] = useState([])
  const [actionData, setActionData] = useState([]);
  const [rendered, setRendered] = useState(0)

  const [locationOneId, setlocationOneId] = useState('')
  const [locationTwoId, setlocationTwoId] = useState('')
  const [locationThreeId, setlocationThreeId] = useState('')
  const [locationFourId, setlocationFourId] = useState('')

  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)

  const [search, setSearch] = useState('')
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();
  const years = [currentYear, currentYear + 1];
  const [selectedYear, setSelectedYear] = useState(currentYear);

  const [inspection, setInspection] = useState([]);
  const [inspectionFilter, setInspectionFilter] = useState([])
  const [inspectionReport, setInspectionReport] = useState([]);
  const [inspectionReportFilter, setInspectionReportFilter] = useState([]);
  useEffect(() => {
    getActionData('Inspection', ['open', 'returned']);
  }, []); // Only run once, when the component mounts

  useEffect(() => {
    getInspection();
  }, []); // Only run once, when the component mounts
  const getActionData = async (applicationType, statusList) => {
    try {
      const response = await API.get(ACTION_URL);
      if (response.status === 200) {
        const filteredData = response.data.filter(action =>
          action.application === applicationType && statusList.includes(action.status)
        );
        setActionData(filteredData);
      }
    } catch (error) {
      console.error('Error fetching action data:', error);
    }
  };


  const getInspection = async () => {
    const params = {
      "include": [{ "relation": "assignedBy" }, { "relation": "checklist" }, { "relation": "assignedTo" }, { "relation": "approver" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }]

    };
    const response = await API.get(`${INSPECTION_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
    if (response.status === 200) {

      const reversedData = response.data.reverse(); // Reverse the array

     
      setInspection(reversedData);
      setInspectionFilter(reversedData);

      const filteredData1 = reversedData.filter(action =>
        action.status && action.status === "Done. (Open Actions)"
      );

      setInspectionReport(filteredData1);
      setInspectionReportFilter(filteredData1);

    }
  }
  const onApplyFilter = (type) => {
    setApplyFilter(type)
    setShowFilter(true)
  }

  const onCancelFilter = (type) => {
    setApplyFilter(false)
    setShowFilter(true)
    setClear(!clear)
  }
  // useEffect(() => {
  //   // This will update the checklist with inspection data
  //   if (inspection.length > 0) {
  //     updateChecklistWithInspection(inspection);
  //   }
  // }, [inspection]);

  const [value, setValue] = useState(TABS.ACTION);


  const handleChange = (event, newValue) => {
    setValue(newValue);
  };


  // const updateChecklistWithInspection = (inspectionData) => {
  //   setChecklist((currentChecklist) => {
  //     const newChecklist = currentChecklist.map((checklistItem) => {
  //       const updatedStatus = [...checklistItem.monthlyStatus];
  //       // Find the inspection data for this checklist item
  //       inspectionData.forEach((inspection) => {
  //         if (inspection.checklistId === checklistItem.id) {
  //           // Convert month string to index (Jan = 0, Feb = 1, etc.)
  //           const monthIndex = months.indexOf(inspection.month);
  //           if (monthIndex !== -1) {
  //             // Update the status for the corresponding month
  //             updatedStatus[monthIndex] = inspection.status;
  //           }
  //         }
  //       });
  //       return { ...checklistItem, monthlyStatus: updatedStatus };
  //     });

  //     return newChecklist;
  //   });
  // }






  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];



  // const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

  // };
  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {
    console.log('Filter applied:', { locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate });
    setDates([])
    const data = [
      locationOneId.name || '',
      locationTwoId.name || '',
      locationThreeId.name || '',
      locationFourId.name || '',

    ];

    if (startDate !== null && endDate !== null) {
      const date = [
        moment(startDate).format('MMM YYYY'),
        moment(endDate).format('MMM YYYY')
      ]
      setDates(date)
    }


    setFilter(data)
    setSearch('')

    setlocationOneId(locationOneId.id)
    setlocationTwoId(locationTwoId.id)
    setlocationThreeId(locationThreeId.id)
    setlocationFourId(locationFourId.id)
    setStartDate(startDate)
    setEndDate(endDate)
  };













  useEffect(() => {
    // Re-fetch action data if necessary when `rendered` changes
    if (rendered > 0) {
      getActionData('Inspection', ['open', 'returned']);
    }
  }, [rendered]);
  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'DD-MM-YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  useEffect(() => {
    // Run filtering when location or date range changes
    console.log('Applying filter based on location or date changes');
    const filterData1 = (data) => {
      return data.filter(item => {
        return (
          (!locationOneId || item.locationOneId === locationOneId) &&
          (!locationTwoId || item.locationTwoId === locationTwoId) &&
          (!locationThreeId || item.locationThreeId === locationThreeId) &&
          (!locationFourId || item.locationFourId === locationFourId) &&
          (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))
        );
      });
    };

    // Set filtered data
    setInspection(filterData1(inspectionFilter));
    setInspectionReport(filterData1(inspectionReportFilter));
  }, [locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate, inspectionFilter, inspectionReportFilter]);

  // const renderCell = (audit, monthIndex, monthName) => {
  //   const isPast = selectedYear < currentYear || (selectedYear === currentYear && monthIndex <= currentMonth);
  //   const status = audit.monthlyStatus[monthIndex];

  //   // Conditionally render content based on whether the month is in the past
  //   if (isPast) {
  //     return (
  //       <div className={`disabled-cell`}>
  //         {status ? <span className="status-cell">{status}</span> : <span>Past Month</span>}
  //       </div>
  //     );
  //   } else {
  //     return (
  //       <div
  //         className={`cursor-pointer`}
  //         onClick={() => handleCellClick(monthIndex, audit.id)}
  //       >
  //         {status ?
  //           <span className="initiated-cell">{status}</span> :
  //           <span>Initiate Audit</span>
  //         }
  //       </div>
  //     );
  //   }
  // };


  return (
    <>

      <CardOverlay title={''}>

        <AppSwitch value={{ label: 'Inspection', value: 'inspection' }} />

        <div className='row'>
          <div className='col-12 mb-4'>
            <div className='d-flex align-items-center'>
              <div className='col-1 d-flex'>
                {!applyFilter ?
                  <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                    <div className='d-flex flex-column align-items-end'>
                      <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                      <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                    </div>
                    <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                  </div>
                  :
                  <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                    <div className='d-flex flex-column align-items-end'>
                      <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                      <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                    </div>
                    <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                  </div>
                }

              </div>
              <div className='col-9 d-flex'>
                {applyFilter && <>
                  {filter.length !== 0 &&
                    <h5><b>Location : </b>{filter.map((location, index) => (
                      location !== '' &&
                      <React.Fragment key={index}>
                        <span className='loc-box'>{location}</span>
                        {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                      </React.Fragment>
                    ))}</h5>
                  }
                  {dates.length !== 0 &&
                    <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                      <React.Fragment key={index}>
                        <span className='loc-box'>{location}</span>
                        {index < dates.length - 1 && " To "}
                      </React.Fragment>
                    ))}</h5>
                  }
                </>}
              </div>
              <div className='col-2'>
                <div className="p-input-icon-left ">
                  <i className="fa fa-search" />
                  <InputText type="search" style={{ borderRadius: 8 }} placeholder='Search' onChange={(e) => { }} />
                </div>
              </div>
            </div>
          </div>

          <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>

            <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
          </div>


          <div className={!showFilter ? 'col-9' : 'col-12'}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>




              <Tabs value={value} onChange={handleChange} aria-label="incident report table">
                <Tab
                  label={
                    <Typography variant="body1" style={customFontStyle}>
                      My Actions <span className='headerCount'>{actionData.length}</span>
                    </Typography>
                  }
                  value={TABS.ACTION}
                />
                <Tab
                  label={
                    <Typography variant="body1" style={customFontStyle}>
                      Scheduling <span className='headerCount'>{inspection.length}</span>
                    </Typography>
                  }
                  value={TABS.INVESTIGATION}
                />
                <Tab
                  label={
                    <Typography variant="body1" style={customFontStyle}>
                      Reports <span className='headerCount'>{inspectionReport.length}</span>
                    </Typography>
                  }
                  value={TABS.REVIEW}
                />
              </Tabs>


            </Box>
            <CustomTabPanel value={value} tabValue={TABS.ACTION}>
              <Box sx={{ width: '100%' }}>
                <Actions action={actionData} applicationType="Inspection" setRendered={setRendered} />
              </Box>
            </CustomTabPanel>
            <CustomTabPanel value={value} tabValue={TABS.REVIEW}>
              <Reports inspection={inspectionReport} />

            </CustomTabPanel>

            <CustomTabPanel value={value} tabValue={TABS.INVESTIGATION}>
              <Dashboard data={inspection} setRendered={setRendered} />

            </CustomTabPanel>

            

          </div>
        </div>
      </CardOverlay >

    </>
  )

}

export default Inspection;